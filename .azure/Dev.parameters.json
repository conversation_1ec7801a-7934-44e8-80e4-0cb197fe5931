{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"location": {"value": "West US 2"}, "tags": {"value": "<PERSON>"}, "applicationInsightsName": {"value": "appi-assessments-dev-001"}, "Application_Type": {"value": "web"}, "Flow_Type": {"value": "Bluefield"}, "keyVaultName": {"value": "kv-assessments-dev-002"}, "skuName": {"value": "standard"}, "tenantId": {"value": "ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab"}, "storageName": {"value": "stassessmentsdev002"}, "storageSkuName": {"value": "Standard_LRS"}, "appName": {"value": "api-assessments-dev-001"}, "rgEnvironment": {"value": "Development"}, "appServicePlanName": {"value": "plan-assessments-dev-001"}, "sku": {"value": "S1"}}}
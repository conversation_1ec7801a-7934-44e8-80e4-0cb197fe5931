﻿namespace AACN.API.Service
{
    using AACN.API.Model;
    using Microsoft.AspNetCore.Mvc;
    using Newtonsoft.Json.Linq;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;
    using AACN.Services;
    using static AACN.API.Model.FormattedWordsModel;
    using static System.Net.Mime.MediaTypeNames;
    using System.Net;
    using static AACN.API.Model.InstanceData;
    using AACN.API.Controllers;
    using Microsoft.Extensions.Logging;
    using Microsoft.AspNetCore.Http;
    using Microsoft.VisualBasic.CompilerServices;
    using System.Text;
    using System.Net.Http.Headers;
    using System.Collections;
    using static AACN.API.Model.SurveyResponse;

    public class HweatService : BaseService
    {
        private Random random = new Random();

        public CEActivityResponseModel GetEvaluationData(string instanceid, string assessmentid, string providerid)
        {
            bool recordStatus = false;
            List<tempSpeakerData> speakerData = new List<tempSpeakerData>(); //Initialize Empty List Of Speakers 
            DynamicsJsonValue dynamicsJsonValue = new DynamicsJsonValue();
            CEActivityResponseModel ceActivityResponseModel = new CEActivityResponseModel();
            //EventSessionResponseData eventSessionResponseData = check_EventinD365(instanceid);
            AssessmentModel assessmentModel = checkAssessmentByCode(assessmentid);

            //Create a New Event or use exsiting once
            if (assessmentModel != null && assessmentModel.value.Count > 0 && !string.IsNullOrEmpty(assessmentModel.value[0].AssessmentId))
            {
                recordStatus = true;
                //Event Available or new Created !
                if (recordStatus == true)
                {
                    string query = string.Format(Utility.getInstanceResponse, instanceid);
                    HttpResponseMessage answerResponse = GetRecords(query);
                    if (answerResponse.IsSuccessStatusCode == true)
                    {
                        if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                        {
                            #region wordFormatting
                            List<formattedWordsList> formattedWords = new List<formattedWordsList>();
                            formattedWords = getSpecialWords();
                            if (formattedWords != null && formattedWords.Count > 0)
                            {
                                foreach (var item in formattedWords)
                                {
                                    switch (item.aacn_format_type)
                                    {
                                        case *********:    //Bold
                                            item.updatedWord = "<b>" + item.aacn_special_word + "</b>";
                                            break;
                                        case *********:     //Italic
                                            item.updatedWord = "<i>" + item.aacn_special_word + "</i>";
                                            break;
                                        case *********:    //Underline
                                            item.updatedWord = "<u>" + item.aacn_special_word + "</u>";
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }

                            #endregion
                            dynamicsJsonValue = JsonConvert.DeserializeObject<DynamicsJsonValue>(answerResponse.Content.ReadAsStringAsync().Result);
                            string jsonResponse = Convert.ToString(dynamicsJsonValue.jsonResponse);
                            dynamicsJsonValue.jsonResponse = null;

                            foreach (var item in formattedWords)
                            {
                                jsonResponse = Regex.Replace(jsonResponse, $@"\b{item.aacn_special_word}\b", item.updatedWord);
                            }

                            Event_SessionResponse event_SessionResponse = new Event_SessionResponse();
                            List<InstanceDataModel> eventResponse = JsonConvert.DeserializeObject<List<InstanceDataModel>>(jsonResponse);
                            if (eventResponse.Count > 0)
                            {
                                //ceActivityResponseModel.eventResponse = eventResponse;
                                event_SessionResponse.RecordType = "event";
                                event_SessionResponse.Name = eventResponse[0].aacn_instance_id;
                                event_SessionResponse.RecordId = eventResponse[0].aacn_instanceid;
                                event_SessionResponse.Record_Code = eventResponse[0].aacn_instance_id;
                                event_SessionResponse.aacn_assessment_code = eventResponse[0].aacn_assessment_code;
                                event_SessionResponse.aacn_start_date = eventResponse[0].aacn_start_date;
                            }
                            if (event_SessionResponse != null && event_SessionResponse.RecordId != null)
                            {
                                ceActivityResponseModel.responseData = new List<Event_SessionResponse>();
                                ceActivityResponseModel.responseData.Add(event_SessionResponse);
                            }
                        }
                    }

                    #region sorting
                    List<assessmentHeaderLineRecords> sortedList = new List<assessmentHeaderLineRecords>();
                    //For Sorting Purpose
                    if (ceActivityResponseModel.responseData != null && ceActivityResponseModel.responseData.Any())

                    {
                        if (ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines != null)
                        {
                            sortedList = ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines.OrderBy(x => string.IsNullOrEmpty(x.assessment_Line_Order) == true ? int.MaxValue.ToString() : x.assessment_Line_Order)
                            .ToList();
                            if (sortedList != null)
                            {
                                ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines = sortedList;
                            }

                        }
                        var assessmentLines = ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines;
                        if (assessmentLines != null && assessmentLines.Any())
                        {
                            foreach (var assessmentLine in assessmentLines)
                            {
                                if (assessmentLine.assessment_Line_Order != null)
                                {   //First Level Sorting - Section(By Order)
                                    if (assessmentLine.aacn_banner_description != null && assessmentLine.aacn_banner_description.Contains("<b>Demographics</b>"))
                                    {
                                        assessmentLine.referenceLines = SortLines(assessmentLine.referenceLines, "");
                                    }
                                    else
                                    {
                                        assessmentLine.referenceLines = SortLines(assessmentLine.referenceLines, "random");

                                    }
                                    if (assessmentLine.aacn_assessment_line_orientation != null && assessmentLine.aacn_assessment_line_orientation == "*********") //Vertical
                                    {
                                        assessmentLine.options = SortOptions(assessmentLine.options, "");
                                    }
                                    else
                                    {
                                        assessmentLine.options = SortOptions(assessmentLine.options, "LineOrder");
                                    }

                                    //Second Level Sorting - Question/Group (By Order)
                                    foreach (var referenceLine in assessmentLine.referenceLines)
                                    { //Third Level Sorting for Questions/Options.
                                        referenceLine.childLines = SortLines(referenceLine.childLines, "random");

                                        foreach (var childOptions in referenceLine.childLines)
                                        {
                                            if (childOptions.aacn_assessment_line_orientation == "*********") //Vertical
                                            {
                                                childOptions.optionLines = SortOptions(childOptions.optionLines, "");
                                            }
                                            else
                                            {
                                                childOptions.optionLines = SortOptions(childOptions.optionLines, "LineOrder");
                                            }
                                        }
                                        if (referenceLine.aacn_assessment_line_orientation == "*********") //Vertical
                                        {
                                            referenceLine.optionLines = SortOptions(referenceLine.optionLines, "");
                                        }
                                        else
                                        {
                                            referenceLine.optionLines = SortOptions(referenceLine.optionLines, "LineOrder");
                                        }
                                    }
                                }
                            }
                            ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines = assessmentLines;
                        }
                    }


                    // Sorting method for lines
                    List<referenceLines> SortLines(List<referenceLines> lines, string sortType)
                    {

                        if (lines == null)
                            return new List<referenceLines>();
                        if (sortType == "random")
                        {
                            return lines.OrderBy(x => string.IsNullOrEmpty(x.assessment_Line_Order) == true ? int.MaxValue.ToString() : random.Next().ToString())
                                         .ToList();
                        }
                        else
                        {
                            return lines.OrderBy(x => string.IsNullOrEmpty(x.assessment_Line_Order) == true ? int.MaxValue.ToString() : x.assessment_Line_Order)
                                             .ToList();
                        }
                    }
                    // Sorting method for options
                    List<assessmentLineOptionRecords> SortOptions(List<assessmentLineOptionRecords> options, string sortType)
                    {
                        if (options != null)
                        {
                            #region BornYearLogic
                            var filteredList = options.Where(item => item.aacn_assessment_line_question_option_text == "[DropDown]").ToList();
                            if (filteredList.Count > 0)
                            {
                                JObject jsonValue = new JObject();
                                jsonValue.Add("Start-Year", 1930);
                                jsonValue.Add("End-Year", 2008);
                                jsonValue.Add("Type", "YearDropDown");

                                //Replace the json now
                                foreach (var item in filteredList)
                                {
                                    item.aacn_assessment_line_question_option_text = jsonValue.ToString();
                                }

                                // Update the original list with the changes made to the filtered list
                                foreach (var item in filteredList)
                                {
                                    var originalItem = options.FirstOrDefault(i => i.aacn_assessment_line_optionid == item.aacn_assessment_line_optionid);
                                    if (originalItem != null)
                                    {
                                        //Update properties of the original items
                                        originalItem.aacn_assessment_line_question_option_text = item.aacn_assessment_line_question_option_text;
                                        originalItem.type = "DropDown";
                                    }
                                }
                            }

                            #endregion
                        }
                        if (options == null)
                        {
                            return new List<assessmentLineOptionRecords>();
                        }

                        if (sortType == "LineOrder")
                        {
                            return options.OrderBy(x =>
                                (x.aacn_assessment_line_question_option_text.ToLower() != "Other (please specify)".ToLower() && x.aacn_assessment_line_question_option_text != "Prefer not to disclose") ?
                                    (string.IsNullOrEmpty(x.aacn_assessment_line_option_sort_order) ? int.MaxValue : Convert.ToInt32(x.aacn_assessment_line_option_sort_order)) :
                                    int.MaxValue)
                                          .ToList();
                        }
                        else
                        {
                            return options.OrderBy(x =>
                                (x.aacn_assessment_line_question_option_text.ToLower() != "Other (please specify)".ToLower() && x.aacn_assessment_line_question_option_text != "Prefer not to disclose") ?
                                    (string.IsNullOrEmpty(x.aacn_assessment_line_question_option_text) ? "Z" : x.aacn_assessment_line_question_option_text) :
                                    "Z")
                                          .ToList();
                        }
                    }
                    #endregion
                }
            }

            return ceActivityResponseModel;

        }


        public string checkSession_EventByName(string Name)
        {
            string result = "";
            EventSessionResponseData eventSessionResponseData = new EventSessionResponseData();

            string query = string.Format(Utility.getEventsData, Name);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    eventSessionResponseData = JsonConvert.DeserializeObject<EventSessionResponseData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    if (eventSessionResponseData.value != null && eventSessionResponseData.value.Count > 0)
                    {
                        result = "event";
                    }
                    else
                    {
                        string query2 = string.Format(Utility.getSessionData, Name);
                        HttpResponseMessage sessionResponse = GetRecords(query2);
                        if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                        {
                            eventSessionResponseData = JsonConvert.DeserializeObject<EventSessionResponseData>(Utility.RemoveJsonNulls(sessionResponse.Content.ReadAsStringAsync().Result));
                            if (eventSessionResponseData.value != null && eventSessionResponseData.value.Count > 0)
                            {
                                result = "session";
                            }
                        }

                    }
                }
                else
                {

                }


            }
            return result;

        }


        public EventSessionResponseData check_EventinD365(string Name)
        {
            EventSessionResponseData eventSessionResponseData = new EventSessionResponseData();
            try
            {
                string query = string.Format(Utility.getEventsData, Name);
                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        eventSessionResponseData = JsonConvert.DeserializeObject<EventSessionResponseData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                        if (eventSessionResponseData.value != null && eventSessionResponseData.value.Count > 0)
                        {

                        }
                    }
                    else
                    {

                    }


                }
            }
            catch (Exception ex)
            {
                return eventSessionResponseData;
            }
            return eventSessionResponseData;

        }

        public sessionResponseData checkSessionidd365(string Name)
        {
            EventSessionResponseData eventSessionResponseData = new EventSessionResponseData();
            sessionResponseData _sessionResponseData = new sessionResponseData();
            try
            {
                string query = string.Format(Utility.getSessionData, Name);
                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        DynamicsJsonValue dynamicsJsonValue = JsonConvert.DeserializeObject<DynamicsJsonValue>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                        string jsonResponse = Convert.ToString(dynamicsJsonValue.jsonResponse);
                        if (dynamicsJsonValue.jsonResponse != null)
                        {
                            _sessionResponseData.value = JsonConvert.DeserializeObject<List<sessionResponse>>(jsonResponse);
                        }
                    }
                    else
                    {

                    }


                }
            }
            catch (Exception ex)
            {
                return _sessionResponseData;
            }
            return _sessionResponseData;

        }
        public List<contactRecords> getContactData(string userId)
        {

            ContactResponseData _contactRecord = new ContactResponseData();
            string query = string.Format(Utility.getContactRecords, userId);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    _contactRecord = JsonConvert.DeserializeObject<ContactResponseData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));

                }
                else
                {

                }


            }
            return _contactRecord.value;
        }

        public List<formattedWordsList> getSpecialWords()
        {

            FormattedWordsModel _response = new FormattedWordsModel();
            string query = string.Format(Utility.getSpecialCharacters);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    _response = JsonConvert.DeserializeObject<FormattedWordsModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));

                }
                else
                {

                }


            }
            return _response.formattedWords;

        }

        static string ReplaceWholeWord(string originalString, string wordToReplace, string replacement)
        {
            string[] words = originalString.Split(' ');
            for (int i = 0; i < words.Length; i++)
            {
                if (words[i] == wordToReplace)
                {
                    words[i] = replacement;
                }
            }
            return string.Join(" ", words);
        }

        public AssessmentModel checkAssessmentByCode(string assessmentCode)
        {
            AssessmentModel assessmentModel = new AssessmentModel();
            try
            {
                string query = string.Format(Utility.getAssessmentByCode, assessmentCode);
                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        assessmentModel = JsonConvert.DeserializeObject<AssessmentModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                        if (assessmentModel.value != null && assessmentModel.value.Count > 0)
                        {

                        }
                    }
                    else
                    {

                    }


                }
            }
            catch (Exception ex)
            {
                return assessmentModel;
            }
            return assessmentModel;

        }

        public ResponseCounterData checkResponseCounterbyDate(string sessionId)
        {
            ResponseCounterData responseCounterData = new ResponseCounterData();
            ResponseCounterModel responseCounterModel = new ResponseCounterModel();
            try
            {
                string recordID = "";
                sessionResponseData _sessionResponse = checkSessionidd365(sessionId);
                if (!string.IsNullOrEmpty(_sessionResponse.value[0].aacn_sessionid) && _sessionResponse.value != null && _sessionResponse != null)
                {
                    recordID = _sessionResponse.value[0].aacn_sessionid;

                    string query = string.Format(Utility.getResponseCounterbySesssion, recordID);
                    HttpResponseMessage answerResponse = GetRecords(query);
                    if (answerResponse.IsSuccessStatusCode == true)
                    {
                        if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                        {
                            responseCounterModel = JsonConvert.DeserializeObject<ResponseCounterModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                            if (responseCounterModel.value.Count > 0)
                            {
                                responseCounterData.submittedResponses = responseCounterModel.value.Count.ToString();

                            }
                            else
                            {
                                responseCounterData.submittedResponses = "0";
                            }
                        }
                        else
                        {
                            responseCounterData.submittedResponses = "0";
                        }


                    }
                }
            }
            catch (Exception ex)
            {

            }
            return responseCounterData;
        }


        public HttpResponseMessage createInstanceData(PostInstanceData postInstanceData)
        {
            HttpResponseMessage answerResponse = null;
            try
            {
                string odataQuery = "aacn_instances";
                postInstanceData.status = (int)Utility.instanceStatus.INPROGRESS;
                answerResponse = CreateRecord(odataQuery, JsonConvert.SerializeObject(postInstanceData));
                if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                {
                    string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                    string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                    answerResponse.StatusCode = System.Net.HttpStatusCode.NoContent;
                }
                else
                {
                    answerResponse.Content = new StringContent(answerResponse.Content.ReadAsStringAsync().Result);
                    answerResponse.StatusCode = System.Net.HttpStatusCode.BadRequest;
                }

                return answerResponse;
            }
            catch (Exception ex)
            {
                answerResponse.Content = new StringContent(ex.Message);
                return null;
            }
        }

        public HttpResponseMessage UpdateInstanceData(PostInstanceData postInstanceData, ILogger<hweatactivitiesController> _logger)
        {
            HttpResponseMessage answerResponse = null;
            try
            {

                var odataQuery = $"aacn_instances({postInstanceData.instanceId})";
                _logger.LogInformation("Update Payload (1) ~ " + JsonConvert.SerializeObject(postInstanceData));
                if (postInstanceData.status == 1)
                {
                    postInstanceData.status = (int)Utility.instanceStatus.COMPLETED;
                }
                else if (postInstanceData.status == 0)
                {
                    postInstanceData.status = (int)Utility.instanceStatus.INPROGRESS;
                }

                _logger.LogInformation("Update Payload (2) ~ " + JsonConvert.SerializeObject(postInstanceData));
                answerResponse = UpdateRequest(odataQuery, JsonConvert.SerializeObject(postInstanceData));
                _logger.LogInformation("Response Status ~" + answerResponse.StatusCode);
                if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                {
                    var _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                    string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                    answerResponse.StatusCode = System.Net.HttpStatusCode.NoContent;
                }
                else
                {
                    answerResponse.Content = new StringContent(answerResponse.Content.ReadAsStringAsync().Result);
                    answerResponse.StatusCode = System.Net.HttpStatusCode.BadRequest;
                }

                return answerResponse;
            }
            catch (Exception ex)
            {
                answerResponse.Content = new StringContent(ex.Message);
                return null;
            }
        }

        public APIResponseNF_D365 createResponseData(PostResponseData responeLine, ILogger<hweatactivitiesController> _logger)
        {
            _logger.LogInformation("HWEAT - CreateResponse Started");
            APIResponseNF_D365 apiResponseNF_D365 = new APIResponseNF_D365();
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            try
            {
                string odataQuery = "aacn_responses";
                if (!string.IsNullOrEmpty(responeLine.survey_Provider_Name))
                {
                    string survey_Provider_ID = getSurveryProviderByName(responeLine.survey_Provider_Name);
                    if (!string.IsNullOrEmpty(survey_Provider_ID))
                    {
                        responeLine.survey_Provider_Name = $"/aacn_survey_providers({survey_Provider_ID})";
                    }
                    else
                    {
                        apiResponseNF_D365.StatusCode_D365 = (int)System.Net.HttpStatusCode.BadRequest;
                        apiResponseNF_D365.D365_status = "Survey Provider does not exists with name -" + responeLine.survey_Provider_Name;
                        return apiResponseNF_D365;
                    }
                }
                _logger.LogInformation("HWEAT - CreateResponse D365 Json ~ " + JsonConvert.SerializeObject(responeLine));
                answerResponse = CreateRecordwithRetryHweat(odataQuery, JsonConvert.SerializeObject(responeLine), _logger);
                _logger.LogInformation("HWEAT - CreateResponse D365 Response ~ " + answerResponse.StatusCode.ToString());

                if (answerResponse.StatusCode == HttpStatusCode.NoContent)  //204
                {
                    string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                    string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                    answerResponse.StatusCode = System.Net.HttpStatusCode.NoContent;
                    apiResponseNF_D365.RecordId = new Guid(splitRetrievedData[1]);
                    _logger.LogInformation("D365 Response Created with Id" + splitRetrievedData[1].ToString());
                    apiResponseNF_D365.StatusCode_D365 = (int)System.Net.HttpStatusCode.NoContent;
                    apiResponseNF_D365.D365_status = "Created";

                    HttpResponseMessage responseData = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest };
                    responseData = sendResponsetoHweat(apiResponseNF_D365.RecordId.ToString(), responeLine, _logger);
                    if (responseData.StatusCode == HttpStatusCode.NoContent)
                    {
                        apiResponseNF_D365.StatusCode_NF = (int)responseData.StatusCode;
                        apiResponseNF_D365.NF_Status = "Created";
                    }
                    else
                    {
                        apiResponseNF_D365.StatusCode_NF = (int)System.Net.HttpStatusCode.BadRequest;
                        apiResponseNF_D365.NF_Status = responseData.Content.ReadAsStringAsync().Result.ToString();
                        UpdateResponseStatustoD365(splitRetrievedData[1], responseData.Content.ReadAsStringAsync().Result.ToString() + responseData.StatusCode.ToString(), _logger);
                    }

                }
                else
                {
                    apiResponseNF_D365.D365_status = answerResponse.Content.ReadAsStringAsync().Result.ToString();
                    apiResponseNF_D365.StatusCode_D365 = (int)System.Net.HttpStatusCode.BadRequest;
                    apiResponseNF_D365.StatusCode_NF = (int)System.Net.HttpStatusCode.BadRequest;
                    apiResponseNF_D365.NF_Status = null;
                }
                return apiResponseNF_D365;
            }
            catch (Exception ex)
            {
                string resultId = apiResponseNF_D365.RecordId == null ? string.Empty : apiResponseNF_D365.RecordId.ToString();
                UpdateResponseStatustoD365(resultId, ex.Message, _logger);
                _logger.LogInformation("Exception ~" + ex.Message);
                apiResponseNF_D365.D365_status = ex.Message;
                return apiResponseNF_D365;
            }
        }

        public ResponseDataModel getResponsesByInstanceId(string instanceId)
        {
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            ResponseDataModel responseDataModel = new ResponseDataModel();
            try
            {
                InstanceData instanceData = getInstanceData(instanceId);
                if (instanceData != null && instanceData.instancerecord.Count > 0 && !string.IsNullOrEmpty(instanceData.instancerecord[0].Assessment_Code))
                {
                    responseDataModel = getResponseDataByAssessmentCode(instanceData.instancerecord[0].aacn_instanceid);
                    if (responseDataModel != null)
                    {
                        responseDataModel.instance = instanceData;

                    }
                }
            }
            catch (Exception ex)
            {

            }

            return responseDataModel;
        }

        public ResponseDataModel getResponseDataByAssessmentCode(string assessmentCodeId)
        {
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            ResponseDataModel responseModel = new ResponseDataModel();
            try
            {
                string query = string.Format(Utility.getResponseDataByAssessmentCode, assessmentCodeId);
                HttpResponseMessage answerResponseData = GetRecords(query);
                if (answerResponseData.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponseData.Content.ReadAsStringAsync().Result))
                    {
                        responseModel = JsonConvert.DeserializeObject<ResponseDataModel>(Utility.RemoveJsonNulls(answerResponseData.Content.ReadAsStringAsync().Result));
                    }
                    else
                    {

                    }
                }
            }
            catch (Exception ex)
            {

            }

            return responseModel;
        }

        public InstanceData getInstanceData(string instanceId)
        {

            InstanceData instaceData = new InstanceData();
            string query = string.Format(Utility.getInstanceDataById, instanceId);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    instaceData = JsonConvert.DeserializeObject<InstanceData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));

                }
                else
                {

                }


            }
            return instaceData;
        }

        public ResponseCounterData checkResponseCounterbyInstanceId(string instanceId)
        {
            ResponseCounterData responseCounterData = new ResponseCounterData();
            ResponseDataModel responseDataModel = new ResponseDataModel();
            ResponseCounterModel responseCounterModel = new ResponseCounterModel();
            try
            {
                InstanceData instanceData = getInstanceData(instanceId);
                if (instanceData != null && instanceData.instancerecord.Count > 0 && !string.IsNullOrEmpty(instanceData.instancerecord[0].Assessment_Code))
                {
                    responseDataModel = getResponseDataByAssessmentCode(instanceData.instancerecord[0].aacn_instanceid);
                    responseCounterData.startDate = (instanceData.instancerecord[0].aacn_start_date).ToString();
                    responseCounterData.endDate = (instanceData.instancerecord[0].aacn_end_date).ToString();
                    if (responseDataModel.responseValue != null && responseDataModel.responseValue.Count > 0)
                    {
                        responseCounterData.submittedResponses = responseDataModel.responseValue.Count.ToString();
                    }
                    else
                    {
                        responseCounterData.submittedResponses = "0";
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return responseCounterData;
        }

        public ResponseDataRoot getResponsesById(string ResponseId)
        {
            ResponseDataRoot responseDataRoot = new ResponseDataRoot();
            string query = string.Format(Utility.getresponseDataById, ResponseId);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    responseDataRoot = JsonConvert.DeserializeObject<ResponseDataRoot>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
                else
                {

                }
            }
            return responseDataRoot;
        }

        public List<Instances> checkinstanceById(string UnitId, string UserId)
        {
            List<Instances> _instanceData = new List<Instances>();
            InstanceResponseData instanceResponseData = new InstanceResponseData();
            try
            {
                string query = string.Format(Utility.getsurveyInstances, UnitId);
                if (!string.IsNullOrEmpty(UserId))
                {
                    query = string.Format(Utility.getsurveyInstancesbyUserId, UnitId, UserId);
                }
                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        instanceResponseData = JsonConvert.DeserializeObject<InstanceResponseData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                        _instanceData = instanceResponseData.value;
                        foreach (var item in _instanceData)
                        {
                            ResponseDataModel responseDataModel = getResponseDataByAssessmentCode(item.assessment_Code_Id);
                            if (responseDataModel != null && responseDataModel.responseValue.Count != 0)
                            {
                                item.submittedResponses = responseDataModel.responseValue.Count.ToString();
                            }
                            else
                            {
                                item.submittedResponses = "0";
                            }

                        }
                    }
                    else
                    {

                    }


                }
            }
            catch (Exception ex)
            {
                return _instanceData;
            }
            return _instanceData;

        }

        public HttpResponseMessage UpdateResponseStatustoD365(string ResponseId, string Error, ILogger<hweatactivitiesController> _logger)
        {
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            UpdateResponseStatus updateResponse = new UpdateResponseStatus();
            try
            {
                _logger.LogInformation("Update Started for Response ID " + ResponseId);
                if (ResponseId != null)
                {
                    updateResponse.responseStatus = false;
                    updateResponse.ErrorMessage = Error;
                    string odataQuery = "aacn_responses(" + ResponseId + ")";
                    answerResponse = UpdateRequest(odataQuery, JsonConvert.SerializeObject(updateResponse));
                    _logger.LogInformation("Response Status ~ " + answerResponse.StatusCode);
                    if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                    {
                        string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                        string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                        answerResponse.StatusCode = System.Net.HttpStatusCode.NoContent; ;
                    }
                    else
                    {
                        answerResponse.Content = new StringContent(answerResponse.Content.ReadAsStringAsync().Result);
                        answerResponse.StatusCode = System.Net.HttpStatusCode.BadRequest;
                    }
                }


                return answerResponse;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Exception ~ " + ex.Message);
                answerResponse.Content = new StringContent(ex.Message);
                return null;
            }
        }

        public bool InstanceValidation(string instanceId, ILogger<hweatactivitiesController> _logger)
        {
            bool status = true;

            try
            {
                List<Instances> _instanceData = checkinstanceByIdNew(instanceId);
                if (_instanceData != null && _instanceData.Count > 0)
                {
                    DateTime StartDate = (DateTime)(_instanceData[0].startDate);
                    DateTime endDate = (DateTime)(_instanceData[0].endDate);
                    string Status = _instanceData[0].status;

                    if (endDate < System.DateTime.UtcNow.Date || Status.ToLower() == "completed")
                    {
                        status = false;
                    }
                }
            }

            catch (Exception ex)
            {
                _logger.LogInformation("Exception ~ " + ex.Message);
                //  answerResponse.Content = new StringContent(ex.Message);

            }

            return status;
        }

        public List<Instances> checkinstanceByIdNew(string instanceID)
        {
            List<Instances> _instanceData = new List<Instances>();
            InstanceResponseData instanceResponseData = new InstanceResponseData();
            try
            {
                string query = string.Format(Utility.getInstanceDataByIdnew, instanceID);
                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        instanceResponseData = JsonConvert.DeserializeObject<InstanceResponseData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                        _instanceData = instanceResponseData.value;
                    }
                    else
                    {

                    }


                }
            }
            catch (Exception ex)
            {
                return _instanceData;
            }
            return _instanceData;

        }

        public HttpResponseMessage sendResponsetoHweat(string responseId, PostResponseData postResponseData, ILogger<hweatactivitiesController> _logger)
        {
            //return new HttpResponseMessage { StatusCode = HttpStatusCode.NoContent };

            var returnresponse = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest };
            ResponseDataRoot responseDataRoot = getResponsesById(responseId);
            if (responseDataRoot != null && responseDataRoot.value != null && responseDataRoot.value.Count > 0)
            {
                SurveyResponse surveyresponse = new SurveyResponse();
                surveyresponse.ResponseId = new Guid(responseId);
                surveyresponse.RandomizeQuestionSequence = postResponseData.hweatData.RandomizeSeq == null ? string.Empty : postResponseData.hweatData.RandomizeSeq;
                surveyresponse.SubmissionDate = DateTime.UtcNow;
                surveyresponse.SurveyInstanceToken = responseDataRoot.value[0].instanceNumber;

                List<SurveyResponse.Response> responseList = new List<SurveyResponse.Response>();
                if (responseDataRoot.value[0].responseLinesData != null && responseDataRoot.value[0].responseLinesData.Count > 0)
                {
                    foreach (var item in responseDataRoot.value[0].responseLinesData)
                    {
                        SurveyResponse.Response response = new SurveyResponse.Response();
                        response.ResponseLineId = new Guid(item.aacn_response_lineid);
                        response.AssessmentLineId = string.IsNullOrEmpty(item._aacn_assesment_line_value) == true ? null : item._aacn_assesment_line_value.ToString();
                        response.AssessmentLineOptionId = string.IsNullOrEmpty(item._aacn_assessment_line_option_value) == true ? null : item._aacn_assessment_line_option_value.ToString();
                        response.AnswerText = string.IsNullOrEmpty(item.aacn_response_line_text_area) == true ? null : item.aacn_response_line_text_area;
                        responseList.Add(response);
                    }
                    surveyresponse.Responses = responseList;
                    _logger.LogInformation("HWEAT Request Model -" + JsonConvert.SerializeObject(surveyresponse));
                    HttpClient httpClient = new HttpClient();
                    try
                    {
                        Task<string> task = AccessTokenGenerator_Hweat();
                        string BearerToken = task.Result;
                        string apiURL = SurveyResponse_Endpoint;
                        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", BearerToken);
                        var content = new StringContent(JsonConvert.SerializeObject(surveyresponse), Encoding.UTF8, "application/json");
                        returnresponse = httpClient.PostAsync(apiURL, content).GetAwaiter().GetResult();

                    }
                    catch (Exception ex)
                    {
                        return new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest };
                    }

                }
            }
            _logger.LogInformation("Nf Response Value -" + returnresponse.StatusCode);
            return returnresponse;
        }



    }
}


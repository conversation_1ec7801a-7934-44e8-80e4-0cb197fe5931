﻿namespace AACN.Services
{
    using AACN.API.Model;
    using AACN.API.Service;
    using Microsoft.Extensions.Logging;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Reflection;
    using System.Text;
    using System.Threading.Tasks;
    using AACN.API.Helper;
    using AACN.API.Controllers;
    using System.Data.SqlClient;

    public class BaseService : Connection
    {
        bool IsTokenValid = false;

        //Sync Methods
        public HttpResponseMessage GetRecords(string query)
        {
            HttpResponseMessage response;
            try
            {
                Task<string> task = AccessTokenGenerator();
                CrmBearerToken = task.Result;
                string crmRestQuery = CrmApiUrl + query;

                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Get, crmRestQuery);
                request.Headers.Add("Prefer", "odata.include-annotations=\"*\"");//for formatted values
                request.Headers.Add("Authorization", "Bearer " + CrmBearerToken);
                HttpClient httpClient = new HttpClient();
                //send request
                response = httpClient.SendAsync(request).Result;

                TokenValidate(response);

                if (CrmBearerToken == null)
                {
                    GetRecords(query);
                }

                string responseString = response.Content.ReadAsStringAsync().Result;
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return response;
        }
        public HttpResponseMessage CreateRecord(string query, object entity)
        {
            HttpResponseMessage response = null;

            try
            {
                Task<string> task = AccessTokenGenerator();
                CrmBearerToken = task.Result;

                HttpClient httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromMinutes(3);
                string crmRestQuery = CrmApiUrl + query;
                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, crmRestQuery);
                //add header parameters
                request.Headers.Add("Authorization", "Bearer " + CrmBearerToken);
                request.Content = new StringContent(entity.ToString(), Encoding.UTF8, "application/json");
                request.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json; charset=utf-8");
                response = httpClient.SendAsync(request).Result;

                TokenValidate(response);
                if (CrmBearerToken == null)
                {
                    CreateRecord(query, entity);
                }
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return response;
        }
        public HttpResponseMessage CreateRecordwithRetry<T>(string query, object entity, ILogger<T> logger)
        {
            HttpResponseMessage response = null;
            int maxRetryAttempts = 4; // 1 initial attempt + 3 retry attempts
            int delayBetweenRetriesMilliseconds = 2000;

            for (int attempt = 1; attempt <= maxRetryAttempts; attempt++)
            {
                try
                {
                    Task<string> task = AccessTokenGenerator();
                    CrmBearerToken = task.Result;

                    string crmRestQuery = CrmApiUrl + query;

                    HttpClient client = new HttpClient();

                    client.Timeout = TimeSpan.FromMinutes(3); // Set timeout to 3 minutes
                    HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, crmRestQuery);
                    // Add header parameters
                    request.Headers.Add("Authorization", "Bearer " + CrmBearerToken);
                    request.Content = new StringContent(entity.ToString(), Encoding.UTF8, "application/json");
                    request.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json; charset=utf-8");

                    response = client.SendAsync(request).Result;
                    // Validate the token
                    TokenValidate(response);
                    if (CrmBearerToken == null)
                    {
                        // Retry immediately if the token is invalid
                        continue;
                    }

                    // If response is successful, return it
                    if (response.IsSuccessStatusCode)
                    {
                        return response;
                    }
                    else if ((int)response.StatusCode == 499) // Assuming 499 is the status code for cancellation
                    {
                        // Log and retry
                        logger.LogInformation("Request was canceled. Retrying...");
                    }
                    else
                    {
                        // Log and break the loop if it's a non-retryable error
                        logger.LogInformation($"Request failed with status code: {(int)response.StatusCode}");
                        break;
                    }
                }
                catch (HttpRequestException ex)
                {
                    logger.LogInformation($"Request exception: {ex.Message}. Retrying...");
                }
                catch (WebException ex)
                {
                    logger.LogInformation($"Web exception: {ex.Message}. Retrying...");
                }
                catch (Exception ex)
                {
                    logger.LogInformation($"Exception: {ex.Message}");
                    throw;
                }

                // Wait before retrying if this is the first attempt
                if (attempt == 1)
                {
                    Task.Delay(delayBetweenRetriesMilliseconds).Wait();
                }
            }

            return response;
        }


        public HttpResponseMessage CreateRecordwithRetryHweat(string query, object entity, ILogger<hweatactivitiesController> logger)
        {
            HttpResponseMessage response = null;
            int maxRetryAttempts = 4; // 1 initial attempt + 1 retry attempt
            int delayBetweenRetriesMilliseconds = 2000;

            for (int attempt = 1; attempt <= maxRetryAttempts; attempt++)
            {
                try
                {
                    Task<string> task = AccessTokenGenerator();
                    CrmBearerToken = task.Result;

                    string crmRestQuery = CrmApiUrl + query;

                    HttpClient client = new HttpClient();

                    client.Timeout = TimeSpan.FromMinutes(3); // Set timeout to 3 minutes
                    HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, crmRestQuery);
                    // Add header parameters
                    request.Headers.Add("Authorization", "Bearer " + CrmBearerToken);
                    request.Content = new StringContent(entity.ToString(), Encoding.UTF8, "application/json");
                    request.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json; charset=utf-8");

                    response = client.SendAsync(request).Result;
                    // Validate the token
                    TokenValidate(response);

                    if (CrmBearerToken == null)
                    {
                        //Retry immediately if the token is invalid
                        continue;
                    }

                    // If response is successful, return it
                    if (response.IsSuccessStatusCode)
                    {
                        return response;
                    }
                    else if ((int)response.StatusCode == 499) // Assuming 499 is the status code for cancellation
                    {
                        // Log and retry
                        logger.LogInformation("Request was canceled. Retrying...");
                    }
                    else
                    {
                        // Log and break the loop if it's a non-retryable error
                        logger.LogInformation($"Request failed with status code: {(int)response.StatusCode}");
                        break;
                    }
                }
                catch (HttpRequestException ex)
                {
                    logger.LogInformation($"Request exception: {ex.Message}. Retrying...");
                }
                catch (WebException ex)
                {
                    logger.LogInformation($"Web exception: {ex.Message}. Retrying...");
                }
                catch (Exception ex)
                {
                    logger.LogInformation($"Exception: {ex.Message}");
                    throw;
                }

                // Wait before retrying if this is the first attempt
                if (attempt == 1)
                {
                    Task.Delay(delayBetweenRetriesMilliseconds).Wait();
                }
            }

            return response;
        }
        public HttpResponseMessage UpdateRequest(string query, object entity)
        {
            HttpResponseMessage response = null;
            try
            {

                //
                Task<string> task = AccessTokenGenerator();
                CrmBearerToken = task.Result;

                string crmRestQuery = CrmApiUrl + query;
                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Patch, crmRestQuery);
                //add header parameters
                request.Headers.Add("Authorization", "Bearer " + CrmBearerToken);
                request.Content = new StringContent(entity.ToString());
                request.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/json; charset=utf-8");
                HttpClient httpClient = new HttpClient();
                response = httpClient.SendAsync(request).Result;

                TokenValidate(response);
                if (CrmBearerToken == null)
                {
                    UpdateRequest(query, entity);
                }
            }
            catch (WebException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return response;

        }
        public string getSurveryProviderByName(string Name)
        {
            string surveyProviderId = "";
            SurveyProviderModel surveyProviderModel = new SurveyProviderModel();
            string query = string.Format(Utility.getSurveyProviderByName, Name);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    surveyProviderModel = JsonConvert.DeserializeObject<SurveyProviderModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    if (surveyProviderModel.value != null && surveyProviderModel.value.Count > 0)
                    {
                        surveyProviderId = surveyProviderModel.value[0].aacn_survey_providerid;
                    }
                }
                else
                {

                }


            }
            return surveyProviderId;

        }
        public void TokenValidate(HttpResponseMessage response)
        {
            if (IsTokenValid == false)
            {
                if (response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.NoContent)
                {
                    IsTokenValid = true;
                    // CrmBearerToken = null;
                }
                else if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    CrmBearerToken = null;
                }
            }

        }

        //Async Method's 
        public async Task<HttpResponseMessage> GetRecordsAsync<T>(string query, ILogger<T> _logger = null)
        {
            HttpResponseMessage response = null;
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            try
            {
                string crmBearerToken = await AccessTokenGenerator();
                string crmRestQuery = CrmApiUrl + query;
                using (HttpClient httpClient = new HttpClient())
                {
                    HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Get, crmRestQuery);
                    request.Headers.Add("Prefer", "odata.include-annotations=\"*\"");
                    request.Headers.Add("Authorization", "Bearer " + crmBearerToken);
                    response = await httpClient.SendAsync(request);
                    TokenValidate(response);
                    if (crmBearerToken == null)
                    {
                        await GetRecordsAsync(crmRestQuery, _logger);
                    }
                    if (response.IsSuccessStatusCode)
                    {
                        string responseString = await response.Content.ReadAsStringAsync();
                        _logger.LogInformation("Response- String" + responseString);
                    }
                    else
                    {
                        _logger.LogError($"Request failed with status code {response.StatusCode}");
                    }
                }
            }
            catch (WebException ex)
            {
                throw new ApplicationException("An error occurred while accessing the web service.", ex);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                throw new ApplicationException("An unexpected error occurred.", ex);
            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return response;
        }
        public async Task<HttpResponseMessage> CreateRecordWithRetry<T>(string query, object entity, ILogger<T> logger)
        {
            logger.LogInformation(LoggerHelper.LogMethodStart(logger) + "CreateRecord -async");
            HttpResponseMessage response = null;
            int maxRetryAttempts = 3; // Number of retry attempts after the initial attempt
            int delayBetweenRetriesMilliseconds = 2000;

            for (int attempt = 0; attempt <= maxRetryAttempts; attempt++)
            {
                try
                {
                    string crmRestQuery = CrmApiUrl + query;

                    using (HttpClient client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromMinutes(3); // Set timeout to 3 minutes
                        Task<string> tokenTask = AccessTokenGenerator();
                        CrmBearerToken = await tokenTask;

                        if (CrmBearerToken == null)
                        {
                            continue;
                        }

                        HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, crmRestQuery);
                        request.Headers.Add("Authorization", "Bearer " + CrmBearerToken);
                        request.Content = new StringContent(entity.ToString(), Encoding.UTF8, "application/json");

                        response = await client.SendAsync(request);

                        TokenValidate(response);
                        if (response.IsSuccessStatusCode)
                        {
                            return response;
                        }
                        else if ((int)response.StatusCode == 499)
                        {
                            // Log and retry
                            logger.LogInformation("Request was canceled. Retrying...");
                        }
                        else
                        {
                            // Log the failure
                            logger.LogInformation($"Request failed with status code: {(int)response.StatusCode}");
                        }
                    }
                }
                catch (HttpRequestException ex)
                {
                    logger.LogInformation($"Request exception: {ex.Message}. Retrying...");
                }
                catch (Exception ex)
                {
                    logger.LogInformation($"Exception: {ex.Message}");
                    throw; // Rethrow any other exceptions
                }

                // Wait before retrying, unless it's the last attempt
                if (attempt < maxRetryAttempts)
                {
                    await Task.Delay(delayBetweenRetriesMilliseconds);
                }
            }
            logger.LogInformation(LoggerHelper.LogMethodEnd(logger) + "CreateRecord -async");
            return response;
        }
        public async Task<HttpResponseMessage> UpsertRecordAsync<T>(string query, object entity, string RequestFor, ILogger<T> logger)
        {

            HttpResponseMessage response = null;
            int maxRetryAttempts = 3; // Number of retry attempts after the initial attempt
            int delayBetweenRetriesMilliseconds = 2000;

            for (int attempt = 0; attempt <= maxRetryAttempts; attempt++)
            {
                try
                {
                    string crmRestQuery = CrmApiUrl + query;

                    using (HttpClient client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromMinutes(3); // Set timeout to 3 minutes
                        Task<string> tokenTask = AccessTokenGenerator();
                        CrmBearerToken = await tokenTask;

                        if (CrmBearerToken == null)
                        {
                            continue;
                        }
                        HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, crmRestQuery);
                        if (RequestFor.ToLower() == "update".ToLower())
                        {
                            request = null;
                            request = new HttpRequestMessage(HttpMethod.Patch, crmRestQuery);
                        }

                        request.Headers.Add("Authorization", "Bearer " + CrmBearerToken);
                        request.Content = new StringContent(entity.ToString(), Encoding.UTF8, "application/json");

                        response = await client.SendAsync(request);

                        TokenValidate(response);
                        if (response.IsSuccessStatusCode)
                        {
                            return response;
                        }
                        else if ((int)response.StatusCode == 499)
                        {
                            // Log and retry
                            logger.LogInformation("Request was canceled. Retrying...");
                        }
                        else
                        {
                            // Log the failure
                            logger.LogInformation($"Request failed with status code: {(int)response.StatusCode}");
                        }
                    }
                }
                catch (HttpRequestException ex)
                {
                    logger.LogInformation($"Request exception: {ex.Message}. Retrying...");
                }
                catch (Exception ex)
                {
                    logger.LogInformation($"Exception: {ex.Message}");
                    throw; // Rethrow any other exceptions
                }

                // Wait before retrying, unless it's the last attempt
                if (attempt < maxRetryAttempts)
                {
                    await Task.Delay(delayBetweenRetriesMilliseconds);
                }
            }

            return response;
        }

        public async Task<HttpResponseMessage> DeleteRecordsAsync<T>(string query, ILogger<T> logger)
        {

            HttpResponseMessage response = null;
            int maxRetryAttempts = 3; // Number of retry attempts after the initial attempt
            int delayBetweenRetriesMilliseconds = 2000;

            for (int attempt = 0; attempt <= maxRetryAttempts; attempt++)
            {
                try
                {
                    string crmRestQuery = CrmApiUrl + query;
                    using (HttpClient client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromMinutes(3); // Set timeout to 3 minutes
                        Task<string> tokenTask = AccessTokenGenerator();
                        CrmBearerToken = await tokenTask;

                        if (CrmBearerToken == null)
                        {
                            continue;
                        }
                        HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Delete, crmRestQuery);
                        request.Headers.Add("Authorization", "Bearer " + CrmBearerToken);
                        response = await client.SendAsync(request);
                        TokenValidate(response);
                        if (response.IsSuccessStatusCode)
                        {
                            return response;
                        }
                        else if ((int)response.StatusCode == 499)
                        {
                            logger.LogInformation("Request was canceled. Retrying...");
                        }
                        else
                        {
                            // Log the failure
                            logger.LogInformation($"Request failed with status code: {(int)response.StatusCode}");
                        }
                    }
                }
                catch (HttpRequestException ex)
                {
                    logger.LogInformation($"Request exception: {ex.Message}. Retrying...");
                }
                catch (Exception ex)
                {
                    logger.LogInformation($"Exception: {ex.Message}");
                    throw; // Rethrow any other exceptions
                }

                // Wait before retrying, unless it's the last attempt
                if (attempt < maxRetryAttempts)
                {
                    await Task.Delay(delayBetweenRetriesMilliseconds);
                }
            }

            return response;
        }

        public async Task<HttpResponseMessage> BulkDeleterecordsAsync<T>(string entitySetName, List<string> guids, ILogger<T> _logger)
        {
            HttpResponseMessage response = null;
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    Task<string> tokenTask = AccessTokenGenerator();
                    string CrmBearerToken = await tokenTask;
                    var batchContent = new StringBuilder();
                    var boundary = "batch_" + Guid.NewGuid();

                    foreach (var id in guids)
                    {
                        var deleteUrl = $"{CrmApiUrl}{entitySetName}({id})";
                        batchContent.AppendLine($"--{boundary}");
                        batchContent.AppendLine("Content-Type: application/http");
                        batchContent.AppendLine("Content-Transfer-Encoding: binary");
                        batchContent.AppendLine();
                        batchContent.AppendLine($"DELETE {deleteUrl} HTTP/1.1");
                        batchContent.AppendLine();
                    }
                    batchContent.AppendLine($"--{boundary}--");

                    var batchRequest = new HttpRequestMessage(HttpMethod.Post, CrmApiUrl + "$batch");
                    batchRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", CrmBearerToken);
                    batchRequest.Content = new StringContent(batchContent.ToString());
                    batchRequest.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("multipart/mixed; boundary=" + boundary);

                    response = await client.SendAsync(batchRequest);
                    if (response.IsSuccessStatusCode)
                    {
                        return response;
                    }
                    else
                    {
                        var responseBody = await response.Content.ReadAsStringAsync();
                        _logger.LogInformation($"Request failed with status code: {(int)response.StatusCode}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"Exception: {ex.Message}");
            }
            return response;
        }

        public async Task<HttpResponseMessage> UpdateRequestAsync<T>(string query, object entity, ILogger<T> _logger)
        {
            HttpResponseMessage response = null;
            try
            {
                _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
                _logger.LogInformation(LoggerHelper.LogObjectValue(entity, JsonConvert.SerializeObject(entity)));
                // Assuming AccessTokenGenerator is an async method
                string CrmBearerToken = await AccessTokenGenerator();

                string crmRestQuery = CrmApiUrl + query;
                using (var httpClient = new HttpClient()) // Reuse HttpClient instance ideally (not within method scope in a real-world scenario)
                {
                    var request = new HttpRequestMessage(HttpMethod.Patch, crmRestQuery);
                    // Add header parameters
                    request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", CrmBearerToken);
                    request.Content = new StringContent(JsonConvert.SerializeObject(entity).ToString(), Encoding.UTF8, "application/json");
                    response = await httpClient.SendAsync(request);
                    TokenValidate(response); // Ensure this is also an async method if applicable
                    if (response.StatusCode == HttpStatusCode.Unauthorized) // Check for token expiration or unauthorized status
                    {
                        // Handle token refresh logic or retry as needed
                        CrmBearerToken = await AccessTokenGenerator();
                        request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", CrmBearerToken);
                        response = await httpClient.SendAsync(request);
                    }
                }
            }
            catch (HttpRequestException ex)
            {
                LoggerHelper.LogException(_logger, ex);
                throw new InvalidOperationException("An error occurred while sending the request.", ex);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                throw new ApplicationException("An unexpected error occurred.", ex);
            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));

            return response;
        }
        public async Task<HttpResponseMessage> CreateBatchRecords<T, R>(string query, List<R> entities, ILogger<T> logger)
        {
            HttpResponseMessage response = null;
            int maxRetryAttempts = 3; // Number of retry attempts after the initial attempt
            int delayBetweenRetriesMilliseconds = 2000;

            for (int attempt = 0; attempt <= maxRetryAttempts; attempt++)
            {
                try
                {
                    string crmRestQuery = CrmApiUrl + query;

                    using (HttpClient client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromMinutes(3); // Set timeout to 3 minutes
                        Task<string> tokenTask = AccessTokenGenerator();
                        CrmBearerToken = await tokenTask;

                        if (CrmBearerToken == null)
                        {
                            continue;
                        }

                        // Construct the batch request
                        var batchBoundary = "batch_" + Guid.NewGuid();
                        var changesetBoundary = "changeset_" + Guid.NewGuid();
                        var batchContent = new StringBuilder();
                        batchContent.AppendLine($"--{batchBoundary}");
                        batchContent.AppendLine("Content-Type: multipart/mixed; boundary=" + changesetBoundary);
                        batchContent.AppendLine();

                        int contentId = 1;
                        foreach (var entity in entities)
                        {
                            batchContent.AppendLine($"--{changesetBoundary}");
                            batchContent.AppendLine("Content-Type: application/http");
                            batchContent.AppendLine("Content-Transfer-Encoding: binary");
                            batchContent.AppendLine($"Content-ID: {contentId++}");
                            batchContent.AppendLine();
                            batchContent.AppendLine("POST " + crmRestQuery + " HTTP/1.1");
                            batchContent.AppendLine("Content-Type: application/json;type=entry");
                            batchContent.AppendLine();
                            batchContent.AppendLine(JsonConvert.SerializeObject(entity));
                        }

                        batchContent.AppendLine($"--{changesetBoundary}--");
                        batchContent.AppendLine($"--{batchBoundary}--");

                        var request = new HttpRequestMessage(HttpMethod.Post, CrmApiUrl + "$batch");
                        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", CrmBearerToken);
                        request.Content = new StringContent(batchContent.ToString());
                        request.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("multipart/mixed; boundary=" + batchBoundary);

                        response = await client.SendAsync(request);
                        TokenValidate(response);
                        if (response.IsSuccessStatusCode)
                        {
                            return response;
                        }
                        else if ((int)response.StatusCode == 499)
                        {
                            logger.LogInformation("Request was canceled. Retrying...");
                        }
                        else
                        {
                            logger.LogInformation($"Request failed with status code: {(int)response.StatusCode}");
                        }
                    }
                }
                catch (HttpRequestException ex)
                {
                    logger.LogInformation($"Request exception: {ex.Message}. Retrying...");
                }
                catch (Exception ex)
                {
                    logger.LogInformation($"Exception: {ex.Message}");
                    throw; // Rethrow any other exceptions
                }

                // Wait before retrying, unless it's the last attempt
                if (attempt < maxRetryAttempts)
                {
                    await Task.Delay(delayBetweenRetriesMilliseconds);
                }
            }

            return response;
        }

        public async Task<HttpResponseMessage> UpdateBatchRecords<T>(string query, List<BeaconResponseLines> entities, ILogger<T> logger)
        {
            HttpResponseMessage response = null;
            int maxRetryAttempts = 3; // Number of retry attempts after the initial attempt
            int delayBetweenRetriesMilliseconds = 2000;

            for (int attempt = 0; attempt <= maxRetryAttempts; attempt++)
            {
                try
                {
                    string crmRestQuery = CrmApiUrl + query;
                    using (HttpClient client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromMinutes(3); // Set timeout to 3 minutes
                        Task<string> tokenTask = AccessTokenGenerator();
                        CrmBearerToken = await tokenTask;

                        if (CrmBearerToken == null)
                        {
                            continue;
                        }

                        // Construct the batch request
                        var batchBoundary = "batch_" + Guid.NewGuid();
                        var changesetBoundary = "changeset_" + Guid.NewGuid();
                        var batchContent = new StringBuilder();
                        batchContent.AppendLine($"--{batchBoundary}");
                        batchContent.AppendLine("Content-Type: multipart/mixed; boundary=" + changesetBoundary);
                        batchContent.AppendLine();

                        int contentId = 1;
                        foreach (var entity in entities)
                        {
                            batchContent.AppendLine($"--{changesetBoundary}");
                            batchContent.AppendLine("Content-Type: application/http");
                            batchContent.AppendLine("Content-Transfer-Encoding: binary");
                            batchContent.AppendLine($"Content-ID: {contentId++}");
                            batchContent.AppendLine();
                            batchContent.AppendLine("PATCH " + crmRestQuery + $"({entity.ResponseLineKey})" + " HTTP/1.1");
                            batchContent.AppendLine("Content-Type: application/json;type=entry");
                            batchContent.AppendLine();
                            batchContent.AppendLine(JsonConvert.SerializeObject(entity));
                        }

                        batchContent.AppendLine($"--{changesetBoundary}--");
                        batchContent.AppendLine($"--{batchBoundary}--");

                        var request = new HttpRequestMessage(HttpMethod.Patch, CrmApiUrl + "$batch");
                        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", CrmBearerToken);
                        request.Content = new StringContent(batchContent.ToString());
                        request.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("multipart/mixed; boundary=" + batchBoundary);

                        response = await client.SendAsync(request);

                        TokenValidate(response);
                        if (response.IsSuccessStatusCode)
                        {
                            return response;
                        }
                        else if ((int)response.StatusCode == 499)
                        {
                            logger.LogInformation("Request was canceled. Retrying...");
                        }
                        else
                        {
                            logger.LogInformation($"Request failed with status code: {(int)response.StatusCode}");
                        }
                    }
                }
                catch (HttpRequestException ex)
                {
                    logger.LogInformation($"Request exception: {ex.Message}. Retrying...");
                }
                catch (Exception ex)
                {
                    logger.LogInformation($"Exception: {ex.Message}");
                    throw; // Rethrow any other exceptions
                }

                // Wait before retrying, unless it's the last attempt
                if (attempt < maxRetryAttempts)
                {
                    await Task.Delay(delayBetweenRetriesMilliseconds);
                }
            }

            return response;
        }

        public BatchResponse ParseBatchResponse(string responseContent)
        {
            var batchResponse = new BatchResponse();

            // Split the response content by the batch boundary
            var batchParts = responseContent.Split(new[] { "--batchresponse_" }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var part in batchParts)
            {
                // Split the part by the changeset boundary
                var changesetParts = part.Split(new[] { "--changesetresponse_" }, StringSplitOptions.RemoveEmptyEntries);

                foreach (var changesetPart in changesetParts)
                {
                    // Check if the part contains a successful response
                    if (changesetPart.Contains("HTTP/1.1 201 Created") || changesetPart.Contains("HTTP/1.1 204 No Content"))
                    {
                        var createdEntity = new BatchResponseItem();

                        // Extract the Location and OData-EntityId headers
                        var locationHeader = ExtractHeader(changesetPart, "Location");
                        var odataEntityIdHeader = ExtractHeader(changesetPart, "OData-EntityId");

                        if (!string.IsNullOrEmpty(locationHeader))
                        {
                            createdEntity.Location = locationHeader;
                            createdEntity.recordId = ExtractGuidFromHeader(odataEntityIdHeader).ToString();

                        }

                        if (!string.IsNullOrEmpty(odataEntityIdHeader))
                        {
                            createdEntity.ODataEntityId = odataEntityIdHeader;
                            createdEntity.recordId = ExtractGuidFromHeader(odataEntityIdHeader).ToString();
                        }

                        batchResponse.Items.Add(createdEntity);
                    }
                }
            }

            return batchResponse;
        }
        private string ExtractHeader(string content, string headerName)
        {
            var headerLine = content.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None)
                                    .FirstOrDefault(line => line.StartsWith(headerName + ":"));
            if (headerLine != null)
            {
                var headerValue = headerLine.Substring(headerName.Length + 1).Trim();
                return headerValue;
            }
            return null;
        }
        private Guid ExtractGuidFromHeader(string headerValue)
        {
            var startIndex = headerValue.IndexOf('(') + 1;
            var endIndex = headerValue.IndexOf(')');
            if (startIndex > 0 && endIndex > startIndex)
            {
                var guidString = headerValue.Substring(startIndex, endIndex - startIndex);
                if (Guid.TryParse(guidString, out var guid))
                {
                    return guid;
                }
            }
            return Guid.Empty;
        }

        public static DataTable GetSqlData(string query, ILogger<beaconController> _logger)
        {
            DataTable sqlData = new DataTable();
            try
            {
                Task<string> task = AccessTokenGenerator();
                string crmToken = task.Result;
                string crmUrl = Connection.CrmResourceUrl;
                string crmResourceUrl = crmUrl.Replace("https://", "");
                using (SqlConnection sqlConn = new SqlConnection($"server={crmResourceUrl}"))
                {
                    sqlConn.AccessToken = crmToken;

                    sqlConn.Open();
                    using (SqlDataAdapter adapter = new SqlDataAdapter(query, sqlConn))
                    {
                        adapter.Fill(sqlData);
                        return sqlData;
                    }
                }
            }
            catch (Exception e)
            {
                LoggerHelper.LogException(_logger, e);
            }
            return sqlData;
        }

    }



}


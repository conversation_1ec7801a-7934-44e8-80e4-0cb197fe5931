﻿namespace AACN.API.Service
{
    using AACN.API.Model;
    using AACN.Services;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;
    using static System.Net.WebRequestMethods;

    public static class Utility
    {
        #region commonFunctions
        public static string RemoveJsonNulls(string str)
        {
            Regex regex = new Regex(UtilityRegExp.JsonNullRegEx);
            string data = regex.Replace(str, string.Empty);
            regex = new Regex(UtilityRegExp.JsonNullArrayRegEx);
            return regex.Replace(data, "[]");
        }

        #endregion

        #region odataQueries

        public static readonly string GetMemberById = "contacts?$filter=(contactid eq '{0}')";
        public static readonly string GetEventById = "aacn_events?$filter=(aacn_eventid eq '{0}')";
        public static readonly string GetAnswerById = "aacn_answers?$filter=(aacn_answerid eq '{0}')";
        public static readonly string getAssessmentbyId = "aacn_assessments?$filter=(aacn_assessmentid eq '{0}')";
        public static readonly string getAssessmentlinebyId = "aacn_assessment_lines?$filter=(aacn_assessment_lineid eq '{0}')";
        public static readonly string getSessionbyId = "aacn_sessions?$filter=(aacn_sessionid eq '{0}')";
        public static readonly string getResponseLinebyId = "aacn_response_lines?$filter=(aacn_response_lineid eq '{0}')";
        public static readonly string getAssessmentLineOptionId = "aacn_assessment_line_options?$filter=(aacn_assessment_line_optionid eq '{0}')";
        public static readonly string getQuestionbyId = "aacn_questions?$filter=(aacn_questionid eq '{0}')";
        public static readonly string getResponsebyId = "aacn_responses?$filter=(aacn_responseid eq '{0}')";
        public static readonly string getSpeakerbyId = "aacn_speakers?$filter=(aacn_speakerid eq '{0}')";
        public static readonly string getSurveyProviderByName = "aacn_survey_providers?$select=aacn_survey_providerid&$filter=(aacn_survey_provider eq '{0}')";


        public static readonly string getAssessmentByNumber = "aacn_assessments?$select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,_modifiedby_value,modifiedon," +
                    "_modifiedonbehalfby_value,overriddencreatedon&$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value," +
                    "aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type," + "aacn_assessment_lineid,aacn_assessment_max_responses,createdon,_modifiedby_value,overriddencreatedon;" +
                    "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text," +
                    "_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type," +
                    "aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon))&" +
                    "$filter=(aacn_assessment_number eq '{0}')";


        public static readonly string getAssessmentByNumberNew = "aacn_assessments?$select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,modifiedon,overriddencreatedon&$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon;$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_createdby_value,createdon,modifiedon)," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,_createdonbehalfby_value;$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_createdby_value,createdon)))," +
            "aacn_event_assessment_code_aacn_assessment($select=_aacn_assessment_code_value,aacn_end_date,aacn_event_id,aacn_event_name,aacn_eventid,_aacn_parent_event_value,aacn_start_date,_createdby_value,createdon;$expand=aacn_event_parent_event_aacn_event($select=_aacn_assessment_code_value,aacn_end_date,aacn_event_id,aacn_event_name,aacn_eventid,_aacn_parent_event_value,aacn_start_date,_aacn_survey_provider_value,modifiedon))," +
            "aacn_response_assessment_aacn_assessment($select=_aacn_assessment_value,_aacn_event_value,_aacn_member_value,aacn_response_number,aacn_responseid,_aacn_session_value,_aacn_survey_provider_value,_createdby_value,createdon;$expand=aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text,_aacn_response_value,aacn_response_line_number,aacn_response_lineid,aacn_response_text,utcconversiontimezonecode,versionnumber))," +
            "aacn_session_assessment_code_aacn_assessment($select=_aacn_assessment_code_value,aacn_end_date,aacn_end_time,_aacn_event_id_value,aacn_session_id,aacn_session_name,aacn_sessionid,aacn_start_date,aacn_start_time,_aacn_survey_provider_value,_createdby_value,createdon,statecode,statuscode)" +
            "&$filter=(aacn_assessment_number eq '{0}')";



        public static readonly string getAssessmentByNumberNew1 = "aacn_assessments?$select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,modifiedon,overriddencreatedon&$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_createdby_value,createdon,modifiedon)," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,_createdonbehalfby_value;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_createdby_value,createdon)," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,overriddencreatedon;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon,modifiedon)," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon;" +
            "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,createdon;$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon,modifiedon,_modifiedonbehalfby_value))))))" +
            "&$filter=(aacn_assessment_number eq '{0}')";


        public static readonly string getAssessmentByNumberNew2 = "aacn_assessments?$select=aacn_assessment_description,statecode,aacn_assessment_number,aacn_assessment_title,aacn_survey_provider,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,modifiedon,overriddencreatedon&$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,statecode,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_createdby_value,createdon,modifiedon)," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,_createdonbehalfby_value;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_createdby_value,createdon)," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,overriddencreatedon;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon,modifiedon)," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon;" +
           "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,statecode,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,createdon;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon,modifiedon,_modifiedonbehalfby_value)," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,statecode,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,timezoneruleversionnumber,utcconversiontimezonecode,versionnumber;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon)))))))&$filter=(aacn_assessment_number eq '{0}')";


        public static readonly string getEventData = "aacn_assessments?$select=aacn_assessment_description,statecode,aacn_assessment_number,aacn_assessment_title,aacn_survey_provider,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,modifiedon,overriddencreatedon&$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,statecode,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_createdby_value,createdon,modifiedon)," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,_createdonbehalfby_value;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_createdby_value,createdon)," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,overriddencreatedon;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon,modifiedon)," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon;" +
            "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,statecode,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,createdon;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon,modifiedon,_modifiedonbehalfby_value)," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,statecode,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,timezoneruleversionnumber,utcconversiontimezonecode,versionnumber;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon)))))))&$filter=(aacn_assessment_number eq '{0}')";

        public static readonly string getSessionData = "aacn_sessions?$select=aacn_session_id&$filter=(aacn_session_id eq '{0}')";

        public static readonly string getEventsData = "aacn_events?$select=aacn_event_id&$filter=(aacn_event_id eq '{0}')";


        public static readonly string getSessionResponse = "aacn_sessions?$select=_aacn_assessment_code_value,aacn_end_date,aacn_end_time,_aacn_event_id_value,aacn_session_id,aacn_session_name,aacn_sessionid,aacn_start_date,aacn_start_time,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,statecode&$expand=aacn_event_id($select=_aacn_assessment_code_value,aacn_end_date,aacn_event_id,aacn_event_name,aacn_eventid,_aacn_parent_event_value,aacn_start_date,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,overriddencreatedon,statecode),aacn_assessment_code($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,statecode;$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_modifiedby_value,overriddencreatedon,statecode;$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,createdon;$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text),aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode,statuscode;$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode),aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode))))),aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,timezoneruleversionnumber,utcconversiontimezonecode,versionnumber))),aacn_speaker_session_aacn_session($select=_aacn_session_value,_aacn_speaker_value,aacn_speaker_name,aacn_speaker_number,aacn_speakerid,statecode)&$filter=(aacn_session_id eq '{0}') and (aacn_event_id/aacn_eventid ne null) and (aacn_assessment_code/aacn_assessmentid ne null)";
        public static readonly string getEventResponse = "aacn_events?$select=_aacn_assessment_code_value,aacn_end_date,aacn_event_id,_aacn_survey_provider_value,statecode,aacn_event_name,aacn_eventid,_aacn_parent_event_value,_ownerid_value,_owningbusinessunit_value,statuscode&$expand=aacn_event_parent_event_aacn_event($select=_aacn_assessment_code_value,aacn_end_date,aacn_event_id,aacn_event_name,aacn_eventid,_aacn_parent_event_value,aacn_start_date,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,overriddencreatedon,statecode),aacn_assessment_code($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,statecode;$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_modifiedby_value,overriddencreatedon,statecode;$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,createdon;$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text),aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode,statuscode;$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode),aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode))))),aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,timezoneruleversionnumber,utcconversiontimezonecode,versionnumber)))&$filter=(aacn_event_name eq '{0}') and (aacn_assessment_code/aacn_assessmentid ne null)";

        public static readonly string getContactRecords = "contacts?$select=address1_city,firstname,lastname,middlename,mobilephone&$filter=(aacn_contact_number eq '{0}')";


        public static readonly string getSessionResponseNew = "aacn_sessions?$select=_aacn_assessment_code_value,aacn_session_description,aacn_end_date,aacn_end_time,_aacn_event_id_value,aacn_session_id,aacn_session_name,aacn_sessionid,aacn_start_date,aacn_start_time,overriddencreatedon,_ownerid_value,statecode" +
            "&$expand=aacn_assessment_code($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,importsequencenumber,overriddencreatedon,statecode;" +
            "$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode,statuscode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0))," +
            "aacn_speaker_session_aacn_session($select=_aacn_session_value,_aacn_speaker_value,aacn_speaker_name,aacn_speaker_number,aacn_speakerid,statecode,versionnumber),aacn_event_id($select=aacn_event_name)&$filter=(aacn_session_id eq '{0}')";

        public static readonly string getSessionResponseNewwitheventCode = "aacn_sessions?$select=_aacn_assessment_code_value,aacn_session_description,aacn_end_date,aacn_end_time,_aacn_event_id_value,aacn_session_id,aacn_session_name,aacn_sessionid,aacn_start_date,aacn_start_time,overriddencreatedon,_ownerid_value,statecode" +
           "&$expand=aacn_assessment_code($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,importsequencenumber,overriddencreatedon,statecode;" +
           "$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode,statuscode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0))," +
           "aacn_speaker_session_aacn_session($select=_aacn_session_value,_aacn_speaker_value,aacn_speaker_name,aacn_speaker_number,aacn_speaker_sort_order,aacn_speakerid,statecode,versionnumber),aacn_event_id($select=aacn_event_name)&$filter=(aacn_session_id eq '{0}' and _aacn_event_id_value eq '{1}')";

        public static readonly string getEventResponseNew = "aacn_events?$select=_aacn_assessment_code_value,aacn_end_date,aacn_event_id,statecode,aacn_event_name,aacn_eventid,_aacn_parent_event_value,_aacn_survey_provider_value,statuscode&" +
            "$expand=aacn_assessment_code($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,importsequencenumber,overriddencreatedon,statecode;" +
            "$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_tool_tip_description,aacn_banner_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode,statuscode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode;$filter=(statecode eq 0))" +
            ",aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_tool_tip_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_tool_tip_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_tool_tip_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_tool_tip_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0))," +
            "aacn_event_parent_event_aacn_event($select=_aacn_assessment_code_value,aacn_end_date,aacn_event_id,aacn_event_name,aacn_eventid,_aacn_parent_event_value,aacn_start_date,_aacn_survey_provider_value,statuscode)&$filter=(aacn_event_id eq '{0}')";

        public static readonly string getInstanceResponse = "aacn_instances?$select=aacn_instance_id,aacn_start_date,aacn_unit_id,statecode,aacn_user_id,aacn_end_date,aacn_assessment_code,statuscode&" +
            "$expand=aacn_assessment_code($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,importsequencenumber,overriddencreatedon,statecode;" +
            "$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_tool_tip_description,aacn_banner_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode,statuscode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode;$filter=(statecode eq 0))" +
            ",aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_tool_tip_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_tool_tip_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_tool_tip_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_tool_tip_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0))," +
            "&$filter=(aacn_instance_id eq '{0}')";


        public static readonly string getEventResponseNewforPE = "aacn_events?$select=_aacn_assessment_code_value,aacn_end_date,aacn_event_id,statecode,aacn_event_name,aacn_eventid,_aacn_parent_event_value,_aacn_survey_provider_value,statuscode&" +
            "$expand=aacn_assessment_code($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,importsequencenumber,overriddencreatedon,statecode;" +
            "$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_banner_description,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode,statuscode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode;$filter=(statecode eq 0))" +
            ",aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0))," +
            "aacn_event_parent_event_aacn_event($select=_aacn_assessment_code_value,aacn_end_date,aacn_event_id,aacn_event_name,aacn_eventid,_aacn_parent_event_value,aacn_start_date,_aacn_survey_provider_value,statuscode)&$filter=(aacn_eventid eq '{0}')";


        public static string getSpecialCharacters = "aacn_special_word_formats?$select=aacn_special_word,aacn_special_word_formatid,aacn_format_type";
        public static string getAssessmentByCode = "aacn_assessments?$select=aacn_assessment_number,aacn_assessmentid&$filter=(aacn_assessment_number eq '{0}')";

        public static string getResponseCounterbySesssion = "aacn_responses?$select=aacn_responseid&$filter=(_aacn_session_value eq '{0}')";

        public static string getResponseDataByAssessmentCode = "aacn_responses?$select=_aacn_assessment_value,_aacn_event_value,_aacn_member_value,aacn_response_number,aacn_responseid,_aacn_session_value,_aacn_survey_provider_value,statecode,versionnumber&$expand=aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text,_aacn_response_value,aacn_response_line_number,aacn_response_line_text_area,aacn_response_lineid,aacn_response_text,overriddencreatedon)&$filter=(_aacn_instance_id_value eq '{0}')";
        public static string getInstanceDataById = "aacn_instances?$select=_aacn_assessment_code_value,aacn_end_date,aacn_instance_id,aacn_start_date,utcconversiontimezonecode,versionnumber&$filter=(aacn_instance_id eq '{0}')";

        public static string getInstanceDataByIdnew = "aacn_instances?$select=aacn_instance_id,_aacn_assessment_code_value,aacn_start_date,aacn_end_date,aacn_status&$filter=(aacn_instance_id eq '{0}')";

        public static string getparenteventIdbyName = "aacn_events?$select=aacn_event_id,aacn_eventid,_aacn_parent_event_value&$filter=(aacn_event_id eq '{0}')";
        public static string getresponseDataById = "aacn_responses?$select=_aacn_assessment_value,_aacn_event_value,_aacn_member_value,aacn_response_number,aacn_responseid,_aacn_session_value,_aacn_survey_provider_value,_aacn_instance_id_value,createdon&$expand=aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text,_aacn_response_value,aacn_response_line_number,aacn_response_line_text_area,aacn_response_lineid,aacn_response_text,_aacn_speaker_value)&$filter=(aacn_responseid eq '{0}')";

        public static string getsurveyInstances = "aacn_instances?$select=aacn_instance_id,_aacn_assessment_code_value,aacn_start_date,aacn_end_date,aacn_status&$filter=(aacn_user_id eq '{0}')";

        public static string getsurveyInstancesbyUserId = "aacn_instances?$select=aacn_instance_id,_aacn_assessment_code_value,aacn_start_date,aacn_end_date,aacn_status&$filter=(aacn_user_id eq '{0}' and aacn_unit_id eq '{1}' )";


        public static readonly string getAssessmentBySurveyProviderName = "aacn_survey_provider?$select=aacn_survey_provider,statecode" +
           "&$expand=aacn_assessment_code($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,importsequencenumber,overriddencreatedon,statecode;" +
           "$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode,statuscode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0))," +
           "aacn_speaker_session_aacn_session($select=_aacn_session_value,_aacn_speaker_value,aacn_speaker_name,aacn_speaker_number,aacn_speaker_sort_order,aacn_speakerid,statecode,versionnumber),aacn_event_id($select=aacn_event_name)&$filter=(aacn_session_id eq '{0}' and _aacn_event_id_value eq '{1}')";

        #region Beacon
        public enum roleType
        {
            Manager = 1,
            Reviewer = 2
        }
        public static string GetResponsesByReviwerId = "aacn_assessment_review_activities?$select=aacn_assessment_review_activity_number,aacn_request_status,_aacn_reviewer_value&$filter=(_aacn_reviewer_value eq {0})";
        public static string GetActivityResponseByReviewerId = "aacn_assessment_review_activities?$select=_aacn_assessment_value,aacn_application_id,aacn_accepted_date,aacn_assigned_date,aacn_completed_date,aacn_rejected_date,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_name,aacn_rejected_reason,aacn_request_status,_aacn_reviewer_value,_aacn_reviewers_response_value,_aacn_unit_value,_aacn_user_response_value,createdon&$expand=aacn_unit_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid),aacn_reviewer_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid),aacn_Unit($select=accountid,accountnumber,name,address1_city,address1_stateorprovince;$expand=parentaccountid($select=accountid,accountnumber,name))&$filter=(_aacn_reviewer_value eq {0} and _aacn_year_value eq {1})";
        public static string GetContactListByRole = "contacts?$select=aacn_user_end_date,aacn_user_start_date,aacn_contact_number,accountrolecode,firstname,_aacn_position_value,lastname,contactid,emailaddress1&$filter=(_aacn_position_value eq {0})";

        public static string GetAssessmentReviewActivityId = "aacn_assessment_review_activities?$select=aacn_assessment_review_activityid,aacn_request_status&$filter=(_aacn_user_response_value eq {0})";

        public static string getManagerDashBoardbyStatus = "aacn_assessment_review_activities ?$select=_aacn_assessment_value,aacn_assigned_date,aacn_completed_date,aacn_rejected_date,aacn_accepted_date,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_name,_aacn_organization_value,aacn_rejected_reason,_aacn_unit_value,aacn_request_status,_aacn_reviewer_value,_aacn_reviewers_response_value,_aacn_user_response_value,createdon&" +
            "$expand=aacn_assessment($select= aacn_assessment_description, aacn_assessment_number, aacn_assessment_title, aacn_assessmentid)" +
            ",aacn_unit_module($select= aacn_assessment_description, aacn_assessment_number, aacn_assessment_title, aacn_assessmentid)," +
            "aacn_reviewer_module($select= aacn_assessment_description, aacn_assessment_number,aacn_assessment_title, aacn_assessmentid)&$filter=(aacn_request_status eq {0})";

        public static string getManagerDashBoardbyStatusNew = "aacn_assessment_review_activities?$select=aacn_accepted_date,aacn_module_completion_status,_aacn_assessment_value,_aacn_unit_value,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_assigned_date,aacn_name,aacn_rejected_date,aacn_rejected_reason,aacn_request_status,_aacn_reviewer_value,_aacn_reviewer_module_value,_aacn_reviewers_response_value,_aacn_unit_module_value,_aacn_user_response_value,statecode,statuscode&" +
            "$expand=aacn_unit_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_reviewer_module_value,_aacn_survey_provider_value,_aacn_unit_module_value,overriddencreatedon,statecode,statuscode),aacn_reviewer_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_reviewer_module_value,_aacn_survey_provider_value,_aacn_unit_module_value," +
            "overriddencreatedon,_owningbusinessunit_value,statecode,statuscode)," +
            "aacn_Unit($select=accountid,accountnumber,name,address1_city,address1_stateorprovince;$expand=parentaccountid($select=accountid,accountnumber,name))," +
            "aacn_reviewer($select=aacn_contact_number,contactid,firstname,lastname)&$filter=(aacn_request_status eq {0} and _aacn_reviewer_module_value ne null)";

        public static string GETALLAPPLICATIONS = "aacn_assessment_review_activities?$select=aacn_accepted_date,aacn_module_completion_status,_aacn_assessment_value,_aacn_unit_value,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_assigned_date,aacn_name,aacn_rejected_date,aacn_rejected_reason,aacn_request_status,_aacn_reviewer_value,_aacn_reviewer_module_value,_aacn_reviewers_response_value,_aacn_unit_module_value,_aacn_user_response_value,statecode,statuscode&" +
           "$expand=aacn_unit_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_reviewer_module_value,_aacn_survey_provider_value,_aacn_unit_module_value,overriddencreatedon,statecode,statuscode),aacn_reviewer_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_reviewer_module_value,_aacn_survey_provider_value,_aacn_unit_module_value," +
           "overriddencreatedon,_owningbusinessunit_value,statecode,statuscode)," +
           "aacn_Unit($select=accountid,accountnumber,name,address1_city,address1_stateorprovince;$expand=parentaccountid($select=accountid,accountnumber,name))," +
           "aacn_reviewer($select=aacn_contact_number,contactid,firstname,lastname)&$filter=(_aacn_reviewer_module_value ne null)";

        public static string getManagerDashBoard = "aacn_assessment_review_activities?$select=_aacn_assessment_value,aacn_application_id,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_name,aacn_rejected_reason,_aacn_unit_value,aacn_request_status,_aacn_reviewer_value,aacn_assigned_date,aacn_accepted_date,aacn_inprogress_date,aacn_rejected_date,aacn_completed_date,_aacn_reviewers_response_value,_aacn_user_response_value,createdon,modifiedon&$expand=aacn_assessment($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid),aacn_unit_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid),aacn_reviewer_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid),aacn_Unit($select=accountid,accountnumber,name,address1_city,address1_stateorprovince;$expand=parentaccountid($select=accountid,accountnumber,name))&" +
             "$filter=(aacn_request_status ne null and statecode eq 0 and _aacn_reviewer_module_value ne null and _aacn_year_value eq '{0}')";

        public static string GetAssessmentReviewActivitybyNumber = "aacn_assessment_review_activities?$select=aacn_application_id,_aacn_assessment_value,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_name,_aacn_organization_value,aacn_rejected_reason,aacn_request_status,_aacn_reviewer_value,_aacn_reviewers_response_value,_aacn_unit_value,_aacn_user_response_value,createdon&$expand=aacn_unit_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid),aacn_reviewer_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid)&$filter=(aacn_assessment_review_activity_number eq '{0}')";

        public static string GetReviewerAndUserResponses = "aacn_assessment_review_activities?$select=_aacn_year_value,_aacn_assessment_value,aacn_assigned_date,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,_aacn_organization_value,aacn_request_status,_aacn_reviewer_value,_aacn_reviewers_response_value," +
            "_aacn_user_response_value&$expand=aacn_user_response($select=aacn_aacn_assigned_date,_aacn_facility_name_value,_aacn_instance_id_value,_aacn_member_value,aacn_nf_minipeak_response_status,aacn_response_message,aacn_response_number,aacn_responseid,_aacn_unit_name_value;" +
            "$expand=aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text,_aacn_response_value,aacn_response_line_number,aacn_response_line_text_area,aacn_response_lineid,aacn_response_text,statecode;$filter=(statecode eq 0)))," +
            "aacn_reviewers_response($select=aacn_aacn_assigned_date,_aacn_facility_name_value,_aacn_instance_id_value,_aacn_member_value,aacn_nf_minipeak_response_status,aacn_response_message,aacn_response_number,aacn_responseid,_aacn_unit_name_value;" +
            "$expand=aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text,_aacn_response_value,aacn_response_line_number,aacn_response_line_text_area,aacn_response_lineid,aacn_response_text,statecode;$filter=(statecode eq 0)))" +
            "&$filter=(aacn_assessment_review_activityid eq '{0}')";

        public static string checkResponsebyId = "aacn_responses?$select=aacn_responseid&$filter=(aacn_responseid eq {0})";

        public static string getUnitModule = "aacn_assessment_review_activities?$select=aacn_accepted_date,aacn_assessment_review_activityid,aacn_request_status,_aacn_unit_module_value,aacn_assessment_review_activity_number" +
            "&$expand=aacn_unit_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,importsequencenumber,overriddencreatedon,statecode;" +
            "$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode,statuscode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
            "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;" +
            "$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0)),&$filter=(aacn_assessment_review_activityid eq '{0}')";

        public static string getReviewerModule = "aacn_assessment_review_activities?$select=aacn_accepted_date,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_request_status,_aacn_reviewer_value,_aacn_user_response_value,_aacn_unit_module_value" +
"&$expand=aacn_reviewer_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,importsequencenumber,overriddencreatedon,statecode;" +
"$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode,statuscode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode;$filter=(statecode eq 0))," +
"aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
"aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
"aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
"$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
            "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;" +
            "$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0)),&$filter=(aacn_assessment_review_activityid eq '{0}')";

        public static string GetQuestionMaster = "aacn_questions?$select=aacn_question,aacn_questionid&$filter=(statecode eq 0)"; //Active
        public static string GetAnswerMaster = "aacn_answers?$select=aacn_answer,aacn_answerid&$filter=(statecode eq 0)"; //Active

        public static string GetPositionByName = "positions?$select=name,positionid&$filter=(statecode eq 0)"; //Active records

        public static string GetActivityReviewbyId = "aacn_assessment_review_activities?$select=_aacn_assessment_value,aacn_accepted_date,aacn_assigned_date,aacn_completed_date,aacn_rejected_date,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_name,_aacn_organization_value,aacn_rejected_reason,aacn_request_status,_aacn_reviewer_value,_aacn_reviewers_response_value,_aacn_unit_value,_aacn_user_response_value,aacn_reviewers_response,createdon&$filter=(aacn_assessment_review_activityid eq {0})";


        public static string GetResponseDataRoot = "aacn_responses?$select=aacn_aacn_assigned_date,_aacn_assessment_value,_aacn_event_value,_aacn_facility_name_value,aacn_nf_minipeak_response_status,aacn_rejection_reason,aacn_response_message,aacn_response_number,aacn_responseid,_aacn_session_value,_aacn_unit_name_value&" +
            "$expand=aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text,_aacn_response_value,aacn_response_line_number,aacn_response_line_text_area,aacn_response_lineid,aacn_response_text,_aacn_speaker_value,importsequencenumber,overriddencreatedon,utcconversiontimezonecode,versionnumber;$filter=(statecode eq 0))&$filter=(aacn_responseid eq {0})";

        public static string GetResponseDataRootAllLines = "aacn_responses?$select=aacn_aacn_assigned_date,_aacn_assessment_value,_aacn_event_value,_aacn_facility_name_value,aacn_nf_minipeak_response_status,aacn_rejection_reason,aacn_response_message,aacn_response_number,aacn_responseid,_aacn_session_value,_aacn_unit_name_value&" +
            "$expand=aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text,_aacn_response_value,aacn_response_line_number,aacn_response_line_text_area,aacn_response_lineid,aacn_response_text,statecode)&$filter=(aacn_responseid eq {0})";

        public static string GetEntityRecordsbyGUId = "{0}?$select={1}&$filter=(Microsoft.Dynamics.CRM.In(PropertyName='{2}',PropertyValues=[{3}]))";

        public static string VerifyReviewerResponses = "aacn_assessment_review_activities?$select=_aacn_assessment_value,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_request_status,_aacn_reviewer_value,_aacn_reviewer_module_value&$filter=(_aacn_reviewer_value eq {0})";

        public static string GetGroupNameByRefId = "aacn_assessment_lines?$select=_aacn_assessment_line_reference_value,aacn_assessment_line_number,aacn_tool_tip_description&$expand=aacn_assessment_line_reference($select=aacn_assessment_line_title,aacn_tool_tip_description,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_line_number)&$filter=(aacn_assessment_lineid eq {0})";


        public static readonly string GetUnitModule_Manager = "aacn_assessments?$select=aacn_assessment_description,statecode,aacn_assessment_number,aacn_assessment_title,aacn_survey_provider,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,modifiedon,overriddencreatedon&$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,statecode,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon;" +
         "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,statecode,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_createdby_value,createdon,modifiedon)," +
         "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,_createdonbehalfby_value;" +
         "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_createdby_value,createdon)," +
         "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,overriddencreatedon;" +
         "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon,modifiedon)," +
         "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon;" +
         "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,statecode,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,createdon;" +
         "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon,modifiedon,_modifiedonbehalfby_value)," +
         "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,statecode,aacn_assessment_line_control,statecode,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,_createdby_value,createdon,timezoneruleversionnumber,utcconversiontimezonecode,versionnumber;" +
         "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,statecode,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,createdon)))))))&$filter=(aacn_assessmentid eq '{0}')";

        public static string GetAssessmentsByCurrentYear = "aacn_assessments?$select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_reviewer_module_value,_aacn_survey_provider_value,_aacn_unit_module_value,_aacn_year_value&$filter=(_aacn_year_value eq {0})";


        public static string GetReviewerModule_Manager = "aacn_assessment_review_activities?$select=aacn_accepted_date,aacn_assessment_review_activityid,aacn_request_status,_aacn_unit_module_value,aacn_assessment_review_activity_number" +
           "&$expand=aacn_reviewer_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,importsequencenumber,overriddencreatedon,statecode;" +
           "$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,overriddencreatedon,statecode,statuscode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;" +
           "$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0)),&$filter=(aacn_assessment_review_activityid eq '{0}')";

        public static string GetUnitModuleFromActivity = "aacn_assessment_review_activities?$select=aacn_accepted_date,aacn_assessment_review_activityid,aacn_request_status,_aacn_unit_module_value,aacn_assessment_review_activity_number" +
           "&$expand=aacn_unit_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_survey_provider_value,_createdby_value,createdon,_createdonbehalfby_value,importsequencenumber,overriddencreatedon,statecode;" +
           "$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_banner_description,aacn_assessment_max_responses,overriddencreatedon,statecode,statuscode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,_aacn_assessment_lineid_skip_to_value,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode,statuscode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_banner_description,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_lineid_skip_to,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_banner_description,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_lineid_skip_to,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;$filter=(statecode eq 0))," +
           "aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_banner_description,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_assessment_line_reference($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_banner_description,aacn_assessment_max_responses,statecode;" +
           "$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_lineid_skip_to,aacn_assessment_line_option_required,_aacn_assessment_lineid_skip_to_value,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,statecode;" +
           "$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0));$filter=(statecode eq 0)),&$filter=(aacn_assessment_review_activityid eq '{0}')";

        public static string GetUserResponse_Manager = "aacn_assessment_review_activities?$select=aacn_accepted_date,_aacn_assessment_value,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_assigned_date,aacn_name,_aacn_organization_value,aacn_rejected_date,aacn_rejected_reason,aacn_request_status,_aacn_reviewer_value,_aacn_reviewer_module_value,_aacn_reviewers_response_value,_aacn_unit_value,_aacn_unit_module_value,_aacn_user_response_value,_createdby_value,createdon,overriddencreatedon,_ownerid_value,_owningbusinessunit_value,statecode&$expand=" +
            "aacn_user_response($select=aacn_aacn_assigned_date,_aacn_assessment_value,_aacn_event_value,_aacn_facility_name_value,_aacn_instance_id_value,_aacn_member_value,aacn_rejection_reason,aacn_response_message,aacn_response_number,aacn_responseid,_aacn_session_value,_aacn_survey_provider_value,_aacn_unit_name_value,importsequencenumber,statecode;" +
            "$expand=aacn_member($select=aacn_contact_number,contactid,firstname,lastname),aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text," +
            "_aacn_response_value,aacn_response_line_number,aacn_response_line_text_area,aacn_response_lineid,aacn_response_text,_aacn_speaker_value,overriddencreatedon," +
            "statecode;$expand=aacn_assesment_line($select=aacn_tool_tip_description);$filter=(statecode eq 0)))&$filter=(aacn_assessment_review_activityid eq {0})";

        public static string GetUserResponse_UnitModdoule = "aacn_assessment_review_activities?$select=aacn_accepted_date,_aacn_assessment_value,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_assigned_date,aacn_name,_aacn_organization_value,aacn_rejected_date,aacn_rejected_reason,aacn_request_status,_aacn_reviewer_value,_aacn_reviewer_module_value,_aacn_reviewers_response_value,_aacn_unit_value,_aacn_unit_module_value,_aacn_user_response_value,_createdby_value,createdon,overriddencreatedon,_ownerid_value,_owningbusinessunit_value,statecode&$expand=" +
            "aacn_user_response($select=aacn_aacn_assigned_date,_aacn_assessment_value,_aacn_event_value,_aacn_facility_name_value,_aacn_instance_id_value,_aacn_member_value,aacn_rejection_reason,aacn_response_message,aacn_response_number,aacn_responseid,_aacn_session_value,_aacn_survey_provider_value,_aacn_unit_name_value,importsequencenumber,statecode;" +
            "$expand=aacn_member($select=aacn_contact_number,contactid,firstname,lastname),aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text," +
            "_aacn_response_value,aacn_response_line_number,aacn_response_line_text_area,aacn_response_lineid,aacn_response_text,_aacn_speaker_value,overriddencreatedon," +
            "statecode;$filter=(statecode eq 0));$filter=(statecode eq 0))&$filter=(_aacn_unit_value eq {0} and _aacn_unit_module_value eq {1})";

        public static string GetReviewerResponse_Manager = "aacn_assessment_review_activities?$select=aacn_accepted_date,_aacn_assessment_value,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_assigned_date,aacn_name,_aacn_organization_value,aacn_rejected_date,aacn_rejected_reason,aacn_request_status,_aacn_reviewer_value,_aacn_reviewer_module_value,_aacn_reviewers_response_value,_aacn_unit_value,_aacn_unit_module_value,_aacn_user_response_value,_createdby_value,createdon,overriddencreatedon,_ownerid_value,_owningbusinessunit_value,statecode" +
            "&$expand=aacn_reviewers_response($select=aacn_aacn_assigned_date,_aacn_assessment_value,_aacn_event_value,_aacn_facility_name_value,_aacn_instance_id_value,_aacn_member_value,aacn_rejection_reason,aacn_response_message,aacn_response_number,aacn_responseid,_aacn_session_value,_aacn_survey_provider_value,_aacn_unit_name_value,importsequencenumber,statecode;$expand=aacn_member($select=aacn_contact_number,contactid,firstname,lastname),aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text,_aacn_response_value," +
            "aacn_response_line_number,aacn_response_line_text_area,aacn_response_lineid,aacn_response_text,_aacn_speaker_value,overriddencreatedon,statecode;$filter=(statecode eq 0));" +
            "$filter=(statecode eq 0))&$filter=(aacn_assessment_review_activityid eq {0})";

        public static string getReviewAcitivitesByUnitId = "aacn_assessment_review_activities?$select=_aacn_assessment_value,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_name,_aacn_organization_value,aacn_rejected_reason,_aacn_unit_value,aacn_request_status,_aacn_reviewer_value,aacn_assigned_date,_aacn_reviewers_response_value,_aacn_user_response_value,createdon,modifiedon&$expand=aacn_assessment($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid),aacn_unit_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid),aacn_reviewer_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid)" +
            "&$filter=(_aacn_unit_value eq {0} and aacn_request_status ne ********* and _aacn_reviewer_module_value ne null)";


        public static string getReviewAcitivitesByUnitIdUpdated = "aacn_assessment_review_activities?$select=aacn_accepted_date,aacn_module_completion_status,_aacn_assessment_value,_aacn_unit_value,aacn_assessment_review_activity_number,aacn_assessment_review_activityid,aacn_assigned_date,aacn_name,_aacn_organization_value,aacn_rejected_date,aacn_rejected_reason,aacn_request_status,_aacn_reviewer_value,_aacn_reviewer_module_value,_aacn_reviewers_response_value,_aacn_unit_module_value,_aacn_user_response_value,statecode,statuscode&" +
         "$expand=aacn_unit_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_reviewer_module_value,_aacn_survey_provider_value,_aacn_unit_module_value,overriddencreatedon,statecode,statuscode),aacn_reviewer_module($select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_reviewer_module_value,_aacn_survey_provider_value,_aacn_unit_module_value," +
         "overriddencreatedon,_owningbusinessunit_value,statecode,statuscode)," +
         "aacn_Unit($select=accountid,accountnumber,name)," +
         "aacn_reviewer($select=aacn_contact_number,contactid,firstname,lastname)&$filter=(_aacn_unit_value eq {0} and _aacn_year_value eq {1})";


        public static string verifyUnitResponses = "aacn_assessment_review_activities?$select=_aacn_assessment_value,aacn_assessment_review_activity_number," +
            "aacn_assessment_review_activityid,_aacn_organization_value,aacn_request_status,_aacn_reviewer_value,_aacn_reviewer_module_value,_aacn_unit_value,_aacn_unit_module_value,_aacn_user_response_value&$expand=aacn_reviewer_module($select=aacn_assessment_number,aacn_assessment_title,aacn_assessmentid)&$filter=(_aacn_unit_value eq {0} and _aacn_reviewer_module_value ne null)";

        public static string getResponseLinesByResponseLineid = "aacn_response_lines?$select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text,_aacn_response_value," +
            "aacn_response_line_text_area,aacn_response_lineid,aacn_response_text&$filter=(Microsoft.Dynamics.CRM.In(PropertyName='aacn_response_lineid',PropertyValues=[{0}]) and statecode eq 0)";


        public static string getResponseActiveResponseLines = "aacn_response_lines?$select=aacn_response_lineid&$filter=(Microsoft.Dynamics.CRM.In(PropertyName='aacn_response_lineid',PropertyValues=[{0}]) and statecode eq 0)";


        public static string GetResponseDataRootByGuid = "aacn_responses?$select=aacn_aacn_assigned_date,_aacn_assessment_value,_aacn_event_value,_aacn_facility_name_value,aacn_nf_minipeak_response_status,aacn_rejection_reason,aacn_response_message,aacn_response_number,aacn_responseid,_aacn_session_value,_aacn_unit_name_value&" +
           "$expand=aacn_response_line_response_aacn_response($select=_aacn_answer_value,_aacn_assesment_line_value,_aacn_assessment_line_option_value,_aacn_question_value,aacn_question_text,_aacn_response_value,aacn_response_line_number,aacn_response_line_text_area,aacn_response_lineid,aacn_response_text,_aacn_speaker_value,importsequencenumber,overriddencreatedon,utcconversiontimezonecode,versionnumber)&" +
            "$filter=(Microsoft.Dynamics.CRM.In(PropertyName='aacn_response_lineid',PropertyValues=[{0}]))";

        public static string GetAuditData = "aacn_audit_report_datas?$select=aacn_application_id,aacn_audit_report_dataid,aacn_assessment_review_activity_number&$expand=aacn_unit($select=accountnumber,name;$expand=parentaccountid($select=accountid,accountnumber,name)),aacn_reviewer($select=aacn_contact_number,emailaddress1,firstname,lastname),aacn_unit_module($select=aacn_assessment_title),aacn_year($select=aacn_year;$filter=(aacn_year eq '{0}'))";
        public static string GetResponseLines = "aacn_response_lines?$select=aacn_response_lineid&$filter=(_aacn_response_value eq {0})";

        public static string GetAuditCompletedData = "aacn_assessment_review_activities?$select=aacn_application_id,aacn_assessment_review_activityid,aacn_assessment_review_activity_number&$expand=aacn_Unit($select=accountnumber,name;$expand=parentaccountid($select=accountid,accountnumber,name)),aacn_reviewer($select=aacn_contact_number,emailaddress1,firstname,lastname),aacn_unit_module($select=aacn_assessment_title),aacn_year($select=aacn_year)&$filter=(aacn_request_status eq *********) and (aacn_year/aacn_year eq '{0}')";
        public static string GetAsrReviewerResponseandLines = "aacn_assessment_review_activities?$select=aacn_assessment_review_activityid,_aacn_reviewers_response_value&$expand=aacn_reviewers_response($select=aacn_responseid;$expand=aacn_response_line_response_aacn_response($select=aacn_response_lineid))&$filter=(_aacn_user_response_value eq {0})";

        public static string GetOrganizationbyunitid = "accounts?$select=accountid,_parentaccountid_value&$filter=(accountid eq {0})";

        public static string GetAssessmentDataBySessionCode = "aacn_assessments?$select=aacn_assessment_description,aacn_assessment_number,aacn_assessment_title,aacn_assessmentid,aacn_passing_score,_aacn_reviewer_module_value,_aacn_survey_provider_value,_aacn_unit_module_value,_aacn_year_value&$expand=aacn_assessment_line_assessment_header_aacn_as($select=_aacn_assessment_header_value,aacn_assessment_line_control,aacn_assessment_line_number,aacn_assessment_line_order,aacn_assessment_line_orientation,_aacn_assessment_line_question_value,aacn_assessment_line_question_text,_aacn_assessment_line_reference_value,aacn_assessment_line_required,aacn_assessment_line_title,aacn_assessment_line_type,aacn_assessment_lineid,aacn_assessment_max_responses,aacn_banner_description,aacn_tool_tip_description;$expand=aacn_assessment_line_option_assessment_line_aa($select=_aacn_assessment_line_value,aacn_assessment_line_option_number,aacn_assessment_line_option_required,aacn_assessment_line_option_score,aacn_assessment_line_option_sort_order,aacn_assessment_line_optionid,_aacn_assessment_line_question_value,_aacn_assessment_line_question_option_value,aacn_assessment_line_question_option_text,aacn_assessment_line_question_text,_aacn_assessment_lineid_skip_to_value))&$filter=(aacn_assessment_title eq '{0}')";
        public static string GetResponsesBySessionCode = "aacn_responses?$select=_aacn_assessment_value,aacn_responseid,_aacn_survey_provider_value&$expand=aacn_assessment($select=aacn_assessment_title,aacn_assessmentid),aacn_response_line_response_aacn_response($select=aacn_response_lineid,aacn_response_text,aacn_question_text)&$filter=(aacn_assessment/aacn_assessment_title eq '{0}') and (aacn_response_line_response_aacn_response/any(o1:(o1/aacn_response_lineid ne null)))";

        public static string GetResponseCounter = "aacn_assessments?$select=aacn_assessment_title,aacn_assessmentid,aacn_passing_score&$expand=aacn_assessment_line_assessment_header_aacn_as($select=aacn_assessment_lineid,aacn_assessment_line_question_text;$expand=aacn_assessment_line_option_assessment_line_aa($select=aacn_assessment_line_optionid,aacn_assessment_line_question_option_text;$expand=aacn_response_line_assessment_line_option_aacn($select=aacn_response_lineid)))&$filter=(aacn_assessment_title eq '{0}') and (aacn_assessment_line_assessment_header_aacn_as/any(o1:(o1/aacn_assessment_lineid ne null) and (o1/aacn_assessment_line_option_assessment_line_aa/any(o2:(o2/aacn_assessment_line_optionid ne null)))))";
        public static string CheckSessionCount = "aacn_assessments?$select=aacn_assessment_title,aacn_assessmentid,aacn_passing_score&$expand=aacn_assessment_line_assessment_header_aacn_as($select=aacn_assessment_line_question_text,aacn_assessment_lineid;$expand=aacn_response_line_assesment_line_aacn_assessm($select=aacn_response_lineid,aacn_question_text,aacn_response_text))&$filter=(aacn_assessment_title eq '{0}') and (aacn_assessment_line_assessment_header_aacn_as/any(o1:(o1/aacn_assessment_lineid ne null) and (o1/aacn_response_line_assesment_line_aacn_assessm/any(o2:(o2/aacn_response_lineid ne null)))))";
        public static string CheckUserEmail = "aacn_assessments?$select=aacn_assessment_title,aacn_assessmentid&$expand=aacn_assessment_line_assessment_header_aacn_as($select=aacn_assessment_line_question_text,aacn_assessment_lineid;$expand=aacn_response_line_assesment_line_aacn_assessm($select=aacn_question_text,aacn_response_lineid,aacn_response_text;$filter=(aacn_question_text eq 'Email+Address')))&$filter=(aacn_assessment_title eq '{0}')";

        public static string GetYear = "aacn_years?$select=aacn_name,aacn_year,aacn_yearid&$filter=(aacn_year eq '{0}')";
        #endregion
        #endregion

        public static class constants
        {
            public static class postionConstants
            {
                public static String ORGANIZATION = "Organization";
                public static String UNIT = "Unit";
                public static String MANAGER = "Manager";
                public static String REVIEWER = "Reviewer";
                public static String POINTOFCONTACT = "PointOfContact";
            }

            public static class AssessmentConstants
            {
                public const string Demographics = "Demographics";
            }

            public static class NTIConstants
            {
                public const string Month = "May";
                public const string NTIRegistrationSurveyProvider = "a1105239-5a26-f011-8c4e-6045bd046b8e";
            }

        }
        public enum instanceStatus
        {
            COMPLETED = *********,
            INPROGRESS = *********
        }
    }
  
       
    public class ActionStatusConstants
    {
        public static String READY = "Ready";
        public static String ASSIGNED = "Assign";
        public static String ACCEPTED = "Accept";
        public static String REJECTED = "Reject";
        public static String IN_PROGRESS = "In-Progress";
        public static String COMPLETE = "Complete";
        public static String SUBMITTED = "Submit";
    }
    public enum RequestStatus
    {
        Ready = *********,
        Assigned = *********,
        Accepted = *********,
        Rejected = *********,
        InProgress = *********,
        Complete = *********,
        submitted = *********,
        all = *********
    }
    public enum AccountRoleCode
    {
        Manager = 1,
        Reviewer = 2
    }


    public enum StatusCode
    {
        Active = 0,
        Inactive = 1
    }
    public static class EnumStatusHelper
    {
        public static int? GetRequestStatusValue(string label)
        {
            if (Enum.TryParse(typeof(RequestStatus), label.Replace("-", ""), true, out var result))
            {
                return (int)result;
            }
            return null; 
        }

      
    }
}





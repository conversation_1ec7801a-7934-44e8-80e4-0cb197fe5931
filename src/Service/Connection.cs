﻿

namespace AACN.Services
{
    using AACN.API.Service;
    using System;
    using System.Threading.Tasks;
    using Microsoft.Identity.Client;
    using System.Collections.Concurrent;
    using Microsoft.PowerPlatform.Dataverse.Client;
    using Microsoft.Xrm.Sdk.Query;
    using Microsoft.Xrm.Sdk;
    using Microsoft.OpenApi.Expressions;
    using System.Data.Entity.Core.Objects.DataClasses;
    using QueryExpression = Microsoft.Xrm.Sdk.Query.QueryExpression;

    public class Connection
    {

        public static readonly string CrmApiUrl = ConnectionConfig.configData.GetSection("AppConnection:CRM:CrmApiUrl").Value;
        public static readonly string CrmResourceUrl = ConnectionConfig.configData.GetSection("AppConnection:CRM:CrmResourceUrl").Value;
        public static readonly string CrmclientId = ConnectionConfig.configData.GetSection("AppConnection:CRM:CrmclientId").Value;
        public static readonly string CrmClientSecret = ConnectionConfig.configData.GetSection("AppConnection:CRM:CrmClientSecret").Value;
        public static readonly string authority = ConnectionConfig.configData.GetSection("AppConnection:CRM:authority").Value;
        public static readonly string PEBusinessRule_AzureEndpoint = ConnectionConfig.configData.GetSection("AppConnection:CRM:PEBusinessRule_AzureEndpoint").Value;
        public static readonly string CEBusinessRule_AzureEndpoint = ConnectionConfig.configData.GetSection("AppConnection:CRM:CEBusinessRule_AzureEndpoint").Value;
        public static readonly string CE_SubmitResponse_AzureEndpoint = ConnectionConfig.configData.GetSection("AppConnection:CRM:CE_SubmitResponse_AzureEndpoint").Value;
        public static readonly string PE_SubmitResponse_AzureEndpoint = ConnectionConfig.configData.GetSection("AppConnection:CRM:PE_SubmitResponse_AzureEndpoint").Value;
        public static readonly string ClientId = ConnectionConfig.configData.GetSection("AppConnection:HWEAT:ClientId").Value;
        public static readonly string ClientSecret = ConnectionConfig.configData.GetSection("AppConnection:HWEAT:ClientSecret").Value;
        public static readonly string SurveyResponse_Endpoint = ConnectionConfig.configData.GetSection("AppConnection:HWEAT:SurveyResponse_Endpoint").Value;
        public static readonly string ResourceURL = ConnectionConfig.configData.GetSection("AppConnection:HWEAT:ResourceURL").Value;
        public static readonly string Scope = ConnectionConfig.configData.GetSection("AppConnection:HWEAT:Scope").Value;
        public static readonly string JWTKey = ConnectionConfig.configData.GetSection("AppConnection:BEACON:JWTKey").Value;

        public static string CrmBearerToken { get; set; } = null;
        public static string HweatBearerToken { get; set; } = null;



        #region oldCode
        //public static async Task<string> AccessTokenGenerator()
        //{
        //    try
        //    {
        //        var app = ConfidentialClientApplicationBuilder.Create(CrmclientId)
        //            .WithClientSecret(CrmClientSecret)
        //            .WithAuthority(new Uri(authority))
        //            .Build();
        //        var scopes = new[] { $"{CrmResourceUrl}/.default" };
        //        var result = await app.AcquireTokenForClient(scopes).ExecuteAsync();
        //        return result.AccessToken;
        //    }
        //    catch (MsalServiceException ex)
        //    {
        //        return null;
        //    }
        //    catch (Exception ex)
        //    {
        //        Console.WriteLine($"Exception occurred: {ex.Message}");
        //        return null;
        //    }
        //}

        #endregion

        public static async Task<string> AccessTokenGenerator()
        {
            var key = $"CRMService_{CrmclientId}";
            return await TokenCache.GetAccessToken(key, async () =>
            {
                try
                {
                    var app = ConfidentialClientApplicationBuilder.Create(CrmclientId)
                        .WithClientSecret(CrmClientSecret)
                        .WithAuthority(new Uri(authority))
                        .Build();

                    var scopes = new[] { $"{CrmResourceUrl}/.default" };
                    var result = await app.AcquireTokenForClient(scopes).ExecuteAsync();

                    return result.AccessToken;
                }
                catch (MsalServiceException ex)
                {
                    Console.WriteLine($"MsalServiceException occurred: {ex.Message}");
                    throw;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Exception occurred: {ex.Message}");
                    throw;
                }
            });
        }

        public static async Task<string> AccessTokenGenerator_Hweat()
        {
            var key = $"CRMService_{ClientId}";
            return await TokenCacheHweat.GetAccessToken_hweat(key, async () =>
            {
                try
                {
                    var app = ConfidentialClientApplicationBuilder.Create(ClientId)
                        .WithClientSecret(ClientSecret)
                        .WithAuthority(new Uri(ResourceURL))
                        .Build();

                    var scopes = new[] { $"{Scope}/.default" };
                    var result = await app.AcquireTokenForClient(scopes).ExecuteAsync();

                    return result.AccessToken;
                }
                catch (MsalServiceException ex)
                {
                    Console.WriteLine($"MsalServiceException occurred: {ex.Message}");
                    throw;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Exception occurred: {ex.Message}");
                    throw;
                }
            });
        }

        public static class TokenCache
        {
            private static readonly ConcurrentDictionary<string, (string AccessToken, DateTimeOffset ExpiryTime)> _cache = new ConcurrentDictionary<string, (string, DateTimeOffset)>();

            public static async Task<string> GetAccessToken(string key, Func<Task<string>> acquireToken)
            {
                if (_cache.TryGetValue(key, out var tokenInfo) && tokenInfo.ExpiryTime > DateTimeOffset.UtcNow.AddMinutes(1))
                {
                    return tokenInfo.AccessToken;
                }
                try
                {
                    var accessToken = await acquireToken();
                    var expiryTime = DateTimeOffset.UtcNow.AddMinutes(19); // Assuming token expiry time is 20 minutes (19 minutes is used as a buffer)
                    _cache[key] = (accessToken, expiryTime);
                    return accessToken;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to acquire token: {ex.Message}");
                    return null;
                }
            }
        }

        public static class TokenCacheHweat
        {
            private static readonly ConcurrentDictionary<string, (string AccessToken, DateTimeOffset ExpiryTime)> _cache = new ConcurrentDictionary<string, (string, DateTimeOffset)>();

            public static async Task<string> GetAccessToken_hweat(string key, Func<Task<string>> acquireToken)
            {
                if (_cache.TryGetValue(key, out var tokenInfo) && tokenInfo.ExpiryTime > DateTimeOffset.UtcNow.AddMinutes(1))
                {
                    return tokenInfo.AccessToken;
                }
                try
                {
                    var accessToken = await acquireToken();
                    var expiryTime = DateTimeOffset.UtcNow.AddMinutes(19); // Assuming token expiry time is 20 minutes (19 minutes is used as a buffer)

                    _cache[key] = (accessToken, expiryTime);

                    return accessToken;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to acquire token: {ex.Message}");
                    return null;
                }
            }
        }

        public static class CRMServiceExtension
        {
            public static ServiceClient GetCRMConnection()
            {
                Microsoft.PowerPlatform.Dataverse.Client.ServiceClient _service = null;
                try
                {
                    Microsoft.PowerPlatform.Dataverse.Client.Model.ConnectionOptions connectionobject = new Microsoft.PowerPlatform.Dataverse.Client.Model.ConnectionOptions();
                    //Add Details to Connection Object;
                    connectionobject.ServiceUri = new System.Uri(CrmResourceUrl);
                    connectionobject.ClientId = CrmclientId;
                    connectionobject.ClientSecret = CrmClientSecret;
                    connectionobject.SkipDiscovery = true;
                    connectionobject.AuthenticationType = Microsoft.PowerPlatform.Dataverse.Client.AuthenticationType.ClientSecret;
                    connectionobject.RequireNewInstance = true;
                    _service = new ServiceClient(connectionobject);
                    if (!_service.IsReady)
                    {
                        throw new Exception("Authentication Failed!");
                    }
                    else
                    {
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Exception " + ex.Message);
                }
                return _service;
            }

            internal static EntityCollection GetEntityCollection(IOrganizationService crmService, QueryExpression qryExp)
            {
                qryExp.PageInfo = new PagingInfo()
                {
                    PageNumber = 1,
                    Count = 5000 // Set your page size, maximum is 5000
                };

                EntityCollection results = new EntityCollection();
                int totalRecordCount = 0;
                do
                {
                    // Retrieve the page.
                    EntityCollection partialResults = crmService.RetrieveMultiple(qryExp);
                    totalRecordCount += partialResults.Entities.Count;
                    if (partialResults.Entities.Count > 0)
                        results.Entities.AddRange(partialResults.Entities);
                    // Process the page of records here.

                    // Check for more records, if it returns less than the page size, then it's the last page
                    if (partialResults.MoreRecords)
                    {
                        // Increment the page number to retrieve the next page.
                        qryExp.PageInfo.PageNumber++;
                        qryExp.PageInfo.PagingCookie = partialResults.PagingCookie;
                    }
                    else
                    {
                        break;
                    }

                } while (true);
                return results;
            }
        }

    }

}


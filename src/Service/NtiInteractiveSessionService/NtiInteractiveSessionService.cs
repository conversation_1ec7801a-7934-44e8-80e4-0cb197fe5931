﻿namespace AACN.API.Service.NtiInteractiveSessionService
{
    using AACN.API.Helper;
    using AACN.API.Model;
    using AACN.Services;
    using Microsoft.Extensions.Logging;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;


    public class NtiInteractiveSessionService : BaseService, INtiInteractiveSessionService
    {

        private readonly ILogger<NtiInteractiveSessionService> _logger;
        public NtiInteractiveSessionService(ILogger<NtiInteractiveSessionService> logger)
        {
            _logger = logger;
        }

        public async Task<NtiFormSessionResponseModel> GetFormBySessionCode(string sessionCode)
        {
            try
            {
                _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
                _logger.LogInformation("SessionCode : " + sessionCode);

                var sessionResponseModel = new NtiFormSessionResponseModel();
                var assessments = new List<AssessmentValue>();

                HttpResponseMessage sessionResponse = GetAssessmentDataBySessionCode(sessionCode);

                if (sessionResponse.IsSuccessStatusCode)
                {
                    sessionResponseModel = JsonConvert.DeserializeObject<NtiFormSessionResponseModel>(Utility.RemoveJsonNulls(await sessionResponse.Content.ReadAsStringAsync()));
                    AssessmentValue assessment = sessionResponseModel.Value.FirstOrDefault();
                    foreach (var item in sessionResponseModel.Value.ToList())
                    {
                        assessment.AssessmentLines = SortLines(item.AssessmentLines);
                    }
                    sessionResponseModel.Value = new List<AssessmentValue>();
                    sessionResponseModel.Value.Add(assessment);

                    HttpResponseMessage counterResponse = GetResponseCountForAllSession(sessionCode);

                    SessionResponseCounterModel sessionCounterModel = new SessionResponseCounterModel();
                    if (counterResponse.IsSuccessStatusCode)
                    {
                        sessionCounterModel = JsonConvert.DeserializeObject<SessionResponseCounterModel>(Utility.RemoveJsonNulls(await counterResponse.Content.ReadAsStringAsync()));
                        if (sessionCounterModel.Value.Count > 0)
                        {
                            CounterModel counterModel = new CounterModel();
                            List<DayQuestionTextValues> dayQuestionTextValuesList = new List<DayQuestionTextValues>();

                            List<AacnAssessmentLineAssessmentHeaderAacnASession> assessmentLines = sessionCounterModel.Value.FirstOrDefault().AssessmentLineAssessmentHeaderAacnAs;
                            List<AacnAssessmentLineAssessmentHeaderAacnASession> filteredAssessmentLines = assessmentLines.Where(x => x.AssessmentLineQuestionText.Contains(Utility.constants.NTIConstants.Month)).ToList();

                            dayQuestionTextValuesList = SetSessionCountDayWise(filteredAssessmentLines);

                            counterModel.MaxResponse = sessionCounterModel.Value.FirstOrDefault().PassingScore.ToString();
                            counterModel.DayQuestionTextValues = dayQuestionTextValuesList;

                            sessionResponseModel.Counter = counterModel;
                        }
                    }
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return sessionResponseModel;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Exception :" + ex.Message);
                LoggerHelper.LogException(_logger, ex);
                return new NtiFormSessionResponseModel();
            }
        }

        public async Task<NtiFormSessionResponse> CreateSessionResponse(NtiFormSessionRequest ntiFormSessionRequestModel)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            NtiFormSessionResponse returnResponseModel = new NtiFormSessionResponse();
            ResponseRequestModel responseCreateModel = new ResponseRequestModel();
            List<ResponseLineBatchCreateModel> _responseLines = new List<ResponseLineBatchCreateModel>();
            try
            {
                _logger.LogInformation(LoggerHelper.LogObjectValue(ntiFormSessionRequestModel, JsonConvert.SerializeObject(ntiFormSessionRequestModel)));

                bool hasEmail = await CheckUserEmail(ntiFormSessionRequestModel);
                bool hasCount = await CheckSessionCount(ntiFormSessionRequestModel);

                if (!hasEmail)
                {
                    if (hasCount)
                    {

                        var assessmentResponseModel = new NtiFormSessionResponseModel();
                        var assessmentLine = string.Empty;
                        var assessmentLineOptionId = string.Empty;
                        var query = string.Format(Utility.GetAssessmentDataBySessionCode, ntiFormSessionRequestModel.SessionId);
                        HttpResponseMessage response = GetRecords(query);

                        if (response.IsSuccessStatusCode)
                        {
                            assessmentResponseModel = JsonConvert.DeserializeObject<NtiFormSessionResponseModel>(Utility.RemoveJsonNulls(await response.Content.ReadAsStringAsync()));

                            if (assessmentResponseModel.Value.Count > 0)
                            {
                                AssessmentValue assessment = assessmentResponseModel.Value.FirstOrDefault();

                                AacnAssessmentLineAssessmentHeaderAacnAQ selectedAssessment = assessment.AssessmentLines.Where(x => x.AssessmentLineQuestionText == ntiFormSessionRequestModel.Date).FirstOrDefault();
                                if (selectedAssessment.AssessmentLineId != null)
                                    assessmentLine = selectedAssessment.AssessmentLineId;

                                AacnAssessmentLineOptionAssessmentLineAaOption selectedAssessmentLineOption = selectedAssessment.AssessmentLineOptions.Where(x => x.AssessmentLineQuestionOptionText == ntiFormSessionRequestModel.SessionTime).FirstOrDefault();
                                if (selectedAssessmentLineOption.AssessmentLineOptionId != null)
                                    assessmentLineOptionId = selectedAssessmentLineOption.AssessmentLineOptionId;

                                responseCreateModel.SurveyProvider = $"/aacn_survey_providers({Utility.constants.NTIConstants.NTIRegistrationSurveyProvider})";
                                responseCreateModel.Assessment = $"/aacn_assessments({assessmentResponseModel.Value[0].AssessmentId})";
                                HttpResponseMessage createResponse = await CreateRecordWithRetry("aacn_responses", JsonConvert.SerializeObject(responseCreateModel), _logger);
                                if (createResponse.IsSuccessStatusCode)
                                {
                                    var responseId = createResponse.Headers.GetValues("OData-EntityId").FirstOrDefault().ToString().Split('[', '(', ')', ']')[1].ToString();

                                    _responseLines = SetResponseLine(ntiFormSessionRequestModel, responseId, assessmentLine, assessmentLineOptionId);

                                    HttpResponseMessage BulkCreateResponse = await CreateBatchRecords("aacn_response_lines", _responseLines, _logger);

                                    if (BulkCreateResponse.IsSuccessStatusCode)
                                    {
                                        returnResponseModel.Status = (int)HttpStatusCode.OK;
                                    }
                                    else
                                    {
                                        returnResponseModel.Status = (int)BulkCreateResponse.StatusCode;
                                        returnResponseModel.Error = BulkCreateResponse.Content.ReadAsStringAsync().ToString();
                                    }
                                }
                                else
                                {
                                    returnResponseModel.Status = (int)createResponse.StatusCode;
                                    returnResponseModel.Error = createResponse.Content.ReadAsStringAsync().ToString();
                                }
                            }
                            else
                            {
                                returnResponseModel.Status = (int)HttpStatusCode.OK;
                                returnResponseModel.Error = "No assessment found for session code - " + ntiFormSessionRequestModel.SessionId;
                            }
                        }
                        else
                        {
                            returnResponseModel.Status = (int)response.StatusCode;
                            returnResponseModel.Error = response.Content.ReadAsStringAsync().ToString();
                        }
                    }
                    else
                    {
                        returnResponseModel.Status = (int)HttpStatusCode.BadRequest;
                        returnResponseModel.Error = "SessionIsFull";
                    }
                }
                else
                {
                    returnResponseModel.Status = (int)HttpStatusCode.BadRequest;
                    returnResponseModel.Error = "This emailaddress has already been registered.";
                }

                _logger.LogInformation(JsonConvert.SerializeObject(returnResponseModel));
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));

                return returnResponseModel;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Exception :" + ex.Message);
                LoggerHelper.LogException(_logger, ex);
                returnResponseModel.Status = (int)HttpStatusCode.InternalServerError;
                returnResponseModel.Error = ex.Message;
                return returnResponseModel;
            }
        }

        public async Task<NtiSessionResponseModel> GetResponseBySessionCode(string sessionCode)
        {
            try
            {
                _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
                _logger.LogInformation("SessionCode : " + sessionCode);
                var responseModel = new NtiSessionResponseModel();

                var query = string.Format(Utility.GetResponsesBySessionCode, sessionCode);
                HttpResponseMessage response = GetRecords(query);

                if (response.IsSuccessStatusCode)
                    responseModel = JsonConvert.DeserializeObject<NtiSessionResponseModel>(Utility.RemoveJsonNulls(await response.Content.ReadAsStringAsync()));

                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));

                return responseModel;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Exception :" + ex.Message);
                LoggerHelper.LogException(_logger, ex);
                return new NtiSessionResponseModel();
            }
        }
        #region private methods
        private List<QuestionMaster> GetAllQuestion()
        {
            QuestionAllModel questionData = new QuestionAllModel();
            try
            {
                string questionMasterQuery = string.Format(Utility.GetQuestionMaster);
                HttpResponseMessage answerResponse = GetRecords(questionMasterQuery);
                if (answerResponse.IsSuccessStatusCode)
                {
                    questionData = JsonConvert.DeserializeObject<QuestionAllModel>(answerResponse.Content.ReadAsStringAsync().Result);
                    if (questionData != null)
                    {
                        return questionData.value;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
            }
            return new List<QuestionMaster>();

        }
        private List<ResponseLineBatchCreateModel> SetResponseLine(NtiFormSessionRequest ntiFormSessionRequestModel, string responseId, string assessmentLine, string assessmentLineOptionId)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));

            List<ResponseLineBatchCreateModel> _responseLines = new List<ResponseLineBatchCreateModel>();
            List<QuestionMaster> _questionMaster = GetAllQuestion();

            if (ntiFormSessionRequestModel.FirstName != null)
            {
                string questionId = _questionMaster.Where(y => y.Question == "First Name").Select(x => x.QuestionId).FirstOrDefault() ?? string.Empty;

                ResponseLineBatchCreateModel responseLine = new ResponseLineBatchCreateModel();
                responseLine.QuestionText = "First Name";
                responseLine.ResponseText = ntiFormSessionRequestModel.FirstName;
                responseLine.Response = $"/aacn_responses({responseId})";
                if (!string.IsNullOrEmpty(questionId))
                    responseLine.Question = $"/aacn_questions({questionId})";
                if (!string.IsNullOrEmpty(assessmentLine))
                    responseLine.AssessmentLine = $"/aacn_assessment_lines({assessmentLine})";

                _responseLines.Add(responseLine);
            }
            if (ntiFormSessionRequestModel.LastName != null)
            {
                string questionId = _questionMaster.Where(y => y.Question == "Last Name").Select(x => x.QuestionId).FirstOrDefault() ?? string.Empty;

                ResponseLineBatchCreateModel responseLine = new ResponseLineBatchCreateModel();
                responseLine.QuestionText = "Last Name";
                responseLine.ResponseText = ntiFormSessionRequestModel.LastName;
                responseLine.Response = $"/aacn_responses({responseId})";
                if (!string.IsNullOrEmpty(questionId))
                    responseLine.Question = $"/aacn_questions({questionId})";
                if (!string.IsNullOrEmpty(assessmentLine))
                    responseLine.AssessmentLine = $"/aacn_assessment_lines({assessmentLine})";

                _responseLines.Add(responseLine);
            }
            if (ntiFormSessionRequestModel.EmailAddress != null)
            {
                string questionId = _questionMaster.Where(y => y.Question == "Email Address").Select(x => x.QuestionId).FirstOrDefault() ?? string.Empty;

                ResponseLineBatchCreateModel responseLine = new ResponseLineBatchCreateModel();
                responseLine.QuestionText = "Email Address";
                responseLine.ResponseText = ntiFormSessionRequestModel.EmailAddress;
                responseLine.Response = $"/aacn_responses({responseId})";
                if (!string.IsNullOrEmpty(questionId))
                    responseLine.Question = $"/aacn_questions({questionId})";
                if (!string.IsNullOrEmpty(assessmentLine))
                    responseLine.AssessmentLine = $"/aacn_assessment_lines({assessmentLine})";
                _responseLines.Add(responseLine);
            }
            if (ntiFormSessionRequestModel.SessionTime != null)
            {
                string questionId = _questionMaster.Where(y => y.Question == "Session Time").Select(x => x.QuestionId).FirstOrDefault() ?? string.Empty;

                ResponseLineBatchCreateModel responseLine = new ResponseLineBatchCreateModel();
                responseLine.QuestionText = "Session Time";
                responseLine.ResponseText = ntiFormSessionRequestModel.SessionTime;
                responseLine.Response = $"/aacn_responses({responseId})";
                if (!string.IsNullOrEmpty(questionId))
                    responseLine.Question = $"/aacn_questions({questionId})";
                if (!string.IsNullOrEmpty(assessmentLine))
                    responseLine.AssessmentLine = $"/aacn_assessment_lines({assessmentLine})";
                if (!string.IsNullOrEmpty(assessmentLineOptionId))
                    responseLine.AssessmentLineOption = $"/aacn_assessment_line_options({assessmentLineOptionId})";
                _responseLines.Add(responseLine);
            }
            if (ntiFormSessionRequestModel.SessionDate != null)
            {
                string questionId = _questionMaster.Where(y => y.Question == "Session Date").Select(x => x.QuestionId).FirstOrDefault() ?? string.Empty;

                ResponseLineBatchCreateModel responseLine = new ResponseLineBatchCreateModel();
                responseLine.QuestionText = "Session Date";
                responseLine.ResponseText = ntiFormSessionRequestModel.SessionDate;
                responseLine.Response = $"/aacn_responses({responseId})";
                if (!string.IsNullOrEmpty(questionId))
                    responseLine.Question = $"/aacn_questions({questionId})";
                if (!string.IsNullOrEmpty(assessmentLine))
                    responseLine.AssessmentLine = $"/aacn_assessment_lines({assessmentLine})";

                _responseLines.Add(responseLine);
            }
            if (ntiFormSessionRequestModel.SessionId != null)
            {
                string questionId = _questionMaster.Where(y => y.Question == "Session Id").Select(x => x.QuestionId).FirstOrDefault() ?? string.Empty;

                ResponseLineBatchCreateModel responseLine = new ResponseLineBatchCreateModel();
                responseLine.QuestionText = "Session Id";
                responseLine.ResponseText = ntiFormSessionRequestModel.SessionId;
                responseLine.Response = $"/aacn_responses({responseId})";
                if (!string.IsNullOrEmpty(questionId))
                    responseLine.Question = $"/aacn_questions({questionId})";
                if (!string.IsNullOrEmpty(assessmentLine))
                    responseLine.AssessmentLine = $"/aacn_assessment_lines({assessmentLine})";

                _responseLines.Add(responseLine);
            }

            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return _responseLines;
        }

        private List<AacnAssessmentLineAssessmentHeaderAacnAQ> SortLines(List<AacnAssessmentLineAssessmentHeaderAacnAQ> Lines)
        {
            return Lines.OrderBy(x => x.AssessmentLineOrder).ToList();
        }

        private async Task<bool> CheckSessionCount(NtiFormSessionRequest requestModel)
        {
            try
            {
                _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));

                int count = 0;
                var query = string.Format(Utility.CheckSessionCount, requestModel.SessionId);
                HttpResponseMessage response = GetRecords(query);
                if (response.IsSuccessStatusCode)
                {
                    CheckSessionCountModel sessionCountModel = JsonConvert.DeserializeObject<CheckSessionCountModel>(Utility.RemoveJsonNulls(await response.Content.ReadAsStringAsync()));
                    if (sessionCountModel.Value.Count >= 0)
                    {
                        if (sessionCountModel.Value.Count == 0)
                            return true;

                        int maxCount = sessionCountModel.Value.FirstOrDefault().PassingScore;
                        List<AacnAssessmentLineAssessmentHeaderAacnAA> AssessmemtLines = sessionCountModel.Value.FirstOrDefault().AssessmentLines;
                        var filteredAssessment = AssessmemtLines.Where(x => x.AssessmentLineQuestionText == requestModel.Date);
                        foreach (var item in filteredAssessment)
                        {
                            List<AacnResponseLineAssessmentLineAacnAssessment> responseLines = item.ResponseLines.Where(x => x.ResponseText == requestModel.SessionTime).ToList();
                            count = responseLines.Count;
                            break;
                        }
                        if (count < maxCount)
                            return true;
                    }
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return false;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return false;
            }
        }

        private async Task<bool> CheckUserEmail(NtiFormSessionRequest requestModel)
        {
            try
            {
                _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));

                var query = string.Format(Utility.CheckUserEmail, requestModel.SessionId);
                HttpResponseMessage response = GetRecords(query);
                if (response.IsSuccessStatusCode)
                {
                    UserEmailValModel userEmailValidationModel = JsonConvert.DeserializeObject<UserEmailValModel>(Utility.RemoveJsonNulls(await response.Content.ReadAsStringAsync()));
                    if (userEmailValidationModel.Value.Count > 0)
                    {
                        List<AacnAssessmentLineAssessmentHeaderAacnALines> AssessmemtLines = userEmailValidationModel.Value.FirstOrDefault().AacnAssessmentLineAssessmentHeaderAacnAs;
                        var filteredAssessment = AssessmemtLines.Where(x => x.AacnAssessmentLineQuestionText.Contains(Utility.constants.NTIConstants.Month));
                        foreach (var item in filteredAssessment)
                        {
                            bool hasReturned = false;
                            hasReturned = item.AacnResponseLineAssesmentLineAacnAssessm.Any(x => x.AacnResponseText.ToLower() == requestModel.EmailAddress.ToLower());
                            if (hasReturned)
                                return true;
                        }
                    }
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return false;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return false;
            }
        }

        private HttpResponseMessage GetAssessmentDataBySessionCode(string sessionCode)
        {
            HttpResponseMessage response = null;
            try
            {
                var query = string.Format(Utility.GetAssessmentDataBySessionCode, sessionCode);
                response = GetRecords(query);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
            }
            return response;
        }

        private HttpResponseMessage GetResponseCountForAllSession(string sessionCode)
        {
            HttpResponseMessage response = null;
            try
            {
                var counterQuery = string.Format(Utility.GetResponseCounter, sessionCode);
                response = GetRecords(counterQuery);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
            }
            return response;
        }
        private List<DayQuestionTextValues> SetSessionCountDayWise(List<AacnAssessmentLineAssessmentHeaderAacnASession> filteredAssessmentLines)
        {
            List<DayQuestionTextValues> dayQuestionTextValuesList = new List<DayQuestionTextValues>();
            foreach (var item in filteredAssessmentLines)
            {
                DayQuestionTextValues dayQuestionText = new DayQuestionTextValues();
                List<AacnAssessmentLineOptionAssessmentLineAaSession> optionLines = item.AssessmentLineOptionAssessmentLineAa;
                dayQuestionText.DayQuestionText = item.AssessmentLineQuestionText;
                List<SessionTimeValues> sessionTimeValuesList = new List<SessionTimeValues>();
                foreach (var option in optionLines)
                {
                    SessionTimeValues sessionTimeModel = new SessionTimeValues();
                    sessionTimeModel.SessionTimeId = option.AssessmentLineOptionId;
                    sessionTimeModel.SessionTime = option.AssessmentLineQuestionOptionText;
                    sessionTimeModel.ResponseCount = option.ResponseLineAssessmentLineOptionAacn.Count.ToString();
                    sessionTimeValuesList.Add(sessionTimeModel);
                }
                dayQuestionText.DayQuestionTextId = item.AssessmentLineId;
                dayQuestionText.SessionTimeValues = sessionTimeValuesList;
                dayQuestionTextValuesList.Add(dayQuestionText);
            }
            return dayQuestionTextValuesList;
        }
        #endregion
    }
}

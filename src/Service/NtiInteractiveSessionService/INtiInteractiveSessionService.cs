﻿namespace AACN.API.Service.NtiInteractiveSessionService
{
    using AACN.API.Model;
    using System.Threading.Tasks;

    public interface INtiInteractiveSessionService
    {
        Task<NtiFormSessionResponseModel> GetFormBySessionCode(string sessionCode);
        Task<NtiFormSessionResponse> CreateSessionResponse(NtiFormSessionRequest ntiFormSessionRequestModel);
        Task<NtiSessionResponseModel> GetResponseBySessionCode(string sessionCode);
    }
}

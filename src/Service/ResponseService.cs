﻿namespace AACN.API.Service
{
    using AACN.API.Model;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Net;
    using System.Threading.Tasks;
    using AACN.Services;
    using Azure;
    using System.Text;
    using AACN.API.Controllers;
    using Microsoft.Extensions.Logging;

    public class ResponseService : BaseService
    {
        public ResponseModel getResponsebyId(string ResponseId)
        {
            ResponseModel responseModel = new ResponseModel();
            string query = string.Format(Utility.getResponsebyId, ResponseId);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    responseModel = JsonConvert.DeserializeObject<ResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
                else
                {

                }
            }
            return responseModel;
        }

        public HttpResponseMessage createResponse(PostResponse postResponse)
        {
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            try
            {
                string odataQuery = "aacn_responses";
                if (!string.IsNullOrEmpty(postResponse.survey_Provider_Name))
                {
                    string survey_Provider_ID = getSurveryProviderByName(postResponse.survey_Provider_Name);
                    if (!string.IsNullOrEmpty(survey_Provider_ID))
                    {
                        postResponse.survey_Provider_Name = $"/aacn_survey_providers({survey_Provider_ID})";
                    }
                    else
                    {
                        answerResponse.StatusCode = System.Net.HttpStatusCode.BadRequest;
                        answerResponse.Content = new StringContent("Survey Provider does not exists with name - " + postResponse.survey_Provider_Name);
                        return answerResponse;
                    }
                }
                answerResponse = CreateRecord(odataQuery, JsonConvert.SerializeObject(postResponse));
                if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                {
                    string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                    string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                    answerResponse.StatusCode = System.Net.HttpStatusCode.NoContent; ;
                }
                else
                {
                    answerResponse.Content = new StringContent(answerResponse.Content.ReadAsStringAsync().Result);
                    answerResponse.StatusCode = System.Net.HttpStatusCode.BadRequest;
                }

                return answerResponse;
            }
            catch (Exception ex)
            {
                answerResponse.Content = new StringContent(ex.Message);
                return null;
            }
        }

        public HttpResponseMessage updateResponse(PostResponse postResponse, string ResponseId)
        {
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            try
            {
                if (ResponseId != null)
                {
                    if (!string.IsNullOrEmpty(postResponse.survey_Provider_Name))
                    {
                        string survey_Provider_ID = getSurveryProviderByName(postResponse.survey_Provider_Name);
                        if (!string.IsNullOrEmpty(survey_Provider_ID))
                        {
                            postResponse.survey_Provider_Name = $"/aacn_survey_providers({survey_Provider_ID})";
                        }
                        else
                        {
                            answerResponse.StatusCode = System.Net.HttpStatusCode.BadRequest;
                            answerResponse.Content = new StringContent("Survey Provider does not exists with name - " + postResponse.survey_Provider_Name);
                            return answerResponse;
                        }
                    }
                    string odataQuery = "aacn_responses(" + ResponseId + ")";
                    answerResponse = UpdateRequest(odataQuery, JsonConvert.SerializeObject(postResponse));
                    if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                    {
                        string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                        string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                        answerResponse.StatusCode = System.Net.HttpStatusCode.NoContent; ;
                    }
                    else
                    {
                        answerResponse.Content = new StringContent(answerResponse.Content.ReadAsStringAsync().Result);
                        answerResponse.StatusCode = System.Net.HttpStatusCode.BadRequest;
                    }
                }

                return answerResponse;
            }
            catch (Exception ex)
            {
                answerResponse.Content = new StringContent(ex.Message);
                return null;
            }
        }

        public APIResponse UpsertResponse(PostResponse postResponse, string responseId)
        {
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            APIResponse apiResponse = new APIResponse();
            try
            {
                bool recordAvailable = false;
                if (!string.IsNullOrEmpty(postResponse.survey_Provider_Name))
                {
                    string survey_Provider_ID = getSurveryProviderByName(postResponse.survey_Provider_Name);
                    if (!string.IsNullOrEmpty(survey_Provider_ID))
                    {
                        postResponse.survey_Provider_Name = $"/aacn_survey_providers({survey_Provider_ID})";
                    }
                }
                if (!string.IsNullOrEmpty(responseId))
                {
                    ResponseModel responseModel = getResponsebyId(responseId);
                    if (responseModel.value != null && responseModel.value.Count != 0)
                    {
                        recordAvailable = true;
                        answerResponse = updateResponse(postResponse, responseId);
                    }

                }
                if (recordAvailable == false)
                {
                    answerResponse = createResponse(postResponse);
                }
                if (answerResponse != null)
                {
                    if (answerResponse.StatusCode == HttpStatusCode.NoContent)
                    {
                        if (recordAvailable == false)
                        {
                            apiResponse.Status = "Created";
                        }
                        else
                        {
                            apiResponse.Status = "Updated";
                        }
                        string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                        string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                        apiResponse.RecordId = new Guid(splitRetrievedData[1]);
                        apiResponse.StatusCode = (int)answerResponse.StatusCode;
                        return apiResponse;
                    }
                    else
                    {
                        apiResponse.RecordId = Guid.Empty;
                        apiResponse.Status = answerResponse.Content.ReadAsStringAsync().Result;
                        apiResponse.StatusCode = (int)answerResponse.StatusCode;
                    }

                }

                return apiResponse;
            }
            catch (Exception ex)
            {
                apiResponse.Status = ex.Message;
                apiResponse.RecordId = Guid.Empty;
                apiResponse.StatusCode = (int)HttpStatusCode.BadRequest;
                return apiResponse;
            }
        }

        public APIResponseNF_D365 createResponseData(PostResponseData responeLine, ILogger<responseController> _logger)
        {
            APIResponseNF_D365 apiResponseNF_D365 = new APIResponseNF_D365();
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            _logger.LogInformation("Response line Model -" + JsonConvert.SerializeObject(responeLine));
            try
            {
                string odataQuery = "aacn_responses";
                if (!string.IsNullOrEmpty(responeLine.survey_Provider_Name))
                {
                    string survey_Provider_ID = getSurveryProviderByName(responeLine.survey_Provider_Name);
                    if (!string.IsNullOrEmpty(survey_Provider_ID))
                    {
                        responeLine.survey_Provider_Name = $"/aacn_survey_providers({survey_Provider_ID})";
                    }
                    else
                    {
                        apiResponseNF_D365.StatusCode_D365 = (int)System.Net.HttpStatusCode.BadRequest;
                        apiResponseNF_D365.D365_status = "Survey Provider does not exists with name -" + responeLine.survey_Provider_Name;
                        return apiResponseNF_D365;
                    }
                }
                //answerResponse = CreateRecord(odataQuery, JsonConvert.SerializeObject(responeLine));
                answerResponse = CreateRecordwithRetry(odataQuery, JsonConvert.SerializeObject(responeLine), _logger);
                _logger.LogInformation("D365 Json Payload -" + JsonConvert.SerializeObject(responeLine));
                if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                {

                    string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                    string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                    answerResponse.StatusCode = System.Net.HttpStatusCode.NoContent;
                    apiResponseNF_D365.RecordId = new Guid(splitRetrievedData[1]);
                    _logger.LogInformation("D365 Response Created with Id" + splitRetrievedData[1].ToString());
                    apiResponseNF_D365.StatusCode_D365 = (int)System.Net.HttpStatusCode.NoContent;
                    apiResponseNF_D365.D365_status = "Created";

                    HttpResponseMessage responseData = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest };
                    if (responeLine.extData.requestTpye.ToLower() == "CE".ToLower())
                    {
                        responseData = sendresponsetoNF(splitRetrievedData[1], responeLine, _logger);
                    }
                    else if (responeLine.extData.requestTpye.ToLower() == "PE".ToLower())
                    {
                        responseData = sendresponsetoNF_PE(splitRetrievedData[1], responeLine, _logger);

                    }

                    if (responseData.StatusCode == HttpStatusCode.NoContent)
                    {
                        apiResponseNF_D365.StatusCode_NF = (int)responseData.StatusCode;
                        apiResponseNF_D365.NF_Status = "Created";
                    }
                    else
                    {
                        apiResponseNF_D365.StatusCode_NF = (int)System.Net.HttpStatusCode.BadRequest;
                        apiResponseNF_D365.NF_Status = responseData.Content.ReadAsStringAsync().Result.ToString();
                        UpdateResponseStatustoD365(splitRetrievedData[1], responseData.Content.ReadAsStringAsync().Result.ToString() + responseData.StatusCode.ToString(), _logger);
                    }

                }
                else
                {
                    apiResponseNF_D365.D365_status = answerResponse.Content.ReadAsStringAsync().Result.ToString();
                    apiResponseNF_D365.StatusCode_D365 = (int)System.Net.HttpStatusCode.BadRequest;
                    apiResponseNF_D365.StatusCode_NF = (int)System.Net.HttpStatusCode.BadRequest;
                    apiResponseNF_D365.NF_Status = null;
                }

                return apiResponseNF_D365;
            }
            catch (Exception ex)
            {
                string resultId = apiResponseNF_D365.RecordId == null ? string.Empty : apiResponseNF_D365.RecordId.ToString();
                UpdateResponseStatustoD365(resultId, ex.Message, _logger);
                _logger.LogInformation("Exception ~" + ex.Message);
                apiResponseNF_D365.D365_status = ex.Message;
                return apiResponseNF_D365;
            }
        }

        public ResponseDataModel getResponsesByInstanceId(string instanceId)
        {
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            ResponseDataModel responseDataModel = new ResponseDataModel();
            try
            {
                InstanceData instanceData = getInstanceData(instanceId);
                if (instanceData != null && instanceData.instancerecord.Count > 0 && !string.IsNullOrEmpty(instanceData.instancerecord[0].Assessment_Code))
                {
                    responseDataModel = getResponseDataByAssessmentCode(instanceData.instancerecord[0].Assessment_Code);
                    if (responseDataModel != null)
                    {
                        responseDataModel.instance = instanceData;

                    }
                }
            }
            catch (Exception ex)
            {

            }

            return responseDataModel;
        }

        public ResponseDataModel getResponseDataByAssessmentCode(string assessmentCodeId)
        {
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            ResponseDataModel responseModel = new ResponseDataModel();
            try
            {
                string query = string.Format(Utility.getResponseDataByAssessmentCode, assessmentCodeId);
                HttpResponseMessage answerResponseData = GetRecords(query);
                if (answerResponseData.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponseData.Content.ReadAsStringAsync().Result))
                    {
                        responseModel = JsonConvert.DeserializeObject<ResponseDataModel>(Utility.RemoveJsonNulls(answerResponseData.Content.ReadAsStringAsync().Result));
                    }
                    else
                    {

                    }
                }
            }
            catch (Exception ex)
            {

            }

            return responseModel;
        }

        public InstanceData getInstanceData(string instanceId)
        {

            InstanceData instaceData = new InstanceData();
            string query = string.Format(Utility.getInstanceDataById, instanceId);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    instaceData = JsonConvert.DeserializeObject<InstanceData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));

                }
                else
                {

                }


            }
            return instaceData;
        }

        public ResponseCounterData checkResponseCounterbyInstanceId(string instanceId)
        {
            ResponseCounterData responseCounterData = new ResponseCounterData();
            ResponseDataModel responseDataModel = new ResponseDataModel();
            ResponseCounterModel responseCounterModel = new ResponseCounterModel();
            try
            {
                InstanceData instanceData = getInstanceData(instanceId);
                if (instanceData != null && instanceData.instancerecord.Count > 0 && !string.IsNullOrEmpty(instanceData.instancerecord[0].Assessment_Code))
                {
                    responseDataModel = getResponseDataByAssessmentCode(instanceData.instancerecord[0].Assessment_Code);
                    responseCounterData.startDate = (instanceData.instancerecord[0].aacn_start_date).ToString();
                    responseCounterData.endDate = (instanceData.instancerecord[0].aacn_end_date).ToString();
                    if (responseDataModel.responseValue != null && responseDataModel.responseValue.Count > 0)
                    {
                        responseCounterData.submittedResponses = responseDataModel.responseValue.Count.ToString();
                    }
                    else
                    {
                        responseCounterData.submittedResponses = "0";
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return responseCounterData;
        }

        public ResponseDataRoot getResponsesById(string ResponseId)
        {
            ResponseDataRoot responseDataRoot = new ResponseDataRoot();
            string query = string.Format(Utility.getresponseDataById, ResponseId);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    responseDataRoot = JsonConvert.DeserializeObject<ResponseDataRoot>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
                else
                {

                }
            }
            return responseDataRoot;
        }

        public HttpResponseMessage sendresponsetoNF(string responseId, PostResponseData postResponseData, ILogger<responseController> _logger)
        {
            //return new HttpResponseMessage { StatusCode = HttpStatusCode.NoContent };

            var returnresponse = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest };
            ResponseDataRoot responseDataRoot = getResponsesById(responseId);
            if (responseDataRoot != null && responseDataRoot.value != null && responseDataRoot.value.Count > 0)
            {
                NFRequestModel nfrequestModel = new NFRequestModel();
                nfrequestModel.ResponseId = responseId;
                nfrequestModel.CustomerKey = responseDataRoot.value[0]._aacn_member_value;
                nfrequestModel.SessionCode = responseDataRoot.value[0]._aacn_session_valueFormattedValue.ToString();
                nfrequestModel.ChildEventCode = string.IsNullOrEmpty(postResponseData.extData.childEventCode) == true ? null : postResponseData.extData.childEventCode;
                nfrequestModel.VendorUserId = string.IsNullOrEmpty(postResponseData.extData.vendorUserId) == true ? 0 : int.Parse(postResponseData.extData.vendorUserId);
                nfrequestModel.VendorSessionId = string.IsNullOrEmpty(postResponseData.extData.vendorSessionId) == true ? 0 : int.Parse(postResponseData.extData.vendorSessionId);
                nfrequestModel.AssessmentId = responseDataRoot.value[0]._aacn_assessment_value.ToString();

                List<NFRequestModel.Responses> nflist = new List<NFRequestModel.Responses>();
                if (responseDataRoot.value[0].responseLinesData != null && responseDataRoot.value[0].responseLinesData.Count > 0)
                {
                    foreach (var item in responseDataRoot.value[0].responseLinesData)
                    {
                        NFRequestModel.Responses response = new NFRequestModel.Responses();
                        response.ResponseLineId = item.aacn_response_lineid;
                        response.ResponseId = string.IsNullOrEmpty(responseDataRoot.value[0].aacn_responseid) == true ? null : responseDataRoot.value[0].aacn_responseid;
                        response.AssessmentLineId = string.IsNullOrEmpty(item._aacn_assesment_line_value) == true ? null : item._aacn_assesment_line_value.ToString();
                        response.AssessmentLineOptionId = string.IsNullOrEmpty(item._aacn_assessment_line_option_value) == true ? null : item._aacn_assessment_line_option_value.ToString();
                        response.AnswerText = string.IsNullOrEmpty(item.aacn_response_line_text_area) == true ? null : item.aacn_response_line_text_area;
                        nflist.Add(response);
                    }
                    nfrequestModel.responses = nflist;
                    _logger.LogInformation("Nf Request Model -" + JsonConvert.SerializeObject(nfrequestModel));
                    HttpClient httpClient = new HttpClient();
                    try
                    {
                        //string apiURL = "https://func-eventeval-dev-001.azurewebsites.net/api/AddSessionEvaluationResponses?code=oz1sL7hR4EMFshdPY7OxxH1re_Q1fmLZUiwKZBlA3mYqAzFuyKHWSw==";
                        string apiURL = CE_SubmitResponse_AzureEndpoint;
                        var content = new StringContent(JsonConvert.SerializeObject(nfrequestModel), Encoding.UTF8, "application/json");
                        returnresponse = httpClient.PostAsync(apiURL, content).GetAwaiter().GetResult();

                    }
                    catch (Exception ex)
                    {
                        return new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest };
                    }

                }
            }
            _logger.LogInformation("Nf Response Value -" + returnresponse.StatusCode);
            return returnresponse;
        }
        public HttpResponseMessage sendresponsetoNF_PE(string responseId, PostResponseData postResponseData, ILogger<responseController> _logger)
        {
            // return new HttpResponseMessage { StatusCode = HttpStatusCode.NoContent };

            var returnresponse = new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest };

            ResponseDataRoot responseDataRoot = getResponsesById(responseId);
            if (responseDataRoot != null && responseDataRoot.value != null && responseDataRoot.value.Count > 0)
            {
                string parentEventCode = null;
                EventRootModel eventdata = check_EventinD365(postResponseData.extData.childEventCode);
                if (eventdata != null && eventdata.value.Count > 0)
                {
                    if (!string.IsNullOrEmpty(eventdata.value[0].parent_Event_Code))
                    {
                        parentEventCode = eventdata.value[0].parent_Event_Code;
                    }
                }
                responsereqModel_PE requestModel = new responsereqModel_PE();
                requestModel.ResponseId = responseId;
                requestModel.CustomerKey = responseDataRoot.value[0]._aacn_member_value;
                requestModel.ChildEventCode = string.IsNullOrEmpty(postResponseData.extData.childEventCode) == true ? null : postResponseData.extData.childEventCode;
                requestModel.ParentEventCode = parentEventCode;
                requestModel.AssessmentId = responseDataRoot.value[0]._aacn_assessment_value.ToString();

                List<responsereqModel_PE.Response> nflist = new List<responsereqModel_PE.Response>();

                if (responseDataRoot.value[0].responseLinesData != null && responseDataRoot.value[0].responseLinesData.Count > 0)
                {
                    foreach (var item in responseDataRoot.value[0].responseLinesData)
                    {
                        responsereqModel_PE.Response responses = new responsereqModel_PE.Response();
                        responses.ResponseLineId = item.aacn_response_lineid;
                        responses.AssessmentLineId = string.IsNullOrEmpty(item._aacn_assesment_line_value) == true ? null : item._aacn_assesment_line_value.ToString();
                        responses.AssessmentLineOptionId = string.IsNullOrEmpty(item._aacn_assessment_line_option_value) == true ? null : item._aacn_assessment_line_option_value.ToString();
                        responses.AnswerText = string.IsNullOrEmpty(item.aacn_response_line_text_area) == true ? null : item.aacn_response_line_text_area;
                        nflist.Add(responses);
                    }

                    requestModel.Responses = nflist;
                    _logger.LogInformation("Nf Request Model -" + JsonConvert.SerializeObject(requestModel));
                    HttpClient httpClient = new HttpClient();
                    try
                    {
                        //string apiURL = "https://func-eventeval-dev-001.azurewebsites.net/api/AddProgramEvaluationResponses?code=_KFOJL6wCsRQGZxH7vuvWwuWcau0AlP8LwZUQk-jzf--AzFuJn8vNw==";
                        string apiURL = PE_SubmitResponse_AzureEndpoint;
                        var content = new StringContent(JsonConvert.SerializeObject(requestModel), Encoding.UTF8, "application/json");
                        returnresponse = httpClient.PostAsync(apiURL, content).GetAwaiter().GetResult();
                    }
                    catch (Exception ex)
                    {
                        // Log or handle the exception
                        return new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest };
                    }
                }
            }
            _logger.LogInformation("Nf Response Value -" + returnresponse.StatusCode);
            return returnresponse;
        }
        public EventRootModel check_EventinD365(string Name)
        {
            EventSessionResponseData eventSessionResponseData = new EventSessionResponseData();
            EventRootModel eventmodel = new EventRootModel();
            try
            {

                string query = string.Format(Utility.getparenteventIdbyName, Name);
                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        eventmodel = JsonConvert.DeserializeObject<EventRootModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    }
                    else
                    {

                    }
                }
            }
            catch (Exception ex)
            {
                return eventmodel;
            }
            return eventmodel;

        }


        public HttpResponseMessage UpdateResponseStatustoD365(string ResponseId, string Error, ILogger<responseController> _logger)
        {
            HttpResponseMessage answerResponse = new HttpResponseMessage();
            UpdateResponseStatus updateResponse = new UpdateResponseStatus();
            try
            {
                _logger.LogInformation("Update Started for Response ID " + ResponseId);
                if (ResponseId != null)
                {
                    updateResponse.responseStatus = false;
                    updateResponse.ErrorMessage = Error;
                    string odataQuery = "aacn_responses(" + ResponseId + ")";
                    answerResponse = UpdateRequest(odataQuery, JsonConvert.SerializeObject(updateResponse));
                    _logger.LogInformation("Response Status ~ " + answerResponse.StatusCode);
                    if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                    {
                        string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                        string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                        answerResponse.StatusCode = System.Net.HttpStatusCode.NoContent; ;
                    }
                    else
                    {
                        answerResponse.Content = new StringContent(answerResponse.Content.ReadAsStringAsync().Result);
                        answerResponse.StatusCode = System.Net.HttpStatusCode.BadRequest;
                    }
                }


                return answerResponse;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Exception ~ " + ex.Message);
                answerResponse.Content = new StringContent(ex.Message);
                return null;
            }
        }
    }
}


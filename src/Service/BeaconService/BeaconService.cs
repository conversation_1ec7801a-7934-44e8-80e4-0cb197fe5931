﻿namespace AACN.API.Service
{
    using AACN.API.Controllers;
    using AACN.API.Model;
    using AACN.API.Service.Beacon_Service;
    using AACN.Services;
    using Microsoft.Extensions.Logging;
    using Newtonsoft.Json;
    using System;
    using System.Linq;
    using System.Net.Http;
    using System.Net;
    using System.Threading.Tasks;
    using static AACN.API.Service.Utility;
    using System.Collections.Generic;
    using System.Text;
    using AACN.API.Model.ReviewerAssessmentLoadModel;
    using AACN.API.Model.SiteCoreSyncModel;
    using AACN.API.Model.OrganisationModel;
    using AACN.API.Helper;
    using static AACN.API.Model.ReviewerAssessmentLoadModel.ReviewerAssessmentLoadResponseModel;
    using AACN.API.Model.Manager;
    using static AACN.API.Model.Manager.ManagerDataModel;
    using AACN.API.Helper.AssessmentHelper;
    using System.IdentityModel.Tokens.Jwt;
    using Microsoft.IdentityModel.Tokens;
    using Microsoft.PowerPlatform.Dataverse.Client;
    using Microsoft.AspNetCore.Mvc;
    using System.Data;

    public class BeaconService : BaseService, IBeaconService

    {
        #region SiteCore_TO_D365 API's
        public BeaconResponse UserVerificationOld(string jwtToken, ILogger<beaconController> _logger)
        {
            BeaconResponse response = new BeaconResponse();
            try
            {
                _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadToken(jwtToken) as JwtSecurityToken;
                if (jsonToken == null)
                {
                    return null;
                }
                var nameClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name")?.Value;
                var email = jsonToken.Claims.FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.Email)?.Value;
                var roles = jsonToken.Claims.Where(c => c.Type == "Role").Select(claim => claim.Value).ToList();
                var rolesString = string.Join(", ", jsonToken.Claims.Where(c => c.Type == "Role").Select(claim => claim.Value));
                var keyClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == "Key")?.Value;
                var recNoClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == "RecNo")?.Value;
                var ueidClaim = jsonToken.Claims.FirstOrDefault(c => c.Type == "ueid")?.Value;

                response.UserName = nameClaim;
                response.UserRole = rolesString;
                response.Email = email;
                response.Rec_Number = recNoClaim;
                response.RecorId = keyClaim;

                _logger.LogInformation(LoggerHelper.LogObjectValue(response, JsonConvert.SerializeObject(response).ToString()));

            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                response.Error = ex.Message;
            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return response;
        }
        public BeaconResponse UserVerification(string jwtToken, ILogger<beaconController> _logger)
        {
            BeaconResponse response = new BeaconResponse();
            try
            {
                _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
                var handler = new JwtSecurityTokenHandler();

                // Define the security key and validation parameters

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(JWTKey)),
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ClockSkew = TimeSpan.Zero
                };

                // Validate the token
                SecurityToken validatedToken;
                var principal = handler.ValidateToken(jwtToken, validationParameters, out validatedToken);

                var jsonToken = handler.ReadToken(jwtToken) as JwtSecurityToken;
                if (jsonToken == null)
                {
                    return null;
                }

                if (jsonToken.ValidTo < DateTime.UtcNow)
                {
                    response.Error = "The Link You Followed Has Expired!";
                    return response;
                }

                #region ClaimTokens
                response.UserName = jsonToken.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name")?.Value;
                response.Email = jsonToken.Claims.FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.Email)?.Value;
                var roles = jsonToken.Claims.Where(c => c.Type == "Role").Select(claim => claim.Value).ToList();
                response.UserRole = string.Join(", ", jsonToken.Claims.Where(c => c.Type == "Role").Select(claim => claim.Value));
                response.RecorId = jsonToken.Claims.FirstOrDefault(c => c.Type == "Key")?.Value;
                response.Rec_Number = jsonToken.Claims.FirstOrDefault(c => c.Type == "RecNo")?.Value;

                #endregion
                _logger.LogInformation(LoggerHelper.LogObjectValue(response, JsonConvert.SerializeObject(response).ToString()));
            }

            catch (SecurityTokenExpiredException ex)
            {
                LoggerHelper.LogException(_logger, ex);
                response.Error = "The Link You Followed Has Expired!";
            }
            catch (SecurityTokenException ex)
            {
                LoggerHelper.LogException(_logger, ex);
                response.Error = "Something went wrong please try again!.";
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                response.Error = ex.Message;
            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return response;
        }
        public async Task<List<APIResponse>> SyncOrganizationData(PostOrganizationCommand BatchRequests, ILogger<beaconController> _logger)
        {
            HttpResponseMessage answerResponse = null;
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            List<APIResponse> _responseList = new List<APIResponse>();
            List<PostionData> _positionMasterList = new List<PostionData>();
            try
            {
                string odataQuery = "accounts";
                if (BatchRequests != null && BatchRequests.OrganizationData.Count > 0)
                {
                    PositionDataModel positionDataModel = GetPositionMaster(_logger);
                    if (positionDataModel != null && positionDataModel.postiionrecords.Count > 0)
                    {
                        _positionMasterList = positionDataModel.postiionrecords;
                    }
                    string unitIds = string.Join(",", BatchRequests.OrganizationData.Select(p => $"\"{p.OrgnizationId}\""));
                    AccountModelRoot accountModelRoot = await GetUnitDataById(unitIds, _logger);
                    foreach (PostOrganizationModel organizationModel in BatchRequests.OrganizationData)
                    {
                        APIResponse apiRepsonse = new APIResponse();
                        try
                        {
                            if (_positionMasterList != null && _positionMasterList.Count > 0)
                            {
                                var positionRecord = _positionMasterList.FirstOrDefault(record => record.name.ToString().ToLower() == Utility.constants.postionConstants.ORGANIZATION.ToLower());
                                if (positionRecord != null)
                                {
                                    organizationModel.PostionId = $"/positions({positionRecord.positionid})";
                                }
                            }
                            _logger.LogInformation(LoggerHelper.LogObjectValue(organizationModel, JsonConvert.SerializeObject(organizationModel)));
                            //Check Unit ExistorNot -- 
                            string requestFor = string.Empty;
                            var unitRecord = accountModelRoot.value.FirstOrDefault(record => record.accountid.ToString().ToLower() == organizationModel.OrgnizationId.ToString().ToLower());
                            HttpResponseMessage answer = new HttpResponseMessage();
                            if (unitRecord == null)
                            {
                                requestFor = "Create";
                                answerResponse = await UpsertRecordAsync(odataQuery, JsonConvert.SerializeObject(organizationModel), requestFor, _logger);

                            }
                            else
                            {
                                requestFor = "Update";
                                answerResponse = await UpsertRecordAsync($"{odataQuery}({organizationModel.OrgnizationId})", JsonConvert.SerializeObject(organizationModel), requestFor, _logger);

                            }

                            _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                            if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                            {
                                string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                                string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                                apiRepsonse.RecordId = new Guid(splitRetrievedData[1]);
                                apiRepsonse.StatusCode = (int)HttpStatusCode.NoContent;
                                apiRepsonse.Status = requestFor ?? string.Empty;
                            }
                            else
                            {
                                apiRepsonse.Status = new StringContent(answerResponse.Content.ReadAsStringAsync().Result).ToString();
                                apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                            }
                            _responseList.Add(apiRepsonse);
                            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                            _logger.LogInformation(LoggerHelper.LogObjectValue(apiRepsonse, JsonConvert.SerializeObject(apiRepsonse)));
                        }
                        catch (Exception ex)
                        {
                            apiRepsonse.RecordId = Guid.Empty;
                            apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                            apiRepsonse.Status = $"Organization Sync Failed because - {ex.Message}";
                            _responseList.Add(apiRepsonse);
                        }
                    }

                }

            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                APIResponse apiResponse = new APIResponse();
                apiResponse.StatusCode = (int)HttpStatusCode.BadRequest;
                apiResponse.Status = $"Data Issue Found  {ex.Message}";
                _responseList.Add(apiResponse);
            }
            return _responseList;

        }
     
        public async Task<SiteCoreResponseModelbatchResponses> SyncSiteCoreResponsesBatchRequests(SyncResponsesCommand siteCoreRequestModel, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            SiteCoreResponseModelbatchResponses apiResponse = new SiteCoreResponseModelbatchResponses();
            List<BeaconRespnseData> beaconRespnseDataList = new List<BeaconRespnseData>();
            List<PostAssessmentReviewActivity> _reviewList = new List<PostAssessmentReviewActivity>();
            var responseItems = new List<SiteCoreResponseModel>();

            try
            {
                if (siteCoreRequestModel != null)
                {
                    string SurveyId = string.Empty;
                    string survey_Provider_ID = getSurveryProviderByName("Future - Beacon 3.x");
                    if (!string.IsNullOrEmpty(survey_Provider_ID))
                    {
                        SurveyId = $"/aacn_survey_providers({survey_Provider_ID})";
                    }
                    List<SurveyResponseDto> surveyResponses = siteCoreRequestModel.SurveyResponses;
                    foreach (var item in surveyResponses)
                    {
                        PostAssessmentReviewActivity acitivityReview = new PostAssessmentReviewActivity();
                        acitivityReview.UserResponseId = item.Key.ToString();
                        acitivityReview.request_Status_Value = (int)RequestStatus.Ready;
                        BeaconRespnseData requestModel = new BeaconRespnseData();
                        requestModel.ResponseKey = item.Key.ToString(); // Response HeaderId
                        requestModel.survey_Provider_Name = SurveyId; // Survey Id passed as Static
                        requestModel.assessment_Id = item.AssessmentKey.ToString();
                        //Pending for StatusDate, AddDate, ChangeDate, IsDeleted
                        List<BeaconResponseLines> responseLines = new List<BeaconResponseLines>();
                        foreach (var responseLine in item.ResponseLines)
                        {
                            BeaconResponseLines responseLineData = new BeaconResponseLines();
                            responseLineData.ResponseLineKey = responseLine.Key.ToString(); // Response Line Key
                            responseLineData.assessment_Line_Id = responseLine.QuestionKey != Guid.Empty ? responseLine.QuestionKey.ToString() : null; // Question Key
                            responseLineData.assessment_Line_Option_Id = responseLine.SubquestionKey.ToString(); // SubQuestion Key
                            responseLineData.response_Text = responseLine.AnswerText; // Answer Text
                            responseLines.Add(responseLineData);
                            //Pending for AddDate, ChangeDate, IsDeleted
                        }
                        requestModel.response_Line = responseLines;
                        beaconRespnseDataList.Add(requestModel);
                        _reviewList.Add(acitivityReview);
                    }
                    _logger.LogInformation($"Total Batch Reqeust Found ~ {beaconRespnseDataList.Count}");
                    string query = "aacn_responses";
                    HttpResponseMessage batchRequestResponse = await CreateBatchRecords<beaconController, BeaconRespnseData>(query, beaconRespnseDataList, _logger);
                    LoggerHelper.LogObjectValue(batchRequestResponse, batchRequestResponse.Content.ReadAsStringAsync().Result);
                    if (batchRequestResponse.IsSuccessStatusCode)
                    {
                        var responseContent = batchRequestResponse.Content.ReadAsStringAsync().Result;
                        var batchResponse = ParseBatchResponse(responseContent);
                        // Process the batch response
                        List<sitecoreResponses> _responseKeys = new List<sitecoreResponses>();
                        apiResponse.toalResponseSynced = batchResponse.Items.Count;
                        ResponseKeys responseKeys = new ResponseKeys();
                        responseKeys.ResponseArray = batchResponse.Items.Select(item => item.recordId.ToString()).ToArray();
                        apiResponse.responseKeys = responseKeys;
                        string activityRequest = "aacn_assessment_review_activities";
                        HttpResponseMessage batchAcitvityResponse = await CreateBatchRecords<beaconController, PostAssessmentReviewActivity>(activityRequest, _reviewList, _logger);
                        if (batchAcitvityResponse.IsSuccessStatusCode)
                        {
                            var batchAcitvityResponses = ParseBatchResponse(batchAcitvityResponse.Content.ReadAsStringAsync().Result);
                            apiResponse.toalAcitivigtyCreated = batchAcitvityResponses.Items.Count;
                            ReviewAcitivityKeys reviewActivityKeys = new ReviewAcitivityKeys();
                            reviewActivityKeys.ReviewAcitivityArray = batchAcitvityResponses.Items.Select(item => item.recordId.ToString()).ToArray();
                            apiResponse.reviewAcitivityKeys = reviewActivityKeys;
                        }
                        else
                        {
                            apiResponse.ErrorMessage = batchAcitvityResponse.Content.ReadAsStringAsync().Result;

                        }
                    }
                    else
                    {
                        apiResponse.ErrorMessage = batchRequestResponse.Content.ReadAsStringAsync().Result;
                    }
                }
                return apiResponse;

            }
            catch (Exception ex)
            {
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                apiResponse.ErrorMessage = ex.Message;
                LoggerHelper.LogException(_logger, ex);
                return apiResponse;
            }

        }
        public async Task<List<APIResponse>> SyncUnits(PostUnitCommand postUnitCommand, ILogger<beaconController> _logger)
        {
            HttpResponseMessage answerResponse = null;
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            List<APIResponse> _responseList = new List<APIResponse>();
            List<PostionData> _positionMasterList = new List<PostionData>();
            APIResponse apiRepsonse = new APIResponse();
            try
            {
                string odataQuery = "accounts";
                if (postUnitCommand != null && postUnitCommand.UnitData.Count > 0)
                {
                    PositionDataModel positionDataModel = GetPositionMaster(_logger);
                    if (positionDataModel != null && positionDataModel.postiionrecords.Count > 0)
                    {
                        _positionMasterList = positionDataModel.postiionrecords;
                    }
                    string unitIds = string.Join(",", postUnitCommand.UnitData.Select(p => $"\"{p.Unit_Id}\""));
                    AccountModelRoot accountModelRoot = await GetUnitDataById(unitIds, _logger);
                    foreach (PostUnitModel postunitmodel in postUnitCommand.UnitData)
                    {
                        APIResponse response = new APIResponse();
                        try
                        {
                            if (_positionMasterList != null && _positionMasterList.Count > 0)
                            {
                                var positionRecord = _positionMasterList.FirstOrDefault(record => record.name.ToString().ToLower() == Utility.constants.postionConstants.UNIT.ToLower());
                                if (positionRecord != null)
                                {
                                    postunitmodel.PostionId = $"/positions({positionRecord.positionid})";
                                }
                            }
                            _logger.LogInformation(LoggerHelper.LogObjectValue(postunitmodel, JsonConvert.SerializeObject(postunitmodel)));
                            //Check Unit ExistorNot -- 
                            string requestFor = string.Empty;
                            var unitRecord = accountModelRoot.value.FirstOrDefault(record => record.accountid.ToString().ToLower() == postunitmodel.Unit_Id.ToString().ToLower());
                            HttpResponseMessage answer = new HttpResponseMessage();
                            if (unitRecord == null)
                            {
                                requestFor = "Create";
                                answerResponse = await UpsertRecordAsync(odataQuery, JsonConvert.SerializeObject(postunitmodel), requestFor, _logger);

                            }
                            else
                            {
                                requestFor = "Update";
                                answerResponse = await UpsertRecordAsync($"{odataQuery}({postunitmodel.Unit_Id})", JsonConvert.SerializeObject(postunitmodel), requestFor, _logger);

                            }
                            _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                            if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                            {
                                string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                                string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                                response.RecordId = new Guid(splitRetrievedData[1]);
                                response.StatusCode = (int)HttpStatusCode.NoContent;
                                response.Status = requestFor ?? string.Empty;
                            }
                            else
                            {
                                response.Status = new StringContent(answerResponse.Content.ReadAsStringAsync().Result).ToString();
                                response.StatusCode = (int)HttpStatusCode.BadRequest;
                            }

                            _responseList.Add(response);
                            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                            _logger.LogInformation(LoggerHelper.LogObjectValue(response, JsonConvert.SerializeObject(apiRepsonse)));
                        }
                        catch (Exception ex)
                        {
                            LoggerHelper.LogException(_logger, ex);
                            response.Status = new StringContent(ex.Message).ToString();
                            response.StatusCode = (int)HttpStatusCode.BadRequest;
                            response.RecordId = Guid.Empty;
                            _responseList.Add(response);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                apiRepsonse.Status = new StringContent(ex.Message).ToString();
                apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                apiRepsonse.RecordId = Guid.Empty;
                _responseList.Add(apiRepsonse);
            }
            return _responseList;

        }

        public async Task<List<ReturnResponse>> RemoveResponses(RemoveResponse responsemodel, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            var _returnResponse = new List<ReturnResponse>();

            try
            {
                _logger.LogInformation(LoggerHelper.LogObjectValue(responsemodel, JsonConvert.SerializeObject(responsemodel)));
                if (responsemodel.SurveyStatuses != null && responsemodel.SurveyStatuses.Count >= 1)
                {
                    var filtereddeleteresponse = responsemodel.SurveyStatuses.Where(x => x.Status != "Submitted").ToList();
                    _logger.LogInformation(filtereddeleteresponse.ToString());
                    _returnResponse = await deleteResponseLines(filtereddeleteresponse,_returnResponse, _logger);
                }
                createApiLog(responsemodel, _returnResponse, _logger);
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Exception Error - " + ex.Message.ToString());
            }
            return _returnResponse;
        }

        public async Task<List<APIResponse>> SyncReviewerData(SyncReviwerCommand syncReviwerCommand, ILogger<beaconController> _logger)
        {
            HttpResponseMessage answerResponse = null;
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            _logger.LogInformation(LoggerHelper.LogObjectValue(syncReviwerCommand, JsonConvert.SerializeObject(syncReviwerCommand)));
            List<APIResponse> _responseList = new List<APIResponse>();
            APIResponse apiRepsonse = new APIResponse();
            try
            {
                if (syncReviwerCommand != null && syncReviwerCommand.ReviewerData.Count > 0)
                {
                    PositionDataModel positionDataModel = GetPositionMaster(_logger);
                    if (positionDataModel != null && positionDataModel.postiionrecords.Count > 0)
                    {
                        _logger.LogInformation($"Position Master Found with ~{positionDataModel.postiionrecords.Count}");
                    }
                    string unitIds = string.Join(",", syncReviwerCommand.ReviewerData.Select(p => $"\"{p.RecordId}\""));
                    ContactModelRoot contactModelRoot = await GetReviewerDataById(unitIds, _logger);
                    string odataQuery = "contacts";
                    foreach (PostContactModel postcontactmodel in syncReviwerCommand.ReviewerData)
                    {
                        if (positionDataModel != null && positionDataModel.postiionrecords.Count > 0)
                        {
                            var positionRecord = positionDataModel.postiionrecords.FirstOrDefault(record => record.name.ToString().ToLower() == postcontactmodel.AccountRoleCode.ToLower());
                            if (positionRecord != null)
                            {
                                postcontactmodel.postionId = $"/positions({positionRecord.positionid})";
                            }
                        }
                        _logger.LogInformation(LoggerHelper.LogObjectValue(postcontactmodel, JsonConvert.SerializeObject(postcontactmodel)));

                        string requestFor = string.Empty;
                        var unitRecord = contactModelRoot.value.FirstOrDefault(record => record.contactid.ToString().ToLower() == postcontactmodel.RecordId.ToString().ToLower());
                        HttpResponseMessage answer = new HttpResponseMessage();
                        if (unitRecord == null)
                        {
                            requestFor = "Create";
                            answerResponse = await UpsertRecordAsync(odataQuery, JsonConvert.SerializeObject(postcontactmodel), requestFor, _logger);

                        }
                        else
                        {
                            requestFor = "Update";
                            

                            answerResponse = await UpsertRecordAsync($"{odataQuery}({postcontactmodel.RecordId})", JsonConvert.SerializeObject(postcontactmodel, new JsonSerializerSettings
                            {
                                NullValueHandling = NullValueHandling.Include
                            }), requestFor, _logger);

                        }
                        _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                        if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                        {
                            string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                            string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                            apiRepsonse.RecordId = new Guid(splitRetrievedData[1]);
                            apiRepsonse.StatusCode = (int)HttpStatusCode.NoContent;
                            apiRepsonse.Status = requestFor;
                        }
                        else
                        {
                            apiRepsonse.Status = new StringContent(answerResponse.Content.ReadAsStringAsync().Result).ToString();
                            apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                        }
                        _responseList.Add(apiRepsonse);
                        _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                        _logger.LogInformation(LoggerHelper.LogObjectValue(apiRepsonse, JsonConvert.SerializeObject(apiRepsonse)));

                    }
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                apiRepsonse.Status = new StringContent(ex.Message).ToString();
                apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                apiRepsonse.RecordId = Guid.Empty;
                _responseList.Add(apiRepsonse);
            }

            return _responseList;

        }

        #endregion SiteCore_TO_D365 API's

        #region D365_TO_Portal_Internal_API's - Methods
        public async Task<BeaconResponseByAction> GetResponseCounterByMemberId(string reviewerId, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            BeaconResponseCounterModel beaconResponseCounterModel = new BeaconResponseCounterModel();
            BeaconResponseByAction beaconResponseByAction = new BeaconResponseByAction();
            try
            {
                string beaconQuery = string.Format(Utility.GetResponsesByReviwerId, reviewerId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(beaconQuery, _logger);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                ServiceClient _service = CRMServiceExtension.GetCRMConnection();

                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    beaconResponseCounterModel = JsonConvert.DeserializeObject<BeaconResponseCounterModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    if (beaconResponseCounterModel != null && beaconResponseCounterModel.beaconResponses.Count > 0)
                    {
                        var valueCounts = beaconResponseCounterModel.beaconResponses.Select(r => r.Request_Status_Formatted_Value).GroupBy(v => v).Select(g => new { Value = g.Key, Count = g.Count() }).ToList();
                        foreach (var item in valueCounts)
                        {
                            if (item.Value.ToString() == ActionStatusConstants.READY)
                            {
                                beaconResponseByAction.Ready = item.Count.ToString();
                            }
                            else if (item.Value.ToString() == ActionStatusConstants.ASSIGNED)
                            {
                                beaconResponseByAction.Assigned = item.Count.ToString();
                            }
                            else if (item.Value.ToString() == ActionStatusConstants.ACCEPTED)
                            {
                                beaconResponseByAction.Accepted = item.Count.ToString();
                            }
                            else if (item.Value.ToString() == ActionStatusConstants.REJECTED)
                            {
                                beaconResponseByAction.Rejected = item.Count.ToString();
                            }
                            else if (item.Value.ToString() == ActionStatusConstants.IN_PROGRESS)
                            {
                                beaconResponseByAction.In_Progress = item.Count.ToString();
                            }
                            else if (item.Value.ToString() == ActionStatusConstants.COMPLETE)
                            {
                                beaconResponseByAction.Complete = item.Count.ToString();
                            }
                            else if (item.Value.ToString() == ActionStatusConstants.SUBMITTED)
                            {
                                beaconResponseByAction.Complete = item.Count.ToString();
                            }
                        }

                    }
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return beaconResponseByAction;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new BeaconResponseByAction();
            }

        }
        public async Task<APIResponse> Post_AssessmentReview(PostAssessmentReviewActivity postAssessmentReviewActivity, ILogger<beaconController> _logger)
        {
            HttpResponseMessage answerResponse = null;
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            APIResponse apiRepsonse = new APIResponse();
            try
            {
                var settings = new JsonSerializerSettings
                {
                    ContractResolver = new CustomContractResolver(),
                    NullValueHandling = NullValueHandling.Ignore
                };
                string odataQuery = "aacn_assessment_review_activities";
                _logger.LogInformation(LoggerHelper.LogObjectValue(postAssessmentReviewActivity, JsonConvert.SerializeObject(postAssessmentReviewActivity, settings)));
                answerResponse = await CreateRecordWithRetry(odataQuery, JsonConvert.SerializeObject(postAssessmentReviewActivity), _logger);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                {
                    string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                    string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                    apiRepsonse.RecordId = new Guid(splitRetrievedData[1]);
                    apiRepsonse.StatusCode = (int)HttpStatusCode.NoContent;
                    apiRepsonse.Status = "Created";
                }
                else
                {
                    apiRepsonse.Status = new StringContent(answerResponse.Content.ReadAsStringAsync().Result).ToString();
                    apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                _logger.LogInformation(LoggerHelper.LogObjectValue(apiRepsonse, JsonConvert.SerializeObject(apiRepsonse)));
                return apiRepsonse;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                apiRepsonse.Status = new StringContent(ex.Message).ToString();
                apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                apiRepsonse.RecordId = Guid.Empty;
                return null;
            }

        }
        public async Task<APIResponse> Update_Assessment_Review(PostAssessmentReviewActivity postAssessmentReviewActivity, ILogger<beaconController> _logger)
        {
            HttpResponseMessage answerResponse = null;
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            APIResponse apiRepsonse = new APIResponse();
            try
            {
                var settings = new JsonSerializerSettings
                {
                    ContractResolver = new CustomContractResolver(),
                    NullValueHandling = NullValueHandling.Ignore
                };
                string odataQuery = $"aacn_assessment_review_activities({postAssessmentReviewActivity.Assessment_Review_ActivityId})";
                _logger.LogInformation(LoggerHelper.LogObjectValue(postAssessmentReviewActivity, JsonConvert.SerializeObject(postAssessmentReviewActivity, settings)));
                answerResponse = await UpdateRequestAsync(odataQuery, JsonConvert.SerializeObject(postAssessmentReviewActivity, settings), _logger);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                {
                    string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                    string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                    apiRepsonse.RecordId = new Guid(splitRetrievedData[1]);
                    apiRepsonse.StatusCode = (int)HttpStatusCode.NoContent;
                    apiRepsonse.Status = "Updated";
                }
                else
                {
                    apiRepsonse.Status = new StringContent(answerResponse.Content.ReadAsStringAsync().Result).ToString();
                    apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                _logger.LogInformation(LoggerHelper.LogObjectValue(apiRepsonse, JsonConvert.SerializeObject(apiRepsonse)));
                return apiRepsonse;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                apiRepsonse.Status = new StringContent(ex.Message).ToString();
                apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                apiRepsonse.RecordId = Guid.Empty;
                return null;
            }

        }
        public async Task<List<APIResponse>> Update_Assessment_ReviewBatch(PostAssessmentReviewActivityCommand postAssessmentReviewActivityCommand, ILogger<beaconController> _logger)

        {
            HttpResponseMessage answerResponse = null;
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            List<APIResponse> _responseList = new List<APIResponse>();
            List<PostionData> _positionMasterList = new List<PostionData>();
            APIResponse apiRepsonse = new APIResponse();
            try
            {
                foreach (PostAssessmentReviewActivity postAssessmentReviewActivity in postAssessmentReviewActivityCommand.ReviewAcitvities)
                {
                    try
                    {
                        var settings = new JsonSerializerSettings
                        {
                            ContractResolver = new CustomContractResolver(),
                            NullValueHandling = NullValueHandling.Ignore
                        };
                        string odataQuery = $"aacn_assessment_review_activities({postAssessmentReviewActivity.Assessment_Review_ActivityId})";
                        _logger.LogInformation(LoggerHelper.LogObjectValue(postAssessmentReviewActivity, JsonConvert.SerializeObject(postAssessmentReviewActivity, settings)));
                        answerResponse = await UpdateRequestAsync(odataQuery, JsonConvert.SerializeObject(postAssessmentReviewActivity, settings), _logger);
                        _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                        if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                        {
                            string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                            string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                            apiRepsonse.RecordId = new Guid(splitRetrievedData[1]);
                            apiRepsonse.StatusCode = (int)HttpStatusCode.NoContent;
                            apiRepsonse.Status = "Updated";
                        }
                        else
                        {
                            apiRepsonse.Status = new StringContent(answerResponse.Content.ReadAsStringAsync().Result).ToString();
                            apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                        }
                        _responseList.Add(apiRepsonse);
                        _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                        _logger.LogInformation(LoggerHelper.LogObjectValue(apiRepsonse, JsonConvert.SerializeObject(apiRepsonse)));
                    }
                    catch (Exception ex)
                    {
                        LoggerHelper.LogException(_logger, ex);
                        apiRepsonse.Status = new StringContent(ex.Message).ToString();
                        apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                        apiRepsonse.RecordId = Guid.Empty;
                        _responseList.Add(apiRepsonse);
                    }
                }
            }

            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                apiRepsonse.Status = new StringContent(ex.Message).ToString();
                apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                apiRepsonse.RecordId = Guid.Empty;
                _responseList.Add(apiRepsonse);
            }

            return _responseList;
        }
        public async Task<AssessmentReviewActivtyRootModel> GetAssessmentActivityByReviwerId(string reviewerId, string yearId, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            AssessmentReviewActivtyRootModel assessmentReviewActivtyRoot = new AssessmentReviewActivtyRootModel();
            BeaconResponseByAction beaconResponseByAction = new BeaconResponseByAction();
            try
            {
                string beaconQuery = string.Format(Utility.GetActivityResponseByReviewerId, reviewerId, yearId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(beaconQuery, _logger);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    assessmentReviewActivtyRoot = JsonConvert.DeserializeObject<AssessmentReviewActivtyRootModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    if (assessmentReviewActivtyRoot != null && assessmentReviewActivtyRoot.assessmentReviews.Count > 0)
                    {
                        assessmentReviewActivtyRoot.status_Counter = GetResponseCounterByRequestStatusMain(assessmentReviewActivtyRoot, _logger);
                    }
                    else
                    {
                        assessmentReviewActivtyRoot.status_Counter = new BeaconResponseByAction
                        {
                            Submitted = "0",
                            Accepted = "0",
                            Ready = "0",
                            Assigned = "0",
                            In_Progress = "0",
                            Complete = "0",
                            Rejected = "0",
                        };
                    }
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return assessmentReviewActivtyRoot;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new AssessmentReviewActivtyRootModel();
            }
        }

        public async Task<AssessmentReviewActivtyRoot> GetAssessmentActivityByActivityReviewId(string activityId, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            AssessmentReviewActivtyRoot assessmentReviewActivtyRoot = new AssessmentReviewActivtyRoot();
            BeaconResponseByAction beaconResponseByAction = new BeaconResponseByAction();
            try
            {
                string beaconQuery = string.Format(Utility.GetActivityReviewbyId, activityId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(beaconQuery, _logger);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    return JsonConvert.DeserializeObject<AssessmentReviewActivtyRoot>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));

                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return assessmentReviewActivtyRoot;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new AssessmentReviewActivtyRoot();
            }

        }
        public async Task<ContactListModelByRole> GetContactListByRole(string requestType, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            string OdataQuery = string.Empty;
            ContactListModelByRole contactListModel = new ContactListModelByRole();
            try
            {
                string reviewerId = string.Empty;
                string managerId = string.Empty;
                if (!string.IsNullOrEmpty(requestType))
                {
                    PositionDataModel _postionMaster = GetPositionMaster(_logger);
                    LoggerHelper.LogObjectValue(_postionMaster, JsonConvert.SerializeObject(_postionMaster));
                    if (_postionMaster != null && _postionMaster.postiionrecords.Count > 0)
                    {
                        var filterId = _postionMaster?.postiionrecords?.Where(r => r.name?.ToLower() == Utility.constants.postionConstants.REVIEWER.ToLower())
                                       .Select(r => r.positionid).ToList() ?? null;
                        if (filterId.Count > 0)
                        {
                            reviewerId = filterId.FirstOrDefault();
                        }

                        var MangerId = _postionMaster?.postiionrecords?.Where(r => r.name?.ToLower() == Utility.constants.postionConstants.MANAGER.ToLower())
                                       .Select(r => r.positionid).ToList() ?? null;
                        if (MangerId.Count > 0)
                        {
                            managerId = MangerId.FirstOrDefault();
                        }

                    }
                    if (requestType.ToLower() == roleType.Manager.ToString().ToLower() && !string.IsNullOrEmpty(reviewerId))
                    {
                        OdataQuery = string.Format(Utility.GetContactListByRole, managerId);
                    }
                    else if (requestType.ToLower() == roleType.Reviewer.ToString().ToLower() && !string.IsNullOrEmpty(managerId))
                    {
                        OdataQuery = string.Format(Utility.GetContactListByRole, reviewerId);

                    }
                    _logger.LogInformation($"{LoggerHelper.LogMethodStart(_logger)} OdataQuery - {OdataQuery}");
                    if (OdataQuery != string.Empty)
                    {
                        HttpResponseMessage contactReponse = await GetRecordsAsync(OdataQuery, _logger);
                        _logger.LogInformation(LoggerHelper.LogObjectValue(contactReponse, contactReponse.StatusCode.ToString()));
                        if (!string.IsNullOrEmpty(contactReponse.Content.ReadAsStringAsync().Result.ToString()))
                        {
                            contactListModel = JsonConvert.DeserializeObject<ContactListModelByRole>(contactReponse.Content.ReadAsStringAsync().Result.ToString());
                            if (contactListModel != null && contactListModel.contactList.Count > 0)
                            {
                                contactListModel.contactList = contactListModel.contactList
                                    .OrderBy(c => c.FirstName)
                                    .Where(c => !c.EndDate.HasValue || c.EndDate.Value >= DateTime.UtcNow)
                                    .ToList();
                                contactListModel.TotalRecords = contactListModel.contactList.Count;
                            }

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
            }
            _logger.LogInformation(LoggerHelper.LogObjectValue(contactListModel, JsonConvert.SerializeObject(contactListModel)));
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));

            return contactListModel;
        }
        public async Task<ReviewerAssessmentLoadResponseModel> GetReviewerAndUserResponses(string reviewActivityId, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            try
            {
                _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
                string OdataQuery = string.Format(Utility.GetReviewerAndUserResponses, reviewActivityId);
                _logger.LogInformation($"OdataQuery - {OdataQuery}");
                HttpResponseMessage answerResponse = await GetRecordsAsync(OdataQuery, _logger);
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.Content.ReadAsStringAsync().Result));
                    return JsonConvert.DeserializeObject<ReviewerAssessmentLoadResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
                else
                {
                    return new ReviewerAssessmentLoadResponseModel();
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                LoggerHelper.LogException(_logger, ex);
                return new ReviewerAssessmentLoadResponseModel { ErrorValue = ex.Message };

            }
        }
        public async Task<BeaconAPIResponse> CreateBeaconResponse(BeaconRespnseData beaconRespnseData, string RequestFor, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            BeaconAPIResponse response = new BeaconAPIResponse();
            try
            {
                string odataQuery = "aacn_responses";
                if (!string.IsNullOrEmpty(beaconRespnseData.survey_Provider_Name))
                {
                    string survey_Provider_ID = getSurveryProviderByName(beaconRespnseData.survey_Provider_Name);
                    if (!string.IsNullOrEmpty(survey_Provider_ID))
                    {
                        beaconRespnseData.survey_Provider_Name = $"/aacn_survey_providers({survey_Provider_ID})";
                    }
                    else
                    {
                        response.StatusCode = (int)HttpStatusCode.BadRequest;
                        response.Errors = "Survey Provider does not exists with name - " + beaconRespnseData.survey_Provider_Name;
                        return response;
                    }
                }
                string activityNumber = beaconRespnseData.response_Activity_Details.Assessment_Review_ActivityNumber;
                string reviewActivityId = beaconRespnseData.response_Activity_Details.Assessment_reviewId;
                beaconRespnseData.response_Activity_Details = null;
                string ResponseId = string.Empty;
                if (activityNumber != null && reviewActivityId != null)
                {
                    AssessmentReviewActivtyRoot assessmentReviewActivtyRoot = await GetAssessmentActivityByActivityReviewId(reviewActivityId, _logger);
                    if (assessmentReviewActivtyRoot != null && assessmentReviewActivtyRoot.assessmentReviews.Count > 0)
                    {
                        ResponseId = assessmentReviewActivtyRoot.assessmentReviews[0].Reviewer_Response_Id;
                    }
                }
                HttpResponseMessage answerResponse = new HttpResponseMessage();
                beaconRespnseData.Year = $"/aacn_years({beaconRespnseData.Year})";
                if (!string.IsNullOrEmpty(odataQuery) && !string.IsNullOrEmpty(ResponseId))
                {
                    odataQuery = odataQuery + $"({ResponseId})";
                    //Update Headers - 
                    List<BeaconResponseLines> _responseLineList = beaconRespnseData.response_Line;
                    beaconRespnseData.response_Line = null;
                    //Update Response Headers -- 
                    answerResponse = await UpdateRequestAsync(odataQuery, JsonConvert.SerializeObject(beaconRespnseData), _logger);
                    if (answerResponse.StatusCode == HttpStatusCode.NoContent)
                    {
                        List<HttpResponseMessage> _responses = await UpdateandCreateResponseLinesNewApproach(ResponseId, _responseLineList, _logger);
                        if (_responses.Any())
                        {
                            var BadRequest = _responses.Where(r => r.StatusCode == HttpStatusCode.BadRequest).ToList();
                            if (BadRequest == null)
                            {
                                _logger.LogInformation("Response Lines Updated Sucessfully");
                            }
                            else
                            {
                                LoggerHelper.LogObjectValue(BadRequest, BadRequest.ToString());
                                LoggerHelper.LogException(_logger, new Exception("Error While Updating Response Lines"));
                            }

                        }
                    }

                }
                else
                {
                    answerResponse = await CreateRecordWithRetry(odataQuery, JsonConvert.SerializeObject(beaconRespnseData), _logger);

                }
                _logger.LogInformation(LoggerHelper.LogObjectValue(beaconRespnseData, JsonConvert.SerializeObject(beaconRespnseData)));
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.NoContent)//204
                {
                    string _recordUrl = answerResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                    string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                    answerResponse.StatusCode = HttpStatusCode.NoContent;
                    response.ResponseId = new Guid(splitRetrievedData[1].ToString());
                    response.Status = HttpStatusCode.NoContent.ToString();
                    response.StatusCode = (int)HttpStatusCode.NoContent;
                    //Update the Assessment Activity Review -
                    HttpResponseMessage UpdateResponse = await UpdateAcitivityReviwerAssessment(splitRetrievedData[1].ToString(), activityNumber, RequestFor, _logger);
                    _logger.LogInformation(LoggerHelper.LogObjectValue(UpdateResponse, UpdateResponse.StatusCode.ToString()));
                    if (UpdateResponse.StatusCode == HttpStatusCode.NoContent)
                    {
                        response.AcvitiyReviewUpdated = true;
                        response.Errors = string.Empty;
                    }
                }
                else
                {
                    response.ResponseId = Guid.Empty;
                    response.Status = new StringContent(answerResponse.Content.ReadAsStringAsync().Result).ToString();
                    response.StatusCode = (int)HttpStatusCode.BadRequest;

                }
                _logger.LogInformation(LoggerHelper.LogObjectValue(response, JsonConvert.SerializeObject(response)));
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return response;
            }
            catch (Exception ex)
            {
                response.Status = new StringContent(ex.Message).ToString();
                response.StatusCode = (int)HttpStatusCode.ExpectationFailed;
                response.Errors = ex.Message;
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return response;
            }
        }

        public async Task<GetAuditRoot> GetAuditReport(ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            GetAuditRoot getAuditDataRoot = new GetAuditRoot();
            try
            {
                string cycleYear = string.Empty;
                if (DateTime.UtcNow.Month == 1)
                {
                    cycleYear = (DateTime.UtcNow.Year - 1).ToString();
                }
                else
                {
                    cycleYear = (DateTime.UtcNow.Year).ToString();
                }
                var query = string.Format(GetAuditData, cycleYear);
                HttpResponseMessage auditResponse = GetRecords(query);
                _logger.LogInformation(LoggerHelper.LogObjectValue(auditResponse, auditResponse.StatusCode.ToString()));
                if (auditResponse.IsSuccessStatusCode)
                {
                    getAuditDataRoot = JsonConvert.DeserializeObject<GetAuditRoot>(Utility.RemoveJsonNulls(await auditResponse.Content.ReadAsStringAsync()));
                    if (getAuditDataRoot != null && getAuditDataRoot.auditReportvalue.Count > 0)
                    {
                        getAuditDataRoot.totalcount = getAuditDataRoot.auditReportvalue.Count.ToString();
                        return getAuditDataRoot;
                    }
                    return getAuditDataRoot;
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return new GetAuditRoot();
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new GetAuditRoot();
            }
        }
        public async Task<StatusCodeResult> RandomizeData(ILogger<beaconController> _logger)
        {
            try
            {
                _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
                string cycleYear = string.Empty;
                if (DateTime.UtcNow.Month == 1)
                {
                    cycleYear = (DateTime.UtcNow.Year - 1).ToString();
                }
                else
                {
                    cycleYear = (DateTime.UtcNow.Year).ToString();
                }
                var query = string.Format(GetAuditCompletedData, cycleYear);
                HttpResponseMessage completedResponse = GetRecords(query);
                GetAuditRoot getAuditDataRoot = new GetAuditRoot();
                List<AuditReportDataModel> _auditReportDataModel = new List<AuditReportDataModel>();
                if (completedResponse.IsSuccessStatusCode)
                {
                    getAuditDataRoot = JsonConvert.DeserializeObject<GetAuditRoot>(Utility.RemoveJsonNulls(completedResponse.Content.ReadAsStringAsync().Result));
                    if (getAuditDataRoot != null && getAuditDataRoot.auditReportvalue.Count > 0)
                    {
                        int totalCount = getAuditDataRoot.auditReportvalue.Count;
                        getAuditDataRoot.auditReportvalue = getAuditDataRoot.auditReportvalue.GroupBy(x => x.aacn_Unit.accountid).Select(group => group.First()).ToList();
                        int returncount = ((totalCount * 5) / 100) + 1;
                        Random randomnumber = new Random();
                        int randomIndex = randomnumber.Next(0, getAuditDataRoot.auditReportvalue.Count - returncount);
                        for (int i = randomIndex; i < randomIndex + returncount; i++)
                        {
                            AuditReportDataModel auditReportDataModel = new AuditReportDataModel();
                            if (getAuditDataRoot.auditReportvalue[i].aacn_assessment_review_activity_number != null)
                            {
                                auditReportDataModel.aacn_assessment_review_activity_number = getAuditDataRoot.auditReportvalue[i].aacn_assessment_review_activity_number;
                            }
                            if (getAuditDataRoot.auditReportvalue[i].aacn_reviewer != null)
                            {
                                auditReportDataModel.aacn_reviewer = $"/contacts({getAuditDataRoot.auditReportvalue[i].aacn_reviewer.contactid})";
                            }
                            if (getAuditDataRoot.auditReportvalue[i].aacn_Unit != null)
                            {
                                auditReportDataModel.aacn_unit = $"/accounts({getAuditDataRoot.auditReportvalue[i].aacn_Unit.accountid})";
                            }
                            if (getAuditDataRoot.auditReportvalue[i].aacn_unit_module != null)
                            {
                                auditReportDataModel.aacn_unit_module = $"/aacn_assessments({getAuditDataRoot.auditReportvalue[i].aacn_unit_module.aacn_assessmentid})";
                            }
                            if (getAuditDataRoot.auditReportvalue[i].aacn_beacon_cycle_year != null)
                            {
                                auditReportDataModel.aacn_year = $"/aacn_years({getAuditDataRoot.auditReportvalue[i].aacn_beacon_cycle_year.YearId})";
                            }

                            auditReportDataModel.ApplicationId =getAuditDataRoot.auditReportvalue[i].ApplicationId;

                            _auditReportDataModel.Add(auditReportDataModel);
                        }
                        var odataquery = "aacn_audit_report_datas";
                        HttpResponseMessage createArdResponse = await CreateBatchRecords(odataquery, _auditReportDataModel, _logger);
                        if (!createArdResponse.IsSuccessStatusCode)
                        {
                            return new StatusCodeResult(400);
                        }
                        _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                        return new StatusCodeResult(200);
                    }
                }
                return new StatusCodeResult(400);
            }
            catch (Exception ex)
            {
                return new StatusCodeResult(500);
            }
        }

        public async Task<AssessmentReviewActivtyRootModel> GetManagerDashboardByStatus(string statusValue, ILogger<beaconController> _logger)
        {

            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            AssessmentReviewActivtyRootModel assessmentReviewActivtyRoot = new AssessmentReviewActivtyRootModel();
            BeaconResponseByAction beaconResponseByAction = new BeaconResponseByAction();
            try
            {
                int Statusvalue = (int)EnumStatusHelper.GetRequestStatusValue(statusValue);
                string beaconQuery = string.Empty;
                if (statusValue.ToLower() != RequestStatus.all.ToString().ToLower())
                {
                    beaconQuery = string.Format(Utility.getManagerDashBoardbyStatusNew, Statusvalue);
                }
                else
                {
                    beaconQuery = string.Format(Utility.GETALLAPPLICATIONS);
                }
                HttpResponseMessage answerResponse = await GetRecordsAsync(beaconQuery, _logger);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    assessmentReviewActivtyRoot = JsonConvert.DeserializeObject<AssessmentReviewActivtyRootModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    if (assessmentReviewActivtyRoot != null && assessmentReviewActivtyRoot.assessmentReviews.Count > 0)
                    {
                        assessmentReviewActivtyRoot.status_Counter = GetResponseCounterByRequestStatusMain(assessmentReviewActivtyRoot, _logger);
                    }
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return assessmentReviewActivtyRoot;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new AssessmentReviewActivtyRootModel();
            }
        }

        public async Task<AssessmentReviewActivtyRootModel> GetManagerDashboardByStatusWithPagination(ManagerDashboardRequest managerRequestModel, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            _logger.LogInformation(LoggerHelper.LogObjectValue(managerRequestModel, JsonConvert.SerializeObject(managerRequestModel)));
            var assessmentReviewActivtyRoot = new AssessmentReviewActivtyRootModel();

            try
            {
                var query = getSqlQuery(managerRequestModel);
                _logger.LogInformation("query = " + query);

                DataTable orderData = GetSqlData(query, _logger);

                AssessmentReviewActivtyRootModel assessmentReviewActivityModel = new AssessmentReviewActivtyRootModel();
                List<AssessmentReviewsModel> assessmentReviews = new List<AssessmentReviewsModel>();

                foreach (DataRow row in orderData.Rows)
                {
                    int? statusEnum = row["aacn_request_status"] != DBNull.Value ? (int?)Convert.ToInt32(row["aacn_request_status"]) : null;
                    var status = string.Empty;
                    if (statusEnum == (int)RequestStatus.Complete)
                    {
                        status = "Complete";
                    }
                    else if (statusEnum == (int)RequestStatus.InProgress)
                    {
                        status = "InProgress";
                    }
                    else if (statusEnum == (int)RequestStatus.Rejected)
                    {
                        status = "Reject";
                    }
                    else if (statusEnum == (int)RequestStatus.Accepted)
                    {
                        status = "Accept";
                    }
                    else if (statusEnum == (int)RequestStatus.Assigned)
                    {
                        status = "Assign";
                    }
                    else if (statusEnum == (int)RequestStatus.Ready)
                    {
                        status = "Ready";
                    }
                    int? rejectedStatusEnum = row["aacn_rejected_reason"] != DBNull.Value ? (int?)Convert.ToInt32(row["aacn_rejected_reason"]) : null;
                    var rejectedDisplayValue = string.Empty;
                    if (rejectedStatusEnum == (int)EnumHelper.RejectedReason.ConflictOfInterest)
                    {
                        rejectedDisplayValue = "Conflict of Interest";
                    }
                    else if (rejectedStatusEnum == (int)EnumHelper.RejectedReason.Busy)
                    {
                        rejectedDisplayValue = "Busy";
                    }
                    else if (rejectedStatusEnum == (int)EnumHelper.RejectedReason.Vacation)
                    {
                        rejectedDisplayValue = "Vacation";
                    }
                    else if (rejectedStatusEnum == (int)EnumHelper.RejectedReason.Other)
                    {
                        rejectedDisplayValue = "Other";
                    }

                    var review = new AssessmentReviewsModel
                    {

                        Assessment_Review_ActivityId = row["aacn_assessment_review_activityid"]?.ToString(),
                        Assessment_Review_Activity_Number = row["aacn_assessment_review_activity_number"]?.ToString(),
                        ApplicationId = row["aacn_application_id"]?.ToString(),
                        Year = row["Year"]?.ToString(),
                        Request_Status_EnumValue = statusEnum,
                        Request_Staus_Display_Value = status,
                        Rejected_Reason_EnumValue = rejectedStatusEnum,
                        Reject_Reason_DisplayValue = rejectedDisplayValue,
                        Reviewer_Name = row["fullname"]?.ToString(),
                        Reviwer_Id = row["aacn_reviewer"]?.ToString(),
                        Unit_Id = row["aacn_unit"]?.ToString(),
                        Unit_Name = row["name"]?.ToString(),
                        Unit_Response = row["aacn_user_response"]?.ToString(),
                        aacn_accepted_date = row["aacn_accepted_date"]?.ToString(),
                        aacn_rejected_date = row["aacn_rejected_date"]?.ToString(),
                        aacn_assigned_date = row["aacn_assigned_date"]?.ToString(),
                        aacn_inprogress_date = row["aacn_inprogress_date"]?.ToString(),
                        aacn_completed_date = row["aacn_completed_date"]?.ToString(),
                        Module_Completion_Status = Convert.ToBoolean(row["aacn_module_completion_status"]),
                        aacn_Unit = new AacnUnitModel
                        {
                            accountid = row["accountid"]?.ToString(),
                            accountnumber = row["accountnumber"]?.ToString(),
                            name = row["name"]?.ToString(),
                            City = row["address1_city"]?.ToString(),
                            State = row["address1_stateorprovince"]?.ToString(),
                            parentaccountid = new Parentaccountid
                            {
                                parent_accountid = row["parent_accountid"]?.ToString(),
                                name = row["parent_name"]?.ToString(),
                                accountnumber = row["parent_accountnumber"]?.ToString()
                            }
                        },
                        aacn_unit_module = new AssessmentDetailsModel
                        {
                            Assessment_Description = row["aacn_assessment_description"]?.ToString(),
                            Assessment_Number = row["aacn_assessment_number"]?.ToString(),
                            Assessment_Title = row["aacn_assessment_title"]?.ToString(),
                            Assessment_Id = row["aacn_assessmentid"]?.ToString(),
                        },
                        aacn_reviewer_module = new AssessmentDetailsModel
                        {
                            Assessment_Description = row["reviewer_description"]?.ToString(),
                            Assessment_Number = row["reviewer_number"]?.ToString(),
                            Assessment_Title = row["reviewer_title"]?.ToString(),
                            Assessment_Id = row["reviewer_module_assessment_id"]?.ToString(),
                        },
                        aacn_reviewer = new AacnReviewerModel
                        {
                            aacn_contact_number = row["aacn_contact_number"]?.ToString(),
                            contactid = row["contactid"]?.ToString(),
                            firstname = row["firstname"]?.ToString(),
                            lastname = row["lastname"]?.ToString()
                        }
                    };

                    assessmentReviews.Add(review);
                }

                assessmentReviewActivityModel.assessmentReviews = assessmentReviews;
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return assessmentReviewActivityModel;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new AssessmentReviewActivtyRootModel();
            }
        }

        public async Task<AssessmentReviewActivtyRootModel> GetManagerDashboard(string yearId,ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            AssessmentReviewActivtyRootModel assessmentReviewActivtyRoot = new AssessmentReviewActivtyRootModel();
            BeaconResponseByAction beaconResponseByAction = new BeaconResponseByAction();
            try
            {
                string beaconQuery = string.Format(Utility.getManagerDashBoard, yearId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(beaconQuery, _logger);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    assessmentReviewActivtyRoot = JsonConvert.DeserializeObject<AssessmentReviewActivtyRootModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    if (assessmentReviewActivtyRoot != null && assessmentReviewActivtyRoot.assessmentReviews.Count > 0)
                    {
                        assessmentReviewActivtyRoot.status_Counter = GetResponseCounterByRequestStatusMain(assessmentReviewActivtyRoot, _logger);
                    }
                    DateTime sevenDaysAgo = DateTime.UtcNow.AddDays(-7);
                    assessmentReviewActivtyRoot.NoStatusChanges = assessmentReviewActivtyRoot.assessmentReviews
.Where(review =>
{
    DateTime? assignedDate = DateTime.TryParse(review.aacn_assigned_date, out var tempAssigned) ? tempAssigned : (DateTime?)null;
    DateTime? inprogressDate = DateTime.TryParse(review.aacn_inprogress_date, out var tempInprogress) ? tempInprogress : (DateTime?)null;
    DateTime? acceptedDate = DateTime.TryParse(review.aacn_accepted_date, out var tempAccepted) ? tempAccepted : (DateTime?)null;
    DateTime? rejectedDate = DateTime.TryParse(review.aacn_rejected_date, out var tempRejected) ? tempRejected : (DateTime?)null;
    DateTime? completedDate = DateTime.TryParse(review.aacn_completed_date, out var tempCompleted) ? tempCompleted : (DateTime?)null;

    if (review.Request_Status_EnumValue == (int)RequestStatus.Complete)
    {
        return false;
    }

    return
       (assignedDate.HasValue && rejectedDate.HasValue &&
        (rejectedDate.Value.Date - assignedDate.Value.Date).TotalDays > 7) ||
        (assignedDate.HasValue && rejectedDate.HasValue &&
        (assignedDate.Value.Date - rejectedDate.Value.Date).TotalDays > 7) ||
       (assignedDate.HasValue && acceptedDate.HasValue &&
        (acceptedDate.Value.Date - assignedDate.Value.Date).TotalDays > 7) ||
       (acceptedDate.HasValue && rejectedDate.HasValue &&
        (rejectedDate.Value.Date - acceptedDate.Value.Date).TotalDays > 7) ||
        (acceptedDate.HasValue && inprogressDate.HasValue &&
        (inprogressDate.Value.Date - acceptedDate.Value.Date).TotalDays > 7) ||
        (!completedDate.HasValue && inprogressDate.HasValue &&
        (DateTime.Now.Date - inprogressDate.Value.Date).TotalDays > 7) ||
       (!acceptedDate.HasValue && !rejectedDate.HasValue &&
        assignedDate.HasValue &&
        (DateTime.Now.Date - assignedDate.Value.Date).TotalDays > 7);
}).ToList();

                    //assessmentReviewActivtyRoot.NoStatusChanges = assessmentReviewActivtyRoot.assessmentReviews.Where(r => r.modifiedon < sevenDaysAgo).ToList();
                    assessmentReviewActivtyRoot.assessmentReviews = assessmentReviewActivtyRoot.assessmentReviews.Where(r => r.Request_Status_EnumValue != (int)RequestStatus.Ready && r.Request_Status_EnumValue != (int)RequestStatus.Assigned).OrderByDescending(r => r.modifiedon).ToList();
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return assessmentReviewActivtyRoot;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new AssessmentReviewActivtyRootModel();
            }

        }
        public async Task<ReviewerAssessmentLoadResponseModel> GetUnitModule(string reviewActivityId, ILogger<beaconController> _logger)
        {

            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            ReviewerAssessmentLoadResponseModel reviewerAssessmentLoadResponseModel = new ReviewerAssessmentLoadResponseModel();
            BeaconResponseByAction beaconResponseByAction = new BeaconResponseByAction();
            try
            {
                string beaconQuery = string.Format(Utility.getUnitModule, reviewActivityId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(beaconQuery, _logger);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    reviewerAssessmentLoadResponseModel = JsonConvert.DeserializeObject<ReviewerAssessmentLoadResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return reviewerAssessmentLoadResponseModel;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new ReviewerAssessmentLoadResponseModel();
            }
        }
        public async Task<ReviewerAssessmentLoadResponseModel> GetReviewerModule(string reviewActivityId, ILogger<beaconController> _logger)
        {

            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            ReviewerAssessmentLoadResponseModel reviewerAssessmentLoadResponseModel = new ReviewerAssessmentLoadResponseModel();
            BeaconResponseByAction beaconResponseByAction = new BeaconResponseByAction();
            try
            {
                string beaconQuery = string.Format(Utility.getReviewerModule, reviewActivityId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(beaconQuery, _logger);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    reviewerAssessmentLoadResponseModel = JsonConvert.DeserializeObject<ReviewerAssessmentLoadResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return reviewerAssessmentLoadResponseModel;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new ReviewerAssessmentLoadResponseModel();
            }
        }
        public async Task<ReviewerAssessmentLoadResponseModel> GetReviewerModuleData(string reviewActivityId, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            ReviewerAssessmentLoadResponseModel reviewerAssessmentLoadResponseModel = new ReviewerAssessmentLoadResponseModel();
            try
            {
                string beaconQuery = string.Format(Utility.getReviewerModule, reviewActivityId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(beaconQuery, _logger);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    reviewerAssessmentLoadResponseModel = JsonConvert.DeserializeObject<ReviewerAssessmentLoadResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }

                #region sorting
                List<assessmentHeaderLineRecords> sortedList = new List<assessmentHeaderLineRecords>();

                //For Sorting Purpose
                if (reviewerAssessmentLoadResponseModel.assessmentReviewActivity[0].Reviewer_Module != null && reviewerAssessmentLoadResponseModel.assessmentReviewActivity[0].Reviewer_Module.AssessmentsLines.Any())
                {
                    reviewerAssessmentLoadResponseModel.assessmentReviewActivity[0].Reviewer_Module.AssessmentsLines = AssessmentHelperExtensions.SortAssessments(reviewerAssessmentLoadResponseModel.assessmentReviewActivity[0].Reviewer_Module.AssessmentsLines);
                }

                #endregion
                //Search for Responses related with Activity--
                if (reviewerAssessmentLoadResponseModel.assessmentReviewActivity[0] != null)
                {
                    string activityId = reviewerAssessmentLoadResponseModel.assessmentReviewActivity[0].aacn_assessment_review_activityid;
                    if (!(string.IsNullOrEmpty(activityId)))
                    {
                        ReviewerAssessmentLoadResponseModel responses = await GetResponseDataRootById(activityId, _logger);
                        if (responses != null && responses.assessmentReviewActivity.Count > 0)
                        {
                            AacnUserResponse userResponse = responses.assessmentReviewActivity[0].UserResponses;
                            if (userResponse != null && userResponse.ResponseLines.Count > 0)
                            {
                                string Questionval = "Please provide an exemplar related to innovations, initiatives, improvements, or unit achievements that positively impacted";
                                List<AacnResponseLineResponseAacnResponse> filteredResponseLines = userResponse.ResponseLines.Where(r => (r.aacn_question_text != string.Empty && r.aacn_question_text != null)).ToList();
                                List<AacnResponseLineResponseAacnResponse> responseLineData = filteredResponseLines.Where(r => r.aacn_question_text.Contains(Questionval)).ToList();
                                foreach (var item in responseLineData)
                                {
                                    if (!string.IsNullOrEmpty(item._aacn_assesment_line_value))
                                    {
                                        item.GroupName = await GetGroupText(item._aacn_assesment_line_value, _logger);
                                    }
                                }
                                if (responseLineData != null && responseLineData.Count > 0)
                                {
                                    userResponse.ResponseLines = responseLineData;
                                    reviewerAssessmentLoadResponseModel.assessmentReviewActivity[0].UserResponses = userResponse;
                                }
                            }
                        }

                    }

                }
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                return reviewerAssessmentLoadResponseModel;
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new ReviewerAssessmentLoadResponseModel();
            }
        }
        public async Task<UnitVerificationResponse> VerfiyUnitResponses(string reviewerKey, ILogger<beaconController> _logger)
        {
            UnitVerificationResponse unitVerificationResponse = new UnitVerificationResponse();
            try
            {
                _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
                AssessmentReviewActivtyRoot assessmentReviewActivtyRoot = await GetReviewerVerificationData(reviewerKey, _logger);
                if (assessmentReviewActivtyRoot != null && assessmentReviewActivtyRoot.assessmentReviews.Any())
                {

                    //Get Countof Completed -- 
                    var CompletedModules = assessmentReviewActivtyRoot.assessmentReviews.Where(r => r.Request_Status_EnumValue == (int)RequestStatus.Complete).ToList();
                    if (CompletedModules != null) { unitVerificationResponse.CompletedModules = CompletedModules.Count; }
                    var NotCompletedModules = assessmentReviewActivtyRoot.assessmentReviews.Where(r => r.Request_Status_EnumValue != (int)RequestStatus.Complete).ToList();
                    if (NotCompletedModules != null && NotCompletedModules.Count > 0)
                    {
                        unitVerificationResponse.NotCompletedModules = NotCompletedModules.Count;
                    }
                    else
                    {
                        unitVerificationResponse.IsSuccess = true;
                    }


                }
            }
            catch (Exception ex)
            {

            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));

            return unitVerificationResponse;
        }
        public async Task<ManagerDataModel> LoadManagerData(string UnitId, string requestPage, string previousModule,string yearId, ILogger<beaconController> _logger)
        {
            ManagerDataModel responseModel = new ManagerDataModel();
            responseModel.assessmentReviewActivity = new List<Acvitydata>();
            responseModel.assessmentReviewActivity.Add(new Acvitydata());
            List<ReviewAcitiyData> reviewAcitiyData = new List<ReviewAcitiyData>();
            try
            {
                string[] assessmentTitlesOrder = { "Nursing Workforce Qualitative Exemplars", "Patient Outcomes Qualitative Exemplars", "Work Environment Qualitative Exemplars" };
                ReviewAcitiyDataModel reviewAcitiyDataModel = await GetReviewerResponseByReviewerID(UnitId, yearId, _logger);
                ReviewAcitiyData UnitModuleData = null;
                if (reviewAcitiyDataModel != null && reviewAcitiyDataModel.reviewAcitiyData.Any())
                {
                    UnitModuleData = reviewAcitiyDataModel.reviewAcitiyData.FirstOrDefault(data => data.aacn_reviewer_module == null);
                    reviewAcitiyData = reviewAcitiyDataModel.reviewAcitiyData.Where(r => r.aacn_reviewer_module != null).ToList();
                }

                if (!string.IsNullOrEmpty(requestPage))
                {

                    string[] filteredAssessmentTitlesOrder = null;
                    if (requestPage.ToLower() != "mainpage" && requestPage.ToLower() != "1stpage")
                    {
                        string[] titlesToRemove = previousModule.Split(new[] { "," }, StringSplitOptions.None);
                        filteredAssessmentTitlesOrder = assessmentTitlesOrder
            .Where(title => !titlesToRemove.Contains(title))
            .ToArray();
                    }

                    ReviewAcitiyData matchedData = null;
                    switch (requestPage)
                    {
                        case "1stPage":
                            foreach (var title in assessmentTitlesOrder)
                            {
                                matchedData = reviewAcitiyData.FirstOrDefault(data =>
                                   data.aacn_reviewer_module?.aacn_assessment_title?.ToLower() == title.ToLower());
                                if (matchedData != null)
                                {
                                    string activityId = matchedData.aacn_assessment_review_activityid == null ? string.Empty : matchedData.aacn_assessment_review_activityid;
                                    responseModel = await UpdateManageModel(activityId, matchedData, _logger);
                                    break;
                                }

                            }

                            break;

                        case "2ndPage":
                            foreach (var title in filteredAssessmentTitlesOrder)
                            {
                                matchedData = reviewAcitiyData.FirstOrDefault(data =>
                                    data.aacn_reviewer_module?.aacn_assessment_title?.ToLower() == title.ToLower());
                                if (matchedData != null)
                                {
                                    string activityId = matchedData.aacn_assessment_review_activityid == null ? string.Empty : matchedData.aacn_assessment_review_activityid;
                                    responseModel = await UpdateManageModel(activityId, matchedData, _logger);
                                    break;
                                }
                            }
                            break;

                        case "3rdPage":
                            foreach (var title in filteredAssessmentTitlesOrder)
                            {
                                matchedData = reviewAcitiyData.FirstOrDefault(data =>
                                    data.aacn_reviewer_module?.aacn_assessment_title?.ToLower() == title.ToLower());
                                if (matchedData != null)
                                {
                                    string activityId = matchedData.aacn_assessment_review_activityid == null ? string.Empty : matchedData.aacn_assessment_review_activityid;
                                    responseModel = await UpdateManageModel(activityId, matchedData, _logger);
                                    break;
                                }
                            }
                            break;

                        case "4thPage":
                            foreach (var title in filteredAssessmentTitlesOrder)
                            {
                                matchedData = reviewAcitiyData.FirstOrDefault(data =>
                                    data.aacn_reviewer_module?.aacn_assessment_title?.ToLower() == title.ToLower());
                                if (matchedData != null)
                                {
                                    string activityId = matchedData.aacn_assessment_review_activityid == null ? string.Empty : matchedData.aacn_assessment_review_activityid;
                                    responseModel = await UpdateManageModel(activityId, matchedData, _logger);
                                    break;
                                }
                            }
                            break;


                        default: //Case for Unit Module Only 

                            var unitModuleKey = await GetAssessmentKey(yearId);
                            AssessmentdataRecords assessmentData = await GetAssessmentData(unitModuleKey, _logger);
                            responseModel.assessmentReviewActivity = new List<Acvitydata>();
                            responseModel.assessmentReviewActivity.Add(new Acvitydata());
                            AssignedModuleDetails assignedModule = new AssignedModuleDetails();
                            assignedModule.assignedModuleCounter = reviewAcitiyDataModel.reviewAcitiyData.Count;
                            responseModel.assessmentReviewActivity[0].assignedModules = assignedModule;
                            responseModel.assessmentReviewActivity[0].unit_Module = assessmentData;
                            responseModel.assessmentReviewActivity[0].unit_Module.AssessmentsLines = assessmentData.AssessmentsLines;
                            if (responseModel.assessmentReviewActivity[0].unit_Module != null)
                            {
                                if (responseModel.assessmentReviewActivity[0].unit_Module != null && responseModel.assessmentReviewActivity[0].unit_Module.AssessmentsLines.Any())
                                {
                                    responseModel.assessmentReviewActivity[0].unit_Module.AssessmentsLines = AssessmentHelperExtensions.SortAssessments(responseModel.assessmentReviewActivity[0].unit_Module.AssessmentsLines);
                                }
                            }

                            ReviewerAssessmentLoadResponseModel userResponse = await GetUserResponseByUnitAssessment(UnitId, unitModuleKey, _logger);
                            if (userResponse != null)
                            {
                                if (userResponse.assessmentReviewActivity.Count > 0)
                                {
                                    if (userResponse.assessmentReviewActivity[0].UserResponses != null)
                                    {
                                        responseModel.assessmentReviewActivity[0].UserResponses = userResponse.assessmentReviewActivity[0].UserResponses;
                                    }

                                }
                            }
                            if (UnitModuleData != null)
                            {

                                responseModel.assessmentReviewActivity[0].aacn_Unit = new UnitData();
                                responseModel.assessmentReviewActivity[0].aacn_Unit.name = UnitModuleData.aacn_Unit.name == null ? string.Empty : UnitModuleData.aacn_Unit.name;
                                responseModel.assessmentReviewActivity[0].aacn_Unit.accountid = UnitModuleData.aacn_Unit.accountid == null ? string.Empty : UnitModuleData.aacn_Unit.accountid;
                                responseModel.assessmentReviewActivity[0].aacn_Unit.accountnumber = UnitModuleData.aacn_Unit.accountnumber == null ? string.Empty : UnitModuleData.aacn_Unit.accountnumber;

                                //Contact - 
                                if (UnitModuleData.aacn_reviewer != null)
                                {
                                    responseModel.assessmentReviewActivity[0].aacn_reviewer = new ReviewerData();
                                    responseModel.assessmentReviewActivity[0].aacn_reviewer.contactid = UnitModuleData.aacn_reviewer.contactid == null ? string.Empty : UnitModuleData.aacn_reviewer.contactid;
                                    responseModel.assessmentReviewActivity[0].aacn_reviewer.aacn_contact_number = UnitModuleData.aacn_reviewer.aacn_contact_number == null ? string.Empty : UnitModuleData.aacn_reviewer.aacn_contact_number;
                                    responseModel.assessmentReviewActivity[0].aacn_reviewer.firstname = UnitModuleData.aacn_reviewer.firstname == null ? string.Empty : UnitModuleData.aacn_reviewer.firstname;
                                }
                            }

                            break;
                    }
                }

            }
            catch (Exception ex)
            {

            }
            return responseModel;
        }
        public async Task<List<APIResponse>> UpdateManagerResponses(UpdateResponseModel UpdateResponseModel, ILogger<beaconController> _logger)
        {
            List<APIResponse> _apiResponses = new List<APIResponse>();
            APIResponse response = new APIResponse();
            try
            {
                if (UpdateResponseModel != null && UpdateResponseModel.Update_Response.Count > 0)
                {
                    List<Model.Manager.UpdateResponse> filteredData = UpdateResponseModel.Update_Response.Where(r => r.responselineId != string.Empty || r.responselineId != null || r.responselineId != "").ToList();
                    string responseLinesIds = string.Join(",", filteredData.Where(p => p.responselineId != null).Select(p => $"\"{p.responselineId}\""));
                    List<LinesData> _responseLines = await GetResponseLinesData(responseLinesIds, _logger);
                    foreach (Model.Manager.UpdateResponse updateitem in UpdateResponseModel.Update_Response)
                    {
                        APIResponse apiRepsonse = new APIResponse();
                        try
                        {
                            LinesData resppnseLines = _responseLines.FirstOrDefault(r => r.aacn_response_lineid == (updateitem.responselineId == null ? string.Empty : updateitem.responselineId));
                            BeaconResponseLines responseLineData = new BeaconResponseLines();
                            string RequestFor = string.Empty;
                            if (resppnseLines == null)
                            {
                                RequestFor = "Create";
                                responseLineData.question_Id = updateitem.questionId;
                                responseLineData.question_Text = updateitem.questionText;
                                responseLineData.AnswerText = updateitem.answerText;
                                responseLineData.Response_Id = updateitem.responseId;
                                responseLineData.answer_Id = updateitem.answerdId;

                            }
                            else
                            {
                                if ((updateitem.answerText.ToLower() == resppnseLines.aacn_response_text || updateitem.answerText.ToLower() == resppnseLines.aacn_response_line_text_area) &&
                                    updateitem.answerdId == resppnseLines._aacn_answer_value && updateitem.questionId == updateitem.questionId &&
                                    updateitem.questionText.ToLower() == resppnseLines.aacn_question_text.ToLower())
                                {

                                }
                                else
                                {
                                    RequestFor = "Update";
                                    responseLineData.question_Id = updateitem.questionId;
                                    responseLineData.question_Text = updateitem.questionText;
                                    responseLineData.AnswerText = updateitem.answerText;
                                    responseLineData.Response_Id = updateitem.responseId;
                                    responseLineData.answer_Id = updateitem.answerdId;

                                }
                            }
                            string reqQuery = string.Empty;
                            if (RequestFor.ToLower() == "create")
                            {
                                reqQuery = "aacn_response_lines";
                            }
                            else
                            {
                                reqQuery = $"aacn_response_lines({updateitem.responselineId})";
                            }
                            if (!String.IsNullOrEmpty(RequestFor))
                            {
                                HttpResponseMessage createUpdateRespnse = await UpsertRecordAsync(reqQuery, JsonConvert.SerializeObject(responseLineData), RequestFor, _logger);
                                if (createUpdateRespnse.IsSuccessStatusCode)
                                {
                                    apiRepsonse.StatusCode = (int)HttpStatusCode.NoContent;
                                    apiRepsonse.Status = RequestFor;
                                    apiRepsonse.RecordId = new Guid(createUpdateRespnse.Headers.GetValues("OData-EntityId").FirstOrDefault().Split('[', '(', ')', ']')[1].ToString());

                                }
                                else
                                {
                                    apiRepsonse.StatusCode = (int)createUpdateRespnse.StatusCode;
                                    apiRepsonse.Status = createUpdateRespnse.Content.ReadAsStringAsync().Result;
                                    apiRepsonse.RecordId = Guid.Empty;
                                }
                            }
                            else
                            {
                                apiRepsonse.StatusCode = (int)HttpStatusCode.OK;
                                apiRepsonse.Status = "Data Found !";
                                apiRepsonse.RecordId = new Guid(updateitem.responseId);
                            }


                        }
                        catch (Exception ex)
                        {
                            apiRepsonse.StatusCode = (int)HttpStatusCode.BadRequest;
                            apiRepsonse.Status = ex.Message;
                            apiRepsonse.RecordId = Guid.Empty;
                        }
                        _apiResponses.Add(apiRepsonse);
                    }

                }

            }
            catch (Exception ex)
            {
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                response.Status = ex.Message;
                response.RecordId = Guid.Empty;
            }
            return _apiResponses;
        }

        //public async Task<List<APIResponse>> UpdateManagerResponsesNew(UpdateResponseModel UpdateResponseModel, ILogger<beaconController> _logger)
        //{
        //    List<APIResponse> _apiResponses = new List<APIResponse>();
        //    APIResponse response = new APIResponse();
        //    try
        //    {
        //        if (UpdateResponseModel != null && UpdateResponseModel.Update_Response.Count > 0)
        //        {
        //            List<UpdateResponse> filteredData = UpdateResponseModel.Update_Response.Where(r => r.responselineId != string.Empty || r.responselineId != null || r.responselineId != "").ToList();
        //            var DistinctResponseHeaders = UpdateResponseModel.Update_Response.GroupBy(r => r.responseId).ToList();
        //            _logger.LogInformation($"Ditinct Headers Found ~{DistinctResponseHeaders.Count}");
        //            List<UpdateOperationModel> _operationList = new List<UpdateOperationModel>();
        //            if (DistinctResponseHeaders != null)
        //            {
        //                foreach (var item in DistinctResponseHeaders)
        //                {
        //                    UpdateOperationModel updateModel = new UpdateOperationModel();
        //                    updateModel.HeaderKey = item.Key;
        //                    updateModel.Response_Lines = item.ToList();
        //                    _operationList.Add(updateModel);
        //                }
        //            }
        //            //Get all Existing data from DataVerse
        //            foreach (var responseHeaderId in _operationList)
        //            {
        //                ResponseDataRoot existingResponses = GetResponseDataRoot(responseHeaderId.HeaderKey, _logger);
        //                if (existingResponses != null && existingResponses.value.Count > 0)
        //                {
        //                    List<UpdateResponse> requestedResponseLines = responseHeaderId.Response_Lines;
        //                    foreach (var existingResponse in existingResponses.value)
        //                    {
        //                        List<ResponseLinesData> existingResponseLines = existingResponse.responseLinesData;
        //                        if (requestedResponseLines.Count > 0)
        //                        {
        //                            foreach (var requestedLines in requestedResponseLines)
        //                            {
        //                                if (requestedLines.responselineId != null)
        //                                {
        //                                    var matchedLines = existingResponseLines.Where(r => r._aacn_response_value.ToLower() == requestedLines.responseId && r._aacn_question_value.ToLower() == requestedLines.questionId).ToList();
        //                                    if (matchedLines != null)
        //                                    {
        //                                        //delete the existing lines
        //                                        List<HttpResponseMessage> _deleteResponses = new List<HttpResponseMessage>();
        //                                        foreach (var item in matchedLines)
        //                                        {
        //                                            var responseLineId = item.aacn_response_lineid;
        //                                            HttpResponseMessage deleteResponses = await DeleteRecordsAsync($"aacn_response_lines({responseLineId})", _logger);
        //                                            if (deleteResponses.IsSuccessStatusCode)
        //                                            {
        //                                                existingResponseLines.RemoveAll(line => line.aacn_response_lineid == responseLineId);
        //                                                _logger.LogInformation("Record deleted successfully");
        //                                            }
        //                                            else
        //                                            {
        //                                                deleteResponses.StatusCode = HttpStatusCode.BadRequest;
        //                                            }
        //                                            _deleteResponses.Add(deleteResponses);
        //                                        }
        //                                        if (!_deleteResponses.Exists(r => r.StatusCode == HttpStatusCode.BadRequest))
        //                                        {
        //                                            //Create the new line--
        //                                            BeaconResponseLines responseLineData = new BeaconResponseLines();
        //                                            responseLineData.ResponseLineKey = requestedLines.responselineId;
        //                                            responseLineData.assessment_Line_Id = requestedLines.questionId;
        //                                            responseLineData.assessment_Line_Option_Id = requestedLines.answerdId;
        //                                            responseLineData.question_Id = requestedLines.questionId;
        //                                            responseLineData.answer_Id = requestedLines.answerdId;
        //                                            responseLineData.question_Text = requestedLines.questionText;
        //                                            responseLineData.AnswerText = requestedLines.answerText;
        //                                            HttpResponseMessage createResponse = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLineData), _logger);
        //                                            if (createResponse.IsSuccessStatusCode)
        //                                            {

        //                                            }
        //                                        }
        //                                    }
        //                                    else
        //                                    {
        //                                        BeaconResponseLines responseLineData = new BeaconResponseLines();
        //                                        responseLineData.ResponseLineKey = requestedLines.responselineId;
        //                                        responseLineData.assessment_Line_Id = requestedLines.questionId;
        //                                        responseLineData.assessment_Line_Option_Id = requestedLines.answerdId;
        //                                        responseLineData.question_Id = requestedLines.questionId;
        //                                        responseLineData.answer_Id = requestedLines.answerdId;
        //                                        responseLineData.question_Text = requestedLines.questionText;
        //                                        responseLineData.AnswerText = requestedLines.answerText;
        //                                        HttpResponseMessage createResponse = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLineData), _logger);
        //                                        if (createResponse.IsSuccessStatusCode)
        //                                        {

        //                                        }

        //                                    }
        //                                }
        //                                else
        //                                { ///Create the Response Lines


        //                                }


        //                            }

        //                        }

        //                    }
        //                }

        //            }


        //        }

        //    }
        //    catch (Exception ex)
        //    {
        //        response.StatusCode = (int)HttpStatusCode.BadRequest;
        //        response.Status = ex.Message;
        //        response.RecordId = Guid.Empty;
        //    }
        //    return _apiResponses;
        //}
        public async Task<List<APIResponse>> UpdateManagerResponsesApproach2(UpdateResponseModel UpdateResponseModel, ILogger<beaconController> _logger)
        {
            List<APIResponse> _apiResponses = new List<APIResponse>();
            APIResponse response = new APIResponse();
            try
            {
                if (UpdateResponseModel != null && UpdateResponseModel.Update_Response.Count > 0)
                {
                    var DistinctResponseHeaders = UpdateResponseModel.Update_Response.GroupBy(r => r.responseId).ToList();
                    _logger.LogInformation($"Ditinct Headers Found ~{DistinctResponseHeaders.Count}");
                    List<UpdateOperationModel> _operationList = new List<UpdateOperationModel>();
                    if (DistinctResponseHeaders != null)
                    {
                        foreach (var item in DistinctResponseHeaders)
                        {
                            UpdateOperationModel updateModel = new UpdateOperationModel();
                            updateModel.HeaderKey = item.Key;
                            updateModel.Response_Lines = item.ToList();
                            _operationList.Add(updateModel);
                        }
                    }
                    //Get all Existing data from DataVerse

                    foreach (var responseHeaderId in _operationList)
                    {
                        ResponseDataRoot existingResponses = GetResponseDataRoot(responseHeaderId.HeaderKey, _logger);
                        if (existingResponses != null && existingResponses.value.Count > 0)
                        {
                            List<Model.Manager.UpdateResponse> requestedResponseLines = responseHeaderId.Response_Lines;
                            foreach (var existingResponse in existingResponses.value)
                            {
                                List<ResponseLinesData> existingResponseLines = existingResponse.responseLinesData;
                                if (requestedResponseLines.Count > 0)
                                {
                                    foreach (var requestedLines in requestedResponseLines)
                                    {
                                        if (requestedLines.responselineId != null)
                                        {
                                            var QuestionResquestedLines = requestedResponseLines.Where(r => r.questionId.ToLower() == requestedLines.questionId).ToList();
                                            var matchedLines = existingResponseLines.Where(r => r._aacn_response_value.ToLower() == requestedLines.responseId && r._aacn_question_value.ToLower() == requestedLines.questionId).ToList();
                                            if (matchedLines != null)  //If we found matched lines - 
                                            {
                                                if (QuestionResquestedLines.Exists(data => data.questionId == requestedLines.questionId && data.answerdId == requestedLines.answerdId))
                                                {
                                                    //No Need to do anyting -- Match Found 
                                                }
                                                else
                                                {
                                                    //check Unmatched Data --//List of not matched answers
                                                    var notMatchedData = QuestionResquestedLines.Where(data => data.questionId == requestedLines.questionId && data.answerdId != requestedLines.answerdId).ToList();

                                                }
                                                //delete the existing lines
                                                List<HttpResponseMessage> _deleteResponses = new List<HttpResponseMessage>();
                                                foreach (var item in matchedLines)
                                                {
                                                    var responseLineId = item.aacn_response_lineid;
                                                    HttpResponseMessage deleteResponses = await DeleteRecordsAsync($"aacn_response_lines({responseLineId})", _logger);
                                                    if (deleteResponses.IsSuccessStatusCode)
                                                    {
                                                        existingResponseLines.RemoveAll(line => line.aacn_response_lineid == responseLineId);
                                                        _logger.LogInformation("Record deleted successfully");
                                                    }
                                                    else
                                                    {
                                                        deleteResponses.StatusCode = HttpStatusCode.BadRequest;
                                                    }
                                                    _deleteResponses.Add(deleteResponses);
                                                }
                                                if (!_deleteResponses.Exists(r => r.StatusCode == HttpStatusCode.BadRequest))
                                                {
                                                    //Create the new line--
                                                    BeaconResponseLines responseLineData = new BeaconResponseLines();
                                                    responseLineData.ResponseLineKey = requestedLines.responselineId;
                                                    responseLineData.assessment_Line_Id = requestedLines.questionId;
                                                    responseLineData.assessment_Line_Option_Id = requestedLines.answerdId;
                                                    responseLineData.question_Id = requestedLines.questionId;
                                                    responseLineData.answer_Id = requestedLines.answerdId;
                                                    responseLineData.question_Text = requestedLines.questionText;
                                                    responseLineData.AnswerText = requestedLines.answerText;
                                                    HttpResponseMessage createResponse = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLineData), _logger);
                                                    if (createResponse.IsSuccessStatusCode)
                                                    {

                                                    }
                                                }
                                            }
                                            else  //No lines with Same Quetion --Create a new Line
                                            {
                                                BeaconResponseLines responseLineData = new BeaconResponseLines();
                                                responseLineData.ResponseLineKey = requestedLines.responselineId;
                                                responseLineData.assessment_Line_Id = requestedLines.questionId;
                                                responseLineData.assessment_Line_Option_Id = requestedLines.answerdId;
                                                responseLineData.question_Id = requestedLines.questionId;
                                                responseLineData.answer_Id = requestedLines.answerdId;
                                                responseLineData.question_Text = requestedLines.questionText;
                                                responseLineData.AnswerText = requestedLines.answerText;
                                                HttpResponseMessage createResponse = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLineData), _logger);
                                                if (createResponse.IsSuccessStatusCode)
                                                {

                                                }

                                            }
                                        }
                                        else
                                        {
                                            //Create the Response Lines
                                            BeaconResponseLines responseLineData = new BeaconResponseLines();
                                            responseLineData.ResponseLineKey = requestedLines.responselineId;
                                            responseLineData.assessment_Line_Id = requestedLines.assessemntLineID;
                                            responseLineData.assessment_Line_Option_Id = requestedLines.assessmentOptionId;
                                            responseLineData.question_Id = requestedLines.questionId;
                                            responseLineData.answer_Id = requestedLines.answerdId;
                                            responseLineData.question_Text = requestedLines.questionText;
                                            responseLineData.AnswerText = requestedLines.answerText;
                                            responseLineData.response_Text = requestedLines.ResponseTextArea;
                                            HttpResponseMessage createResponse = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLineData), _logger);
                                            if (createResponse.IsSuccessStatusCode)
                                            {

                                            }

                                        }


                                    }

                                }

                            }
                        }

                        //If existing Responses not Available in D365 --
                        else
                        {


                        }

                    }


                }

            }
            catch (Exception ex)
            {
                response.StatusCode = (int)HttpStatusCode.BadRequest;
                response.Status = ex.Message;
                response.RecordId = Guid.Empty;
            }
            return _apiResponses;
        }
        public async Task<ResponseSubmitResponse> UpdateManagerResponsesApproach3(UpdateResponseModel UpdateResponseModel, ILogger<beaconController> _logger)
        {
            List<APIResponse> _apiResponses = new List<APIResponse>();
            ResponseSubmitResponse apiResponse = new ResponseSubmitResponse();
            APIResponse response = new APIResponse();
            StringBuilder _errrorList = new StringBuilder();
            _logger.LogInformation("Request Payload " + JsonConvert.SerializeObject(UpdateResponseModel));
            try
            {
                if (UpdateResponseModel != null && UpdateResponseModel.Update_Response.Count > 0)
                {
                    var DistinctResponseHeaders = UpdateResponseModel.Update_Response.GroupBy(r => r.responseId).ToList();
                    _logger.LogInformation($"Ditinct Headers Found ~{DistinctResponseHeaders.Count}");
                    List<UpdateOperationModel> _operationList = new List<UpdateOperationModel>();
                    if (DistinctResponseHeaders != null)
                    {
                        foreach (var item in DistinctResponseHeaders)
                        {
                            UpdateOperationModel updateModel = new UpdateOperationModel();
                            updateModel.HeaderKey = item.Key;
                            updateModel.Response_Lines = item.ToList();
                            _operationList.Add(updateModel);
                        }
                    }
                    bool Status = true;
                    int CreateCounter = 0;
                    int UpdateCounter = 0;
                    //Get all Existing data from DataVerse
                    foreach (var responseHeaderId in _operationList)
                    {
                        string ResponesHeaderId = responseHeaderId.HeaderKey.ToString();
                        ResponseDataRoot existingResponses = GetResponseDataRoot(responseHeaderId.HeaderKey, _logger);
                        List<Model.Manager.UpdateResponse> requestedResponseLines = responseHeaderId.Response_Lines;
                        if (existingResponses != null && existingResponses.value.Count > 0)
                        {
                            List<Model.Manager.UpdateResponse> CreateResponseLinesList = requestedResponseLines.Where(rr => rr.responselineId == null).ToList();
                            List<ResponseLinesData> existingResponseLines = existingResponses.value[0].responseLinesData;
                            var requestedId = new HashSet<string>(requestedResponseLines.Select(er => er.responselineId));
                            requestedId.Remove(null);
                            var filteredRequests = existingResponseLines.Where(rr => !requestedId.Contains(rr.aacn_response_lineid)).ToList();

                            List<HttpResponseMessage> _InactiveUpdateResponse = new List<HttpResponseMessage>();
                            //Inactive the Lines
                            foreach (var updateResponseLines in filteredRequests)
                            {
                                BeaconResponseLines responseLines = new BeaconResponseLines();
                                responseLines.ResponseLineKey = updateResponseLines.aacn_response_lineid;
                                responseLines.statecode = (int)StatusCode.Inactive;
                                HttpResponseMessage udpateResponse = await UpsertRecordAsync($"aacn_response_lines({updateResponseLines.aacn_response_lineid})", JsonConvert.SerializeObject(responseLines), "update", _logger);
                                if (!udpateResponse.IsSuccessStatusCode)
                                {
                                    Status = false;
                                    _errrorList.Append($"Error while updating - Response Key -  {ResponesHeaderId} & ResponseLine Key - {updateResponseLines.aacn_response_lineid} due to {udpateResponse.Content.ReadAsStringAsync().Result}");
                                }
                                _InactiveUpdateResponse.Add(udpateResponse);
                            }

                            if (_InactiveUpdateResponse.Any())
                            {
                                UpdateCounter += _InactiveUpdateResponse.Count;
                                var failedRequests = _InactiveUpdateResponse.Where(r => r.StatusCode == HttpStatusCode.BadRequest).ToList();
                                if (failedRequests != null && failedRequests.Count > 0)
                                {
                                    Status = false;
                                }

                            }
                            List<HttpResponseMessage> _CreateResponse = new List<HttpResponseMessage>();

                            //Create New Lines-- 
                            foreach (Model.Manager.UpdateResponse createRespnseLines in CreateResponseLinesList)
                            {
                                BeaconResponseLines responseLines = new BeaconResponseLines();

                                responseLines.Response_Id = ResponesHeaderId;
                                responseLines.assessment_Line_Id = createRespnseLines.assessemntLineID == null ? null : createRespnseLines.assessemntLineID;
                                responseLines.assessment_Line_Option_Id = createRespnseLines.assessmentOptionId == null ? null : createRespnseLines.assessmentOptionId;
                                responseLines.question_Id = createRespnseLines.questionId == null ? null : createRespnseLines.questionId;
                                responseLines.answer_Id = createRespnseLines.answerdId == null ? null : createRespnseLines.answerdId;
                                responseLines.question_Text = createRespnseLines.questionText == null ? null : createRespnseLines.questionText;
                                responseLines.AnswerText = createRespnseLines.answerText == null ? null : createRespnseLines.answerText;
                                responseLines.response_Text = createRespnseLines.ResponseTextArea == null ? null : createRespnseLines.ResponseTextArea;
                                HttpResponseMessage createResponse = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLines), _logger);
                                if (!createResponse.IsSuccessStatusCode)
                                {
                                    createResponse.StatusCode = HttpStatusCode.BadRequest;

                                    _errrorList.Append($"Error while Creating - Response Key -  {ResponesHeaderId} & Question Key - {createRespnseLines.questionId} & QuestionText - {createRespnseLines.questionText} due to {createResponse.Content.ReadAsStringAsync().Result}");
                                }
                                _CreateResponse.Add(createResponse);
                            }
                            if (_CreateResponse.Any())
                            {
                                CreateCounter += _CreateResponse.Count;
                                var failedRequests = _CreateResponse.Where(r => r.StatusCode == HttpStatusCode.BadRequest).ToList();
                                if (failedRequests != null && failedRequests.Count > 0)
                                {
                                    Status = false;
                                }

                            }
                        }
                    }
                    apiResponse.SyncStatus = Status;
                    apiResponse.CreatedLines = CreateCounter;
                    apiResponse.UpdatedLines = UpdateCounter;
                    apiResponse.Error = _errrorList.ToString();
                }

            }
            catch (Exception ex)
            {
                apiResponse.SyncStatus = false;
                apiResponse.CreatedLines = 0;
                apiResponse.UpdatedLines = 0;
            }

            _logger.LogInformation("Response Payload " + JsonConvert.SerializeObject(apiResponse));
            return apiResponse;
        }

        public async Task<List<SiteCoreResponseModel>> SyncSiteCoreResponses(SyncResponsesCommand siteCoreRequestModel, ILogger<beaconController> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            _logger.LogInformation(LoggerHelper.LogObjectValue(siteCoreRequestModel, JsonConvert.SerializeObject(siteCoreRequestModel)));
            SiteCoreResponseModel apiResponse = new SiteCoreResponseModel();
            var responseItems = new List<SiteCoreResponseModel>();

            try
            {
                if (siteCoreRequestModel != null)
                {
                    List<BeaconResponseLines> _responseLinesUpdateData = new List<BeaconResponseLines>();
                    #region Masters -- 
                    List<QuestionData> _questionMaster = GetQuestionMaster<beaconController>(_logger);
                    List<AnswerData> _anserMaster = GetAnswerMaster<beaconController>(_logger);
                    List<PostionData> _positionMasterList = null;
                    string postionId = string.Empty;
                    PositionDataModel positionDataModel = GetPositionMaster(_logger);
                    if (positionDataModel != null && positionDataModel.postiionrecords.Count > 0)
                    {
                        _positionMasterList = positionDataModel.postiionrecords;
                    }
                    if (_positionMasterList != null && _positionMasterList.Count > 0)
                    {
                        var positionRecord = _positionMasterList.FirstOrDefault(record => record.name.ToString().ToLower() == Utility.constants.postionConstants.POINTOFCONTACT.ToLower());
                        if (positionRecord != null)
                        {
                            postionId = positionRecord.positionid;
                        }
                    }
                    if (_questionMaster.Any())
                    {
                        _logger.LogInformation($"Question Master Found with ~{_questionMaster.Count}");
                    }
                    if (_anserMaster.Any())
                    {
                        _logger.LogInformation($"Answer Master Found with ~{_anserMaster.Count}");

                    }
                    string SurveyId = string.Empty;
                    string survey_Provider_ID = getSurveryProviderByName("Future - Beacon 3.x");
                    if (!string.IsNullOrEmpty(survey_Provider_ID))
                    {
                        SurveyId = $"/aacn_survey_providers({survey_Provider_ID})";
                    }
                    #endregion Masters --
                    List<SurveyResponseDto> surveyResponses = siteCoreRequestModel.SurveyResponses;
                    string RequestFor = "Create";
                    string query = "aacn_responses";
                    foreach (var item in surveyResponses)
                    {
                        string yearId = await GetYearId(item.CycleYear.ToString());
                        if (!string.IsNullOrEmpty(yearId))
                        {
                            SiteCoreResponseModel responseModel = new SiteCoreResponseModel();
                            try
                            {
                                RequestFor = "Create";
                                string ContactId = null;
                                if (item.PocCustomerKey != null && item.PocFirstName != null)
                                {
                                    ContactId = await CreatePOCData(postionId, item.PocCustomerKey.ToString(), item.PocFirstName, item.PocLastName, item.PocCustomerId.ToString(), _logger);
                                }
                                responseModel.ResponseKey = item.Key.ToString(); // Response HeaderId
                                BeaconRespnseData requestModel = new BeaconRespnseData();
                                requestModel.ResponseKey = item.Key.ToString(); // Response HeaderId
                                requestModel.survey_Provider_Name = SurveyId; // Survey Id passed as Static
                                requestModel.assessment_Id = item.AssessmentKey.ToString();
                                requestModel.Unit_Id = item.UnitKey != Guid.Empty ? item.UnitKey.ToString() : null;
                                requestModel.Facility_Id = item.OrganizationKey != Guid.Empty ? item.OrganizationKey.ToString() : null;
                                requestModel.member_Id = ContactId;
                                if (!string.IsNullOrEmpty(yearId))
                                    requestModel.Year = $"/aacn_years({yearId})";
                                item.ResponseLines = item.ResponseLines.GroupBy(r => r.Key).Select(g => g.First()).ToList();
                                List<ResponseLineDto> _InactiveResponseLines = item.ResponseLines.Where(r => r.IsDeleted != 0).ToList();

                                _logger.LogInformation($"Inactive Lines Counter - {_InactiveResponseLines.Count} & RequestLiens - {item.ResponseLines.Count} " +
                                    $"&& Active Lines {item.ResponseLines.Count - _InactiveResponseLines.Count}");

                                List<BeaconResponseLines> responseLines = new List<BeaconResponseLines>();
                                foreach (var responseLine in item.ResponseLines)
                                {
                                    BeaconResponseLines responseLineData = new BeaconResponseLines();
                                    responseLineData.ResponseLineKey = responseLine.Key != Guid.Empty ? responseLine.Key.ToString() : null; // Response Line Key
                                    responseLineData.assessment_Line_Id = responseLine.QuestionKey != Guid.Empty ? responseLine.QuestionKey.ToString() : null;
                                    responseLineData.assessment_Line_Option_Id = responseLine.SubquestionKey.ToString(); // SubQuestion Key
                                    responseLineData.question_Id = responseLine.QuestionKey.ToString();
                                    responseLineData.answer_Id = responseLine.SubquestionKey.ToString();
                                    if (responseLine.AnswerText != string.Empty)
                                    {
                                        responseLineData.response_Text = responseLine.AnswerText; // Answer Text
                                    }
                                    if (_anserMaster.Any() && responseLine.SubquestionKey != null)
                                    {
                                        string AnswerText = _anserMaster
                                           .Where(q => q.AnswerId == responseLine.SubquestionKey.ToString())
                                           .Select(q => q.Answer)
                                           .FirstOrDefault() ?? string.Empty;
                                        _logger.LogInformation($"Answer-Text ~ {AnswerText}");
                                        responseLineData.AnswerText = AnswerText;
                                    }
                                    if (_questionMaster.Any())
                                    {
                                        string QuestionText = _questionMaster
                                            .Where(q => q.questionid == responseLine.QuestionKey.ToString())
                                            .Select(q => q.question)
                                            .FirstOrDefault() ?? string.Empty;
                                        _logger.LogInformation($"Question-Text ~ {QuestionText}");
                                        responseLineData.question_Text = QuestionText;
                                    }
                                    responseLines.Add(responseLineData);
                                }
                                requestModel.response_Line = responseLines;
                                responseModel.RequestResponseLinesCounter = responseLines.Count;
                                query = "aacn_responses";
                                //Check Response Exist or not
                                HttpResponseMessage response = new HttpResponseMessage();
                                ResponseDataRoot responseData = GetResponseDataRootAllLines(requestModel.ResponseKey.ToString(), _logger);
                                if (responseData != null && responseData.value.Count > 0)
                                {
                                    _logger.LogInformation("Response Header found ~" + responseData.value[0].aacn_responseid);
                                    responses ResponseHeaderData = responseData.value.FirstOrDefault();
                                    if (ResponseHeaderData != null)
                                    {
                                        RequestFor = "Update";
                                        List<BeaconResponseLines> _responseLines = requestModel.response_Line;
                                        requestModel.response_Line = null;
                                        response = await UpsertRecordAsync($"{query}({requestModel.ResponseKey})", JsonConvert.SerializeObject(requestModel), RequestFor, _logger);
                                        _logger.LogInformation("Response Update Response ~" + response.StatusCode.ToString());
                                        if (response.StatusCode == HttpStatusCode.NoContent)
                                        {
                                            responseModel.SyncStatus = true;
                                            List<HttpResponseMessage> _responsesList = await UpdateandCreateSiteResponseLines(requestModel.ResponseKey, _responseLines, responseData, _logger);
                                            responseModel.SyncStatus = _responsesList.Exists(rr => rr.StatusCode != HttpStatusCode.BadRequest);
                                            responseModel.SyncedLinesCounter = _responseLines.Count;

                                            if (_InactiveResponseLines.Any())
                                            {
                                                bool status = await IactiveLineBulkUpdate(_InactiveResponseLines, _logger);
                                                _logger.LogInformation($" Update Status - {status}");
                                            }
                                        }
                                        ReviewAcitityResponseModel reviewResponses = new ReviewAcitityResponseModel();
                                        reviewResponses.Status = "Already-CREATED";
                                        responseModel.ReviewAcitityKey = reviewResponses;

                                        string responseId = ResponseHeaderData.aacn_responseid;
                                        if (!string.IsNullOrEmpty(responseId))
                                        {
                                            string assessmentActivityReviewQuery = string.Format(Utility.GetAssessmentReviewActivityId, responseId);
                                            HttpResponseMessage assessmentActivityReviewResposne = await GetRecordsAsync(assessmentActivityReviewQuery, _logger);
                                            if (assessmentActivityReviewResposne.IsSuccessStatusCode)
                                            {
                                                var assessmentReviewActivityModel = JsonConvert.DeserializeObject<AssessmentReviewActivityRootMain>(assessmentActivityReviewResposne.Content.ReadAsStringAsync().Result);
                                                if (assessmentReviewActivityModel != null && assessmentReviewActivityModel.value.Count > 0)
                                                {
                                                    AssessmentReviewActivityValues assessmentReviewActivitValueModel = assessmentReviewActivityModel.value.FirstOrDefault();
                                                    string assessmentReviewActivityId = assessmentReviewActivitValueModel.aacn_assessment_review_activityid;
                                                    if (!string.IsNullOrEmpty(assessmentReviewActivityId))
                                                    {
                                                        PostAssessmentReviewActivity postAssessmentReviewActivity = new PostAssessmentReviewActivity();
                                                        postAssessmentReviewActivity.UserResponseId = responseId;
                                                        postAssessmentReviewActivity.Assessment_Review_Activity_Number = item.ModuleId.ToString();
                                                        postAssessmentReviewActivity.ApplicationId = item.ApplicationId;
                                                    if(!string.IsNullOrEmpty(yearId))
                                                            postAssessmentReviewActivity.Year = $"/aacn_years({yearId})";
                                                        postAssessmentReviewActivity.Unit_Module_Id = item.AssessmentKey != Guid.Empty ? item.AssessmentKey.ToString() : null;
                                                       
                                                        postAssessmentReviewActivity.request_Status_Value = assessmentReviewActivitValueModel.requestStatusValue;
                                                        if (item.OrganizationKey == Guid.Empty && item.UnitKey != Guid.Empty)
                                                        {
                                                        string orgId =await getOrganizationByUnit(item.UnitKey.ToString());
                                                            postAssessmentReviewActivity.Organization_Id = orgId;
                                                        }
                                                        else
                                                        {
                                                            postAssessmentReviewActivity.Organization_Id = item.OrganizationKey != Guid.Empty ? item.OrganizationKey.ToString() : null;
                                                        }
                                                        postAssessmentReviewActivity.Unit_Id = item.UnitKey != Guid.Empty ? item.UnitKey.ToString() : null;
                                                        LoggerHelper.LogObjectValue(postAssessmentReviewActivity, JsonConvert.SerializeObject(postAssessmentReviewActivity));
                                                        HttpResponseMessage assessmentActivityUpdateResponse = UpdateRequest($"aacn_assessment_review_activities({assessmentReviewActivityId})", JsonConvert.SerializeObject(postAssessmentReviewActivity));
                                                        if (assessmentActivityUpdateResponse.IsSuccessStatusCode)
                                                        {
                                                            responseModel.UpdateReviewActivity = "Assessment-Review-Activity Updated";
                                                            _logger.LogInformation($"AssessmentReviewActivity Update StatusCode : {assessmentActivityUpdateResponse.StatusCode.ToString()}");
                                                        }
                                                        else
                                                        {
                                                            responseModel.UpdateReviewActivity = "Assessment-Review-Activity Update - Failed";
                                                            _logger.LogInformation($"AssessmentReviewActivity Update Failed : {assessmentActivityUpdateResponse.StatusCode.ToString()}");
                                                            _logger.LogInformation(assessmentActivityUpdateResponse.Content.ReadAsStringAsync().Result);
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                    }
                                }
                                else
                                {

                                    response = await UpsertRecordAsync(query, JsonConvert.SerializeObject(requestModel), RequestFor, _logger);
                                    _logger.LogInformation("Response Create Response ~" + response.StatusCode.ToString());

                                    if (_InactiveResponseLines.Any())
                                    {
                                        bool status = await IactiveLineBulkUpdate(_InactiveResponseLines, _logger);
                                        _logger.LogInformation($" Update Status - {status}");
                                    }

                                    _logger.LogInformation($"Response Created {response.StatusCode.ToString()}");
                                    if (response.StatusCode == HttpStatusCode.NoContent)
                                    {
                                        responseModel.SyncStatus = true;
                                        ReviewAcitityResponseModel reviewResponses = new ReviewAcitityResponseModel();
                                        if (RequestFor.ToLower() == "create")
                                        {
                                            responseModel.SyncedLinesCounter = requestModel.response_Line.Count;
                                        }
                                        var _recordUrl = response.Headers.GetValues("OData-EntityId").FirstOrDefault();
                                        string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                                        var ResponseKey = splitRetrievedData[1].ToString();
                                        apiResponse.ResponseKey = ResponseKey;
                                        PostAssessmentReviewActivity postAssessmentReviewActivity = new PostAssessmentReviewActivity();
                                        postAssessmentReviewActivity.UserResponseId = ResponseKey;
                                        postAssessmentReviewActivity.Assessment_Review_Activity_Number = item.ModuleId.ToString();
                                        postAssessmentReviewActivity.ApplicationId = item.ApplicationId;
                                        if (!string.IsNullOrEmpty(yearId))
                                            postAssessmentReviewActivity.Year = $"/aacn_years({yearId})";
                                        postAssessmentReviewActivity.Unit_Module_Id = item.AssessmentKey.ToString();
                                        
                                        postAssessmentReviewActivity.request_Status_Value = (int)RequestStatus.Ready;
                                        if (item.OrganizationKey == Guid.Empty && item.UnitKey != Guid.Empty)
                                        {
                                        string orgId =await getOrganizationByUnit(item.UnitKey.ToString());
                                            postAssessmentReviewActivity.Organization_Id = orgId;
                                        }
                                        else
                                        {
                                            postAssessmentReviewActivity.Organization_Id = item.OrganizationKey != Guid.Empty ? item.OrganizationKey.ToString() : null;
                                        }
                                        postAssessmentReviewActivity.Unit_Id = item.UnitKey != Guid.Empty ? item.UnitKey.ToString() : null;
                                        LoggerHelper.LogObjectValue(postAssessmentReviewActivity, JsonConvert.SerializeObject(postAssessmentReviewActivity));
                                        HttpResponseMessage acitivityResponse = await CreateRecordWithRetry("aacn_assessment_review_activities", JsonConvert.SerializeObject(postAssessmentReviewActivity), _logger);
                                        _logger.LogInformation($" - Activity Response Created ~ {acitivityResponse.StatusCode.ToString()}");
                                        if (acitivityResponse.StatusCode == HttpStatusCode.NoContent)
                                        {
                                            string activityKey = acitivityResponse.Headers.GetValues("OData-EntityId").FirstOrDefault().ToString().Split('[', '(', ')', ']')[1].ToString();
                                            reviewResponses.ReviewAcitivityKey = activityKey;
                                        }
                                        else
                                        {
                                            reviewResponses.ReviewAcitivityKey = null;
                                            reviewResponses.ErrorMessage = acitivityResponse.Content.ReadAsStringAsync().Result;
                                        }
                                        responseModel.ReviewAcitityKey = reviewResponses;
                                    }
                                    else
                                    {
                                        responseModel.SyncStatus = false;
                                        responseModel.ErrorMessage = response.Content.ReadAsStringAsync().Result;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                                LoggerHelper.LogException(_logger, ex);
                                responseModel.SyncStatus = false;
                                responseModel.ErrorMessage = ex.Message;
                            }

                            responseModel.OperationType = RequestFor;
                            responseItems.Add(responseModel);
                        }
                        else
                        {
                            SiteCoreResponseModel responseYearModel = new SiteCoreResponseModel();
                            responseYearModel.ResponseKey = item.Key.ToString();
                            responseYearModel.YearSyncStatus = false;
                            responseYearModel.ErrorMessage = $"Provided Year {item.CycleYear} does not exists in D365";
                            responseItems.Add(responseYearModel);
                        }
                    }
                }
                LoggerHelper.LogObjectValue(responseItems, JsonConvert.SerializeObject(responseItems));
                return responseItems;

            }
            catch (Exception ex)
            {
                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                apiResponse.ErrorMessage = ex.Message;
                LoggerHelper.LogException(_logger, ex);
                responseItems.Add(new SiteCoreResponseModel
                {
                    ResponseKey = null,
                    ReviewAcitityKey = null,
                    SyncStatus = false,
                    ErrorMessage = ex.Message
                });

            }
            var failedRequests = responseItems.Where(r => r.SyncStatus == false).ToList();
            if (responseItems.Count > 0 && failedRequests != null)
            {
                _logger.LogError($"Sync Failed {JsonConvert.SerializeObject(responseItems)}");
            }
            return responseItems;
        }


        #endregion D365_TO_Portal_Internal_API's - Methods
        #region Private-Methods
        private List<QuestionData> GetQuestionMaster<T>(ILogger<T> _logger)
        {
            QuestionModel questionData = new QuestionModel();
            try
            {
                string questionMasterQuery = string.Format(Utility.GetQuestionMaster);
                HttpResponseMessage answerResponse = GetRecords(questionMasterQuery);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    questionData = JsonConvert.DeserializeObject<QuestionModel>(answerResponse.Content.ReadAsStringAsync().Result);
                    if (questionData != null)
                    {
                        return questionData.value;
                    }
                }

                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new List<QuestionData>();
            }
            return new List<QuestionData>();

        }
        private List<AnswerData> GetAnswerMaster<T>(ILogger<T> _logger)
        {
            AnswerModel answerModel = new AnswerModel();
            try
            {
                string questionMasterQuery = string.Format(Utility.GetAnswerMaster);
                HttpResponseMessage answerResponse = GetRecords(questionMasterQuery);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    answerModel = JsonConvert.DeserializeObject<AnswerModel>(answerResponse.Content.ReadAsStringAsync().Result);
                    if (answerModel != null)
                    {
                        return answerModel.value;
                    }
                }

                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new List<AnswerData>();
            }
            return new List<AnswerData>();

        }
        private PositionDataModel GetPositionMaster<T>(ILogger<T> _logger)
        {
            string RecordId = string.Empty;
            PositionDataModel data = new PositionDataModel();
            try
            {
                string positiondataQuery = string.Format(Utility.GetPositionByName);
                HttpResponseMessage answerResponse = GetRecords(positiondataQuery);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    return JsonConvert.DeserializeObject<PositionDataModel>(answerResponse.Content.ReadAsStringAsync().Result);

                }

                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            }
            catch (Exception ex)
            {
                return data;
            }
            return data;
        }
        private async Task<List<HttpResponseMessage>> UpdateandCreateResponseLines(string responseId, List<BeaconResponseLines> _responseList, ILogger<beaconController> _logger)
        {
            List<HttpResponseMessage> _httpResponseList = new List<HttpResponseMessage>();
            try
            {
                ResponseDataRoot responseData = GetResponseDataRoot(responseId, _logger);
                if (responseData != null)
                {
                    responses ResponseRoot = responseData.value.FirstOrDefault();
                    List<ResponseLinesData> _existingResponseLines = ResponseRoot.responseLinesData;
                    if (_existingResponseLines != null)
                    {

                        foreach (var responseLine in _responseList)
                        {
                            HttpResponseMessage httpresponseitem = new HttpResponseMessage();
                            try
                            {
                                string ResponseLineKey = responseLine.ResponseLineKey ?? string.Empty;
                                // Check if there is a matching response line in the existing list
                                var existingLine = _existingResponseLines
                                    .FirstOrDefault(e => e.aacn_response_lineid == ResponseLineKey);

                                //Create --
                                if (existingLine == null && ResponseLineKey == string.Empty)
                                {
                                    responseLine.Response_Id = responseId;
                                    httpresponseitem = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLine), _logger);
                                    LoggerHelper.LogObjectValue(httpresponseitem, httpresponseitem.Content.ToString());
                                }
                                else
                                {
                                    httpresponseitem = await UpdateRequestAsync($"aacn_response_lines({existingLine.aacn_response_lineid})", responseLine, _logger);
                                }
                            }
                            catch (Exception ex)
                            {
                                string OperationType = string.IsNullOrEmpty(responseLine.ResponseLineKey) ? "Creation" : "Update";
                                httpresponseitem.StatusCode = HttpStatusCode.BadRequest;
                                string Content = string.Empty;
                                if (OperationType.ToLower() == "update")
                                {
                                    Content = $" Line {OperationType} Failed for {(responseLine.ResponseLineKey)} beacuse {ex.Message}";
                                }
                                else
                                {
                                    Content = $" Line {OperationType} Failed for beacuse {ex.Message}";
                                }
                                httpresponseitem.Content = new StringContent(Content);
                            }
                            _httpResponseList.Add(httpresponseitem);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _httpResponseList.Add(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent(ex.Message)
                });
            }

            return _httpResponseList;
        }
        private async Task<List<HttpResponseMessage>> UpdateandCreateSiteResponseLinesOld(string responseId, List<BeaconResponseLines> _responseList, ResponseDataRoot responseData, ILogger<beaconController> _logger)
        {
            List<HttpResponseMessage> _httpResponseList = new List<HttpResponseMessage>();
            try
            {
                if (responseData != null && responseData.value.Count > 0)
                {
                    responses ResponseRoot = responseData.value.FirstOrDefault();
                    List<ResponseLinesData> _existingResponseLines = ResponseRoot.responseLinesData;
                    if (_existingResponseLines != null && _existingResponseLines.Count > 0)
                    {
                        foreach (var responseLine in _responseList)
                        {
                            HttpResponseMessage httpresponseitem = new HttpResponseMessage();
                            try
                            {
                                // Check if there is a matching response line in the existing list
                                var existingLine = _existingResponseLines
                                    .FirstOrDefault(e => e.aacn_response_lineid == responseLine.ResponseLineKey);
                                //Create --
                                if (existingLine == null)
                                {
                                    responseLine.Response_Id = responseId;
                                    httpresponseitem = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLine), _logger);
                                    LoggerHelper.LogObjectValue(httpresponseitem, httpresponseitem.Content.ToString());
                                }
                                else
                                {   //Update --
                                    httpresponseitem = await UpdateRequestAsync($"aacn_response_lines({existingLine.aacn_response_lineid})", responseLine, _logger);
                                    LoggerHelper.LogObjectValue(httpresponseitem, httpresponseitem.Content.ToString());
                                }

                            }
                            catch (Exception ex)
                            {
                                string OperationType = string.IsNullOrEmpty(responseLine.ResponseLineKey) ? "Creation" : "Update";
                                httpresponseitem.StatusCode = HttpStatusCode.BadRequest;
                                string Content = string.Empty;
                                if (OperationType.ToLower() == "update")
                                {
                                    Content = $" Line {OperationType} Failed for {(responseLine.ResponseLineKey)} beacuse {ex.Message}";
                                }
                                else
                                {
                                    Content = $" Line {OperationType} Failed for beacuse {ex.Message}";
                                }
                                httpresponseitem.Content = new StringContent(Content);
                            }
                            _httpResponseList.Add(httpresponseitem);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _httpResponseList.Add(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent(ex.Message)
                });
            }

            return _httpResponseList;
        }
        private ResponseDataRoot GetResponseDataRoot(string responseId, ILogger<beaconController> _logger)
        {
            ResponseDataRoot responseData = new ResponseDataRoot();
            try
            {
                string responseQuery = string.Format(Utility.GetResponseDataRoot, responseId);
                HttpResponseMessage answerResponse = GetRecords(responseQuery);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    responseData = JsonConvert.DeserializeObject<ResponseDataRoot>(answerResponse.Content.ReadAsStringAsync().Result);
                    if (responseData != null)
                    {
                        return responseData;
                    }
                }

                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new ResponseDataRoot();
            }
            return new ResponseDataRoot();
        }

        private ResponseDataRoot GetResponseDataRootAllLines(string responseId, ILogger<beaconController> _logger)
        {
            ResponseDataRoot responseData = new ResponseDataRoot();
            try
            {
                string responseQuery = string.Format(Utility.GetResponseDataRootAllLines, responseId);
                HttpResponseMessage answerResponse = GetRecords(responseQuery);
                _logger.LogInformation(LoggerHelper.LogObjectValue(answerResponse, answerResponse.StatusCode.ToString()));
                if (answerResponse.StatusCode == HttpStatusCode.OK)
                {
                    responseData = JsonConvert.DeserializeObject<ResponseDataRoot>(answerResponse.Content.ReadAsStringAsync().Result);
                    if (responseData != null)
                    {
                        return responseData;
                    }
                }

                _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return new ResponseDataRoot();
            }
            return new ResponseDataRoot();
        }
        private async Task<HttpResponseMessage> UpdateAcitivityReviwerAssessment<T>(string newResponseId, string ReviewActivityNumber, string requestFor, ILogger<T> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            string Exception = "";
            try
            {
                var assessmentReviewActivtyRoot = await GetAssessmentReviewActivityByNumber(ReviewActivityNumber, _logger);
                if (assessmentReviewActivtyRoot != null && assessmentReviewActivtyRoot.assessmentReviews.Count > 0)
                {
                    string activityId = string.Empty;
                    if (!string.IsNullOrEmpty(assessmentReviewActivtyRoot.assessmentReviews.FirstOrDefault().Assessment_Review_ActivityId))
                    {
                        //Update the Assessment Review Activity
                        PostAssessmentReviewActivity patchAssessmentReviewActivity = new PostAssessmentReviewActivity();
                        if (!string.IsNullOrEmpty(newResponseId))
                        {
                            patchAssessmentReviewActivity.ReviewerResponseId = newResponseId;
                            if (requestFor.ToLower() == "save")
                            {
                                patchAssessmentReviewActivity.ApplicationId = Convert.ToInt64(assessmentReviewActivtyRoot.assessmentReviews[0].ApplicationId);
                                patchAssessmentReviewActivity.request_Status_Value = (int)RequestStatus.InProgress;
                                patchAssessmentReviewActivity.InprogressDate = DateTime.UtcNow;
                            }
                            else if (requestFor.ToLower() == "submit")
                            {
                                patchAssessmentReviewActivity.ApplicationId = Convert.ToInt64(assessmentReviewActivtyRoot.assessmentReviews[0].ApplicationId);
                                patchAssessmentReviewActivity.request_Status_Value = (int)RequestStatus.Complete;
                                patchAssessmentReviewActivity.CompletedDate = DateTime.UtcNow;
                            }
                        }
                        if (assessmentReviewActivtyRoot.assessmentReviews.Count > 0)
                        {
                            activityId = assessmentReviewActivtyRoot.assessmentReviews[0].Assessment_Review_ActivityId.ToString();
                        }

                        string odataQuery = $"aacn_assessment_review_activities({activityId})";
                        _logger.LogInformation(LoggerHelper.LogObjectValue(patchAssessmentReviewActivity, JsonConvert.SerializeObject(patchAssessmentReviewActivity)));
                        return await UpdateRequestAsync(odataQuery, patchAssessmentReviewActivity, _logger);
                    }
                }

            }
            catch (Exception ex)
            {
                Exception = ex.Message;
                LoggerHelper.LogException(_logger, ex);
            }

            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return new HttpResponseMessage { StatusCode = HttpStatusCode.BadRequest, Content = new StringContent(Exception) };
        }
        private async Task<AssessmentReviewActivtyRoot> GetAssessmentReviewActivityByNumber<T>(string ReviewActivityNumber, ILogger<T> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            try
            {
                string AssessmentReviewActivityQuery = string.Format(Utility.GetAssessmentReviewActivitybyNumber, ReviewActivityNumber);
                HttpResponseMessage answerResponse = await GetRecordsAsync(AssessmentReviewActivityQuery, _logger);
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    return JsonConvert.DeserializeObject<AssessmentReviewActivtyRoot>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return new AssessmentReviewActivtyRoot();
        }
        private static BeaconResponseByAction GetResponseCounterByRequestStatus<T>(AssessmentReviewActivtyRootModel assessmentReviewActivtyRoot, ILogger<T> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            int allcounter = 0;
            BeaconResponseByAction beaconResponseByAction = new BeaconResponseByAction();
            var valueCounts = assessmentReviewActivtyRoot.assessmentReviews.Select(r => r.Request_Staus_Display_Value).GroupBy(v => v).Select(g => new { Value = g.Key, Count = g.Count() }).ToList();
            foreach (var item in valueCounts)
            {
                if (item.Value.ToString() == ActionStatusConstants.READY)
                {
                    beaconResponseByAction.Ready = item.Count.ToString();
                    allcounter = allcounter + item.Count;
                }
                else if (item.Value.ToString() == ActionStatusConstants.ASSIGNED)
                {
                    beaconResponseByAction.Assigned = item.Count.ToString();
                    allcounter = allcounter + item.Count;
                }
                else if (item.Value.ToString() == ActionStatusConstants.ACCEPTED)
                {
                    beaconResponseByAction.Accepted = item.Count.ToString();
                    allcounter = allcounter + item.Count;
                }
                else if (item.Value.ToString() == ActionStatusConstants.REJECTED)
                {
                    beaconResponseByAction.Rejected = item.Count.ToString();
                    allcounter = allcounter + item.Count;
                }
                else if (item.Value.ToString() == ActionStatusConstants.IN_PROGRESS)
                {
                    beaconResponseByAction.In_Progress = item.Count.ToString();
                    allcounter = allcounter + item.Count;
                }
                else if (item.Value.ToString() == ActionStatusConstants.COMPLETE)
                {
                    beaconResponseByAction.Complete = item.Count.ToString();
                    allcounter = allcounter + item.Count;
                }
                else if (item.Value.ToString() == ActionStatusConstants.SUBMITTED)
                {
                    beaconResponseByAction.Submitted = item.Count.ToString();
                    allcounter = allcounter + item.Count;
                }
                beaconResponseByAction.All = allcounter.ToString();
            }
            _logger.LogInformation(LoggerHelper.LogObjectValue(beaconResponseByAction, JsonConvert.SerializeObject(beaconResponseByAction)));
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));

            return beaconResponseByAction;
        }
        private static BeaconResponseByAction GetResponseCounterByRequestStatusMain<T>(AssessmentReviewActivtyRootModel assessmentReviewActivtyRoot, ILogger<T> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            BeaconResponseByAction beaconResponseByAction = new BeaconResponseByAction();
            var valueCounts = assessmentReviewActivtyRoot.assessmentReviews.Select(r => r.Request_Staus_Display_Value).GroupBy(v => v).Select(g => new { Value = g.Key, Count = g.Count() }).ToList();
            foreach (var item in valueCounts)
            {
                if (item.Value.ToString() == ActionStatusConstants.READY)
                {
                    beaconResponseByAction.Ready = item.Count.ToString();
                }
                else if (item.Value.ToString() == ActionStatusConstants.ASSIGNED)
                {
                    beaconResponseByAction.Assigned = item.Count.ToString();
                }
                else if (item.Value.ToString() == ActionStatusConstants.ACCEPTED)
                {
                    beaconResponseByAction.Accepted = item.Count.ToString();
                }
                else if (item.Value.ToString() == ActionStatusConstants.REJECTED)
                {
                    beaconResponseByAction.Rejected = item.Count.ToString();
                }
                else if (item.Value.ToString() == ActionStatusConstants.IN_PROGRESS)
                {
                    beaconResponseByAction.In_Progress = item.Count.ToString();
                }
                else if (item.Value.ToString() == ActionStatusConstants.COMPLETE)
                {
                    beaconResponseByAction.Complete = item.Count.ToString();
                }
                else if (item.Value.ToString() == ActionStatusConstants.SUBMITTED)
                {
                    beaconResponseByAction.Submitted = item.Count.ToString();
                }
            }
            _logger.LogInformation(LoggerHelper.LogObjectValue(beaconResponseByAction, JsonConvert.SerializeObject(beaconResponseByAction)));
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));

            return beaconResponseByAction;
        }
        private async Task<AccountModelRoot> GetUnitDataById<T>(string UnitIds, ILogger<T> _logger)
        {
            AccountModelRoot accountModelRoot = new AccountModelRoot();
            try
            {
                HttpResponseMessage httpResponse = await GetListofRecordsByGUID("accounts", UnitIds, _logger);
                if (httpResponse.StatusCode == HttpStatusCode.OK)
                {
                    return JsonConvert.DeserializeObject<AccountModelRoot>(httpResponse.Content.ReadAsStringAsync().Result);
                }
            }
            catch (Exception ex)
            {

            }
            return accountModelRoot;

        }
        private async Task<AccountModelRoot> GetOrganizationDataById<T>(string UnitIds, ILogger<T> _logger)
        {
            AccountModelRoot accountModelRoot = new AccountModelRoot();
            try
            {
                HttpResponseMessage httpResponse = await GetListofRecordsByGUID("accounts", UnitIds, _logger);
                if (httpResponse.StatusCode == HttpStatusCode.OK)
                {
                    return JsonConvert.DeserializeObject<AccountModelRoot>(httpResponse.Content.ReadAsStringAsync().Result);
                }
            }
            catch (Exception ex)
            {

            }
            return accountModelRoot;

        }
        private async Task<ContactModelRoot> GetReviewerDataById<T>(string UnitIds, ILogger<T> _logger)
        {
            ContactModelRoot ModelRoot = new ContactModelRoot();
            try
            {
                HttpResponseMessage httpResponse = await GetListofRecordsByGUID("contacts", UnitIds, _logger);
                if (httpResponse.StatusCode == HttpStatusCode.OK)
                {
                    return JsonConvert.DeserializeObject<ContactModelRoot>(httpResponse.Content.ReadAsStringAsync().Result);
                }
            }
            catch (Exception ex)
            {

            }
            return ModelRoot;
        }
        public async Task<HttpResponseMessage> GetListofRecordsByGUID<T>(string EntityName, string recordId, ILogger<T> _logger)
        {
            HttpResponseMessage response = new HttpResponseMessage();
            try
            {
                string entityName = EntityName;
                string fieldstoretrieve = $"{EntityName.Substring(0, EntityName.Length - 1)}id";
                string QueryString = string.Format(GetEntityRecordsbyGUId, entityName, fieldstoretrieve, fieldstoretrieve, recordId);
                return await GetRecordsAsync(QueryString, _logger);

            }
            catch (Exception ex)
            {
                response.StatusCode = HttpStatusCode.BadRequest;
                response.Content = new StringContent(ex.Message);
            }
            return response;
        }
        private async Task<ReviewerAssessmentLoadResponseModel> GetResponseDataRootById<T>(string ResponseId, ILogger<T> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));

            ReviewerAssessmentLoadResponseModel responseData = new ReviewerAssessmentLoadResponseModel();
            try
            {
                string query = string.Format(Utility.GetReviewerAndUserResponses, ResponseId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(query, _logger);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                        return JsonConvert.DeserializeObject<ReviewerAssessmentLoadResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    }

                }
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return responseData;
        }
        private async Task<AssessmentReviewActivtyRoot> GetReviewerVerificationData<T>(string unitId, ILogger<T> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));

            AssessmentReviewActivtyRoot assessmentReviewActivtyRoot = new AssessmentReviewActivtyRoot();
            try
            {
                string query = string.Format(Utility.verifyUnitResponses, unitId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(query, _logger);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
                        return JsonConvert.DeserializeObject<AssessmentReviewActivtyRoot>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    }

                }
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return assessmentReviewActivtyRoot;
        }
        private async Task<string> GetGroupText<T>(string LineId, ILogger<T> _logger)
        {
            string ResponseText = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(LineId))
                {
                    string QueryData = string.Format(Utility.GetGroupNameByRefId, LineId);
                    HttpResponseMessage answerResponse = await GetRecordsAsync(QueryData, _logger);
                    if (answerResponse.IsSuccessStatusCode)
                    {
                        GroupNameModel dataModel = JsonConvert.DeserializeObject<GroupNameModel>(answerResponse.Content.ReadAsStringAsync().Result);
                        if (dataModel != null && dataModel.groupData.Count > 0)
                        {
                            GroupData groupData = dataModel.groupData.FirstOrDefault();
                            if (groupData != null)
                            {
                                return groupData.tool_tipValue == null ? string.Empty : groupData.tool_tipValue;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return ResponseText;
        }

        /* For Manager Front Load*/
        //For Reviewer/Unit Module/User Response/ReviewerResponses and Unit Assessments
        private async Task<AssessmentdataRecords> GetAssessmentData<t>(string assessmentId, ILogger<t> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            AssessmentdataRecords assessmentdataRecords = new AssessmentdataRecords();
            try
            {
                string assessmentQuery = string.Format(Utility.GetUnitModule_Manager, assessmentId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(assessmentQuery, _logger);
                if (answerResponse.IsSuccessStatusCode)
                {
                    AACN.API.Helper.AssessmentHelper.AssessmentHelper assessmentHelper = JsonConvert.DeserializeObject<AACN.API.Helper.AssessmentHelper.AssessmentHelper>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    if (assessmentHelper != null && assessmentHelper.assessmentData.Count > 0)
                    {
                        assessmentdataRecords = assessmentHelper.assessmentData.FirstOrDefault();

                    }
                }
            }
            catch (Exception ex)
            {

            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return assessmentdataRecords;
        }
        private async Task<ReviewerAssessmentLoadResponseModel> GetReviewerModule<t>(string activityId, ILogger<t> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            ReviewerAssessmentLoadResponseModel reviewerAssessmentLoadResponseModel = new ReviewerAssessmentLoadResponseModel();
            try
            {
                string GetReviewerModule_ManagerQuery = string.Format(Utility.GetReviewerModule_Manager, activityId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(GetReviewerModule_ManagerQuery, _logger);
                if (answerResponse.IsSuccessStatusCode)
                {
                    return JsonConvert.DeserializeObject<ReviewerAssessmentLoadResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
            }
            catch (Exception ex)
            {

            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return reviewerAssessmentLoadResponseModel;
        }
        private async Task<ReviewerAssessmentLoadResponseModel> GetUnitModule<t>(string activityId, ILogger<t> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            ReviewerAssessmentLoadResponseModel reviewerAssessmentLoadResponseModel = new ReviewerAssessmentLoadResponseModel();
            try
            {
                string GetUnitModuleFromActivity = string.Format(Utility.GetUnitModuleFromActivity, activityId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(GetUnitModuleFromActivity, _logger);
                if (answerResponse.IsSuccessStatusCode)
                {
                    return JsonConvert.DeserializeObject<ReviewerAssessmentLoadResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
            }
            catch (Exception ex)
            {

            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return reviewerAssessmentLoadResponseModel;
        }
        private async Task<ReviewerAssessmentLoadResponseModel> GetReviewerResponse<t>(string activityId, ILogger<t> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            ReviewerAssessmentLoadResponseModel reviewerAssessmentLoadResponseModel = new ReviewerAssessmentLoadResponseModel();
            try
            {
                string assessmentQuery = string.Format(Utility.GetReviewerResponse_Manager, activityId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(assessmentQuery, _logger);
                if (answerResponse.IsSuccessStatusCode)
                {
                    return JsonConvert.DeserializeObject<ReviewerAssessmentLoadResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
            }
            catch (Exception ex)
            {

            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return reviewerAssessmentLoadResponseModel;
        }
        private async Task<ReviewerAssessmentLoadResponseModel> GetUserResponse<t>(string activityId, ILogger<t> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            ReviewerAssessmentLoadResponseModel reviewerAssessmentLoadResponseModel = new ReviewerAssessmentLoadResponseModel();
            try
            {
                string assessmentQuery = string.Format(Utility.GetUserResponse_Manager, activityId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(assessmentQuery, _logger);
                if (answerResponse.IsSuccessStatusCode)
                {
                    return JsonConvert.DeserializeObject<ReviewerAssessmentLoadResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
            }
            catch (Exception ex)
            {

            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return reviewerAssessmentLoadResponseModel;
        }
        private async Task<ReviewAcitiyDataModel> GetReviewerResponseByReviewerID<t>(string UnitId,string yearId, ILogger<t> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            ReviewAcitiyDataModel reviewAcitiyDataModel = new ReviewAcitiyDataModel();
            try
            {
                string assessmentQuery = string.Format(Utility.getReviewAcitivitesByUnitIdUpdated, UnitId, yearId);
                HttpResponseMessage answerResponse = await GetRecordsAsync(assessmentQuery, _logger);
                if (answerResponse.IsSuccessStatusCode)
                {
                    return JsonConvert.DeserializeObject<ReviewAcitiyDataModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
            }
            catch (Exception ex)
            {

            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return reviewAcitiyDataModel;
        }
        private async Task<ReviewerAssessmentLoadResponseModel> GetUserResponseByUnitAssessment<t>(string unitId,string unitModuleKey, ILogger<t> _logger)
        {
            _logger.LogInformation(LoggerHelper.LogMethodStart(_logger));
            ReviewerAssessmentLoadResponseModel reviewerAssessmentLoadResponseModel = new ReviewerAssessmentLoadResponseModel();
            try
            {
                string assessmentQuery = string.Format(Utility.GetUserResponse_UnitModdoule, unitId, unitModuleKey);
                HttpResponseMessage answerResponse = await GetRecordsAsync(assessmentQuery, _logger);
                if (answerResponse.IsSuccessStatusCode)
                {
                    return JsonConvert.DeserializeObject<ReviewerAssessmentLoadResponseModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                }
            }
            catch (Exception ex)
            {

            }
            _logger.LogInformation(LoggerHelper.LogMethodEnd(_logger));
            return reviewerAssessmentLoadResponseModel;
        }
        private async Task<ManagerDataModel> UpdateManageModel<T>(string activityKey, ReviewAcitiyData matchedData, ILogger<T> _logger)
        {
            ManagerDataModel responseModel = new ManagerDataModel();
            responseModel.assessmentReviewActivity = new List<Acvitydata>();
            responseModel.assessmentReviewActivity.Add(new Acvitydata());
            try
            {
                if (matchedData != null)
                {
                    string activityId = matchedData.aacn_assessment_review_activityid == null ? string.Empty : matchedData.aacn_assessment_review_activityid;
                    ReviewerAssessmentLoadResponseModel reviewerModule = await GetReviewerModule(activityId, _logger);
                    if (reviewerModule != null)
                    {
                        if (reviewerModule.assessmentReviewActivity.Count > 0)
                        {
                            if (reviewerModule.assessmentReviewActivity[0].Reviewer_Module != null)
                            {
                                responseModel.assessmentReviewActivity[0].Reviewer_Module = reviewerModule.assessmentReviewActivity[0].Reviewer_Module;
                                if (responseModel.assessmentReviewActivity[0].Reviewer_Module != null && responseModel.assessmentReviewActivity[0].Reviewer_Module.AssessmentsLines.Any())
                                {
                                    responseModel.assessmentReviewActivity[0].Reviewer_Module.AssessmentsLines = AssessmentHelperExtensions.SortAssessments(responseModel.assessmentReviewActivity[0].Reviewer_Module.AssessmentsLines);
                                }
                            }

                        }
                    }
                    ReviewerAssessmentLoadResponseModel userResponse = await GetUserResponse(activityId, _logger);
                    if (userResponse != null)
                    {
                        if (userResponse.assessmentReviewActivity.Count > 0)
                        {
                            if (userResponse.assessmentReviewActivity[0].UserResponses != null)
                            {
                                responseModel.assessmentReviewActivity[0].UserResponses = userResponse.assessmentReviewActivity[0].UserResponses;
                            }

                        }
                    }
                    ReviewerAssessmentLoadResponseModel reviewerResponse = await GetReviewerResponse(activityId, _logger);
                    if (reviewerResponse != null)
                    {
                        if (reviewerResponse.assessmentReviewActivity.Count > 0)
                        {
                            if (reviewerResponse.assessmentReviewActivity[0].reviewerReponses != null)
                            {
                                responseModel.assessmentReviewActivity[0].reviewerReponses = reviewerResponse.assessmentReviewActivity[0].reviewerReponses;
                            }

                        }
                    }
                    ReviewerAssessmentLoadResponseModel UnitModule = GetUnitModule(activityId, _logger).Result;
                    if (UnitModule != null)
                    {
                        if (UnitModule.assessmentReviewActivity.Count > 0)
                        {
                            if (UnitModule.assessmentReviewActivity[0].unit_Module != null)
                            {
                                responseModel.assessmentReviewActivity[0].unit_Module = UnitModule.assessmentReviewActivity[0].unit_Module;
                                if (responseModel.assessmentReviewActivity[0].unit_Module != null && responseModel.assessmentReviewActivity[0].unit_Module.AssessmentsLines.Any())
                                {
                                    responseModel.assessmentReviewActivity[0].unit_Module.AssessmentsLines = AssessmentHelperExtensions.SortAssessments(responseModel.assessmentReviewActivity[0].unit_Module.AssessmentsLines);
                                }
                            }

                        }
                    }

                }

                if (responseModel.assessmentReviewActivity[0].unit_Module != null || responseModel.assessmentReviewActivity[0].Reviewer_Module != null
                    || responseModel.assessmentReviewActivity[0].reviewerReponses != null || responseModel.assessmentReviewActivity[0].UserResponses != null)
                {
                    responseModel.assessmentReviewActivity[0].aacn_assessment_review_activityid = matchedData.aacn_assessment_review_activityid;
                    responseModel.assessmentReviewActivity[0].aacn_assessment_review_activity_number = matchedData.aacn_assessment_review_activity_number;
                    responseModel.assessmentReviewActivity[0].aacn_request_status = matchedData.aacn_request_status;
                    //Unit
                    if (matchedData.aacn_Unit != null)
                    {
                        responseModel.assessmentReviewActivity[0].aacn_Unit = new UnitData();
                        responseModel.assessmentReviewActivity[0].aacn_Unit.name = matchedData.aacn_Unit.name == null ? string.Empty : matchedData.aacn_Unit.name;
                        responseModel.assessmentReviewActivity[0].aacn_Unit.accountid = matchedData.aacn_Unit.accountid == null ? string.Empty : matchedData.aacn_Unit.accountid;
                        responseModel.assessmentReviewActivity[0].aacn_Unit.accountnumber = matchedData.aacn_Unit.accountnumber == null ? string.Empty : matchedData.aacn_Unit.accountnumber;
                    }

                    //Contact - 
                    if (matchedData.aacn_reviewer != null)
                    {
                        responseModel.assessmentReviewActivity[0].aacn_reviewer = new ReviewerData();
                        responseModel.assessmentReviewActivity[0].aacn_reviewer.contactid = matchedData.aacn_reviewer.contactid == null ? string.Empty : matchedData.aacn_reviewer.contactid;
                        responseModel.assessmentReviewActivity[0].aacn_reviewer.aacn_contact_number = matchedData.aacn_reviewer.aacn_contact_number == null ? string.Empty : matchedData.aacn_reviewer.aacn_contact_number;
                        responseModel.assessmentReviewActivity[0].aacn_reviewer.firstname = matchedData.aacn_reviewer.firstname == null ? string.Empty : matchedData.aacn_reviewer.firstname;
                    }


                }
            }
            catch (Exception ex)
            {

            }
            return responseModel;
        }
        private async Task<List<LinesData>> GetResponseLinesData<T>(string responselineIDs, ILogger<T> _logger)
        {
            List<LinesData> _responseLines = new List<LinesData>();
            try
            {
                string Query = string.Format(Utility.getResponseLinesByResponseLineid, responselineIDs);
                HttpResponseMessage httpResponse = await GetRecordsAsync(Query, _logger);
                if (httpResponse.IsSuccessStatusCode)
                {
                    UpdateResponseModelResponse updateResponseModelResponse = JsonConvert.DeserializeObject<UpdateResponseModelResponse>(httpResponse.Content.ReadAsStringAsync().Result);
                    if (updateResponseModelResponse._lineData != null)
                    {
                        return updateResponseModelResponse._lineData;
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return _responseLines;

        }
        private async Task<List<LinesData>> GetActiveResponseLinesDataId<T>(string responselineIDs, ILogger<T> _logger)
        {
            List<LinesData> _responseLines = new List<LinesData>();
            try
            {
                string Query = string.Format(Utility.getResponseActiveResponseLines, responselineIDs);
                HttpResponseMessage httpResponse = await GetRecordsAsync(Query, _logger);
                if (httpResponse.IsSuccessStatusCode)
                {
                    UpdateResponseModelResponse updateResponseModelResponse = JsonConvert.DeserializeObject<UpdateResponseModelResponse>(httpResponse.Content.ReadAsStringAsync().Result);
                    if (updateResponseModelResponse._lineData != null)
                    {
                        return updateResponseModelResponse._lineData;
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return _responseLines;

        }
        private async Task<String> CreatePOCData(string postionId, string pocKey, string firstName, string LastName, string pocNumber, ILogger<beaconController> _logger)
        {
            string contactId = null;
            try
            {
                if (pocKey != null)
                {
                    string unitIds = $"'{pocKey}'";
                    ContactModelRoot contactModelRoot = await GetReviewerDataById(unitIds, _logger);
                    if (contactModelRoot.value != null && contactModelRoot.value.Count > 0)
                    {
                        _logger.LogInformation($"POC Found {contactModelRoot.value[0].contactid.ToString()}");

                        return contactModelRoot.value[0].contactid.ToString();
                    }
                }
                PostContactModel postContactModel = new PostContactModel();
                postContactModel.FirstName = firstName;
                postContactModel.LastName = LastName;
                if (pocKey != null)
                {
                    postContactModel.RecordId = pocKey;
                }
                if (!string.IsNullOrEmpty(postionId))
                {
                    postContactModel.postionId = $"/positions({postionId})";
                }
                postContactModel.record_Number = pocNumber;

                HttpResponseMessage contactResponse = await CreateRecordWithRetry("contacts", JsonConvert.SerializeObject(postContactModel), _logger);
                if (contactResponse.IsSuccessStatusCode)
                {
                    return contactResponse.Headers.GetValues("OData-EntityId").FirstOrDefault().Split('[', '(', ')', ']')[1].ToString();
                }

            }
            catch (Exception ex)
            {

            }

            return contactId;
        }

        private async Task<Boolean> updateResponseLinestoInative(List<ResponseLineDto> _RequestedInactiveLines, ILogger<beaconController> _logger)
        {

            if (_RequestedInactiveLines.Count > 0)
            {
                List<LinesData> existingLines;
                List<HttpResponseMessage> _updateResponse = new List<HttpResponseMessage>();
                string responeLinesId = string.Join(",", _RequestedInactiveLines.Select(p => $"\"{p.Key}\""));
                if (responeLinesId != null)
                {
                    existingLines = await GetActiveResponseLinesDataId(responeLinesId, _logger);
                    if (existingLines.Any() && existingLines.Count > 0)
                    {
                        var existingLinesId = new HashSet<string>(existingLines.Select(er => er.aacn_response_lineid));
                        existingLinesId.Remove(null);
                        var filteredRequests = _RequestedInactiveLines.Where(rr => existingLinesId.Contains(rr.Key.ToString())).ToList();
                        if (filteredRequests.Any() && filteredRequests.Count > 0)
                        {
                            List<BeaconResponseLines> _UpdatedLines = new List<BeaconResponseLines>();
                            foreach (ResponseLineDto updateLines in filteredRequests)
                            {
                                BeaconResponseLines updateLineItem = new BeaconResponseLines();
                                updateLineItem.statecode = (int)StatusCode.Inactive;
                                HttpResponseMessage updateResponse = await UpdateRequestAsync($"aacn_response_lines({updateLines.Key})", JsonConvert.SerializeObject(updateLineItem), _logger);
                                if (!updateResponse.IsSuccessStatusCode)
                                {
                                    updateResponse.StatusCode = HttpStatusCode.BadRequest;
                                }
                                _updateResponse.Add(updateResponse);
                            }
                        }

                    }
                }
                if (_updateResponse.Any() && _updateResponse.Count > 0)
                {
                    return _updateResponse.Exists(r => r.StatusCode != HttpStatusCode.BadRequest);
                }
            }
            return true;
        }

        private async Task<Boolean> updateResponseLinestoInativeApproach1(List<ResponseLineDto> _RequestedInactiveLines, List<QuestionData> _questionMaster, List<AnswerData> _anserMaster, string ResponseKey, ILogger<beaconController> _logger)
        {
            const int chunkSize = 30;
            bool hasErrorOccurred = false;

            // Split the list into chunks of the specified size
            var chunks = _RequestedInactiveLines
                .Select((line, index) => new { line, index })
                .GroupBy(x => x.index / chunkSize)
                .Select(g => g.Select(x => x.line).ToList())
                .ToList();

            foreach (var chunk in chunks)
            {
                if (chunk.Count > 0)
                {
                    List<LinesData> existingLines;
                    List<HttpResponseMessage> _updateResponse = new List<HttpResponseMessage>();
                    string responeLinesId = string.Join(",", chunk.Select(p => $"\"{p.Key}\""));
                    List<ResponseLineDto> CreateRequests = new List<ResponseLineDto>();

                    if (!string.IsNullOrEmpty(responeLinesId))
                    {
                        existingLines = await GetActiveResponseLinesDataId(responeLinesId, _logger);
                        if (!existingLines.Any())
                            CreateRequests = chunk.Where(rr => rr.Key != null).ToList();

                        if (existingLines.Any() || CreateRequests.Any())
                        {
                            if (existingLines.Any())
                            {
                                var existingLinesId = new HashSet<string>(existingLines.Select(er => er.aacn_response_lineid));
                                existingLinesId.Remove(null);
                                CreateRequests = chunk.Where(rr => !existingLinesId.Contains(rr.Key.ToString())).ToList();
                                var filteredRequests = chunk.Where(rr => existingLinesId.Contains(rr.Key.ToString())).ToList();
                                if (filteredRequests.Any())
                                {
                                    List<BeaconResponseLines> _UpdatedLines = new List<BeaconResponseLines>();
                                    foreach (ResponseLineDto updateLines in filteredRequests)
                                    {
                                        BeaconResponseLines updateLineItem = new BeaconResponseLines
                                        {
                                            statecode = (int)StatusCode.Inactive
                                        };

                                        HttpResponseMessage updateResponse = await UpdateRequestAsync(
                                            $"aacn_response_lines({updateLines.Key})",
                                            JsonConvert.SerializeObject(updateLineItem),
                                            _logger
                                        );

                                        if (!updateResponse.IsSuccessStatusCode)
                                        {
                                            updateResponse.StatusCode = HttpStatusCode.BadRequest;
                                            hasErrorOccurred = true; // Track if any error occurred
                                        }

                                        _updateResponse.Add(updateResponse);
                                    }
                                }
                            }

                            //Create the Lines - Is Deleted-1
                            if (CreateRequests.Any())
                            {
                                List<BeaconResponseLines> _CreateLines = new List<BeaconResponseLines>();
                                foreach (ResponseLineDto createLines in CreateRequests)
                                {
                                    #region CreateLines --
                                    BeaconResponseLines responseLineData = new BeaconResponseLines();
                                    responseLineData.Response_Id = ResponseKey;
                                    responseLineData.ResponseLineKey = createLines.Key != Guid.Empty ? createLines.Key.ToString() : null; // Response Line Key
                                    responseLineData.assessment_Line_Id = createLines.QuestionKey != Guid.Empty ? createLines.QuestionKey.ToString() : null;
                                    responseLineData.assessment_Line_Option_Id = createLines.SubquestionKey.ToString(); // SubQuestion Key
                                    responseLineData.question_Id = createLines.QuestionKey.ToString();
                                    responseLineData.answer_Id = createLines.SubquestionKey.ToString();
                                    if (createLines.AnswerText != string.Empty)
                                    {
                                        responseLineData.response_Text = createLines.AnswerText; // Answer Text
                                    }
                                    if (_anserMaster.Any() && createLines.SubquestionKey != null)
                                    {
                                        string AnswerText = _anserMaster
                                           .Where(q => q.AnswerId == createLines.SubquestionKey.ToString())
                                           .Select(q => q.Answer)
                                           .FirstOrDefault() ?? string.Empty;
                                        _logger.LogInformation($"Answer-Text ~ {AnswerText}");
                                        responseLineData.AnswerText = AnswerText;
                                    }

                                    if (_questionMaster.Any())
                                    {
                                        string QuestionText = _questionMaster
                                            .Where(q => q.questionid == createLines.QuestionKey.ToString())
                                            .Select(q => q.question)
                                            .FirstOrDefault() ?? string.Empty;
                                        _logger.LogInformation($"Question-Text ~ {QuestionText}");
                                        responseLineData.question_Text = QuestionText;
                                    }

                                    //_CreateLines.Add(responseLineData);
                                    HttpResponseMessage CreateResponse = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLineData), _logger);
                                    if (!CreateResponse.IsSuccessStatusCode)
                                    {
                                        CreateResponse.StatusCode = HttpStatusCode.BadRequest;
                                        hasErrorOccurred = true; // Track if any error occurred
                                    }
                                    #endregion CreateLines --
                                    else
                                    {
                                        #region UpdateLines --
                                        BeaconResponseLines updateLineItem = new BeaconResponseLines
                                        {
                                            statecode = (int)StatusCode.Inactive
                                        };

                                        HttpResponseMessage updateResponse = await UpdateRequestAsync(
                                            $"aacn_response_lines({createLines.Key})",
                                            JsonConvert.SerializeObject(updateLineItem),
                                            _logger
                                        );

                                        if (!updateResponse.IsSuccessStatusCode)
                                        {
                                            updateResponse.StatusCode = HttpStatusCode.BadRequest;
                                            hasErrorOccurred = true; // Track if any error occurred
                                        }
                                        _updateResponse.Add(updateResponse);
                                        #endregion UpdateLines -- 
                                    }
                                    _updateResponse.Add(CreateResponse);
                                }

                            }
                        }
                    }
                }
            }

            return !hasErrorOccurred;
        }
        private async Task<List<HttpResponseMessage>> UpdateandCreateResponseLinesNewApproach(string responseId, List<BeaconResponseLines> _requestedLines, ILogger<beaconController> _logger)
        {
            int CreateCounter = 0;
            int UpdateCounter = 0;
            List<HttpResponseMessage> _httpResponseList = new List<HttpResponseMessage>();
            try
            {
                ResponseDataRoot responseData = GetResponseDataRoot(responseId, _logger);
                if (responseData != null)
                {
                    responses ResponseRoot = responseData.value.FirstOrDefault();
                    List<ResponseLinesData> _existingResponseLines = ResponseRoot.responseLinesData;
                    if (_requestedLines != null)
                    {
                        if (_requestedLines != null && _requestedLines.Count > 0)
                        {
                            List<BeaconResponseLines> CreateResponseLinesList = _requestedLines.Where(rr => rr.ResponseLineKey == null).ToList();
                            var requestedId = new HashSet<string>(_requestedLines.Select(er => er.ResponseLineKey));
                            var filteredRequests = _existingResponseLines.Where(rr => !requestedId.Contains(rr.aacn_response_lineid)).ToList();
                            List<HttpResponseMessage> _InactiveUpdateResponse = new List<HttpResponseMessage>();
                            //Inactive the Lines
                            foreach (var updateResponseLines in filteredRequests)
                            {
                                BeaconResponseLines responseLines = new BeaconResponseLines();
                                responseLines.ResponseLineKey = updateResponseLines.aacn_response_lineid;
                                responseLines.statecode = (int)StatusCode.Inactive;
                                HttpResponseMessage udpateResponse = await UpsertRecordAsync($"aacn_response_lines({updateResponseLines.aacn_response_lineid})", JsonConvert.SerializeObject(responseLines), "update", _logger);
                                if (!udpateResponse.IsSuccessStatusCode)
                                {
                                    udpateResponse.StatusCode = HttpStatusCode.BadRequest;

                                }
                                _InactiveUpdateResponse.Add(udpateResponse);
                            }

                            if (_InactiveUpdateResponse.Any())
                            {
                                UpdateCounter = UpdateCounter + _InactiveUpdateResponse.Count;
                                var failedRequests = _InactiveUpdateResponse.Where(r => r.StatusCode == HttpStatusCode.BadRequest).ToList();
                                if (failedRequests != null && failedRequests.Count > 0)
                                {

                                }

                            }
                            List<HttpResponseMessage> _CreateResponse = new List<HttpResponseMessage>();

                            //Create New Lines-- 
                            foreach (BeaconResponseLines createRespnseLines in CreateResponseLinesList)
                            {
                                BeaconResponseLines responseLines = new BeaconResponseLines();
                                responseLines.Response_Id = responseId;
                                responseLines.assessment_Line_Id = createRespnseLines.assessment_Line_Id;
                                responseLines.assessment_Line_Option_Id = createRespnseLines.assessment_Line_Option_Id;
                                responseLines.question_Id = createRespnseLines.question_Id;
                                responseLines.answer_Id = createRespnseLines.answer_Id;
                                responseLines.question_Text = createRespnseLines.question_Text;
                                responseLines.AnswerText = createRespnseLines.AnswerText;
                                responseLines.response_Text = createRespnseLines.response_Text;
                                HttpResponseMessage createResponse = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLines), _logger);
                                if (!createResponse.IsSuccessStatusCode)
                                {
                                    createResponse.StatusCode = HttpStatusCode.BadRequest;

                                }
                                _CreateResponse.Add(createResponse);
                            }

                            if (_CreateResponse.Any())
                            {
                                CreateCounter = CreateCounter + _CreateResponse.Count;
                                var failedRequests = _CreateResponse.Where(r => r.StatusCode == HttpStatusCode.BadRequest).ToList();
                                if (failedRequests != null && failedRequests.Count > 0)
                                {

                                }

                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _httpResponseList.Add(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent(ex.Message)
                });
            }

            return _httpResponseList;
        }
        private async Task<Boolean> IactiveLineBulkUpdate(List<ResponseLineDto> _InactiveLines, ILogger<beaconController> _logger)
        {
            bool status = true;
            List<HttpResponseMessage> _httpResponseList = new List<HttpResponseMessage>();
            try
            {
                List<BeaconResponseLines> _UpdateList = new List<BeaconResponseLines>();
                if (_InactiveLines.Count > 0)
                {
                    foreach (ResponseLineDto updateLines in _InactiveLines)
                    {
                        BeaconResponseLines responseLines = new BeaconResponseLines();
                        responseLines.ResponseLineKey = updateLines.Key.ToString();
                        responseLines.statecode = (int)StatusCode.Inactive;
                        _UpdateList.Add(responseLines);

                    }
                    if (_UpdateList.Any())
                    {
                        HttpResponseMessage BulkUpdateResponse = await UpdateBatchRecords("aacn_response_lines", _UpdateList, _logger);
                        if (!BulkUpdateResponse.IsSuccessStatusCode)
                        {
                            status = false;
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                status = false;
            }

            return status;
        }

        private async Task<List<HttpResponseMessage>> UpdateandCreateSiteResponseLines(string responseId, List<BeaconResponseLines> _responseList, ResponseDataRoot responseData, ILogger<beaconController> _logger)
        {
            List<HttpResponseMessage> _httpResponseList = new List<HttpResponseMessage>();
            try
            {
                if (responseData != null && responseData.value.Count > 0)
                {
                    responses ResponseRoot = responseData.value.FirstOrDefault();
                    List<ResponseLinesData> _existingResponseLines = ResponseRoot.responseLinesData;
                    if (_existingResponseLines != null && _existingResponseLines.Count > 0)
                    {
                        List<BeaconResponseLines> _CreateRequests = new List<BeaconResponseLines>();
                        List<BeaconResponseLines> _UpsertRequests = new List<BeaconResponseLines>();

                        foreach (var responseLine in _responseList)
                        {
                            try
                            {
                                // Check if there is a matching response line in the existing list
                                var existingLine = _existingResponseLines
                                    .FirstOrDefault(e => e.aacn_response_lineid == responseLine.ResponseLineKey);
                                //Create --
                                if (existingLine == null)
                                {
                                    responseLine.Response_Id = responseId;
                                    _CreateRequests.Add(responseLine);
                                    //httpresponseitem = await CreateRecordWithRetry("aacn_response_lines", JsonConvert.SerializeObject(responseLine), _logger);
                                    //LoggerHelper.LogObjectValue(httpresponseitem, httpresponseitem.Content.ToString());
                                }
                                else
                                {   //Update --
                                    if (existingLine.LineStatus != (int)StatusCode.Inactive)
                                    {
                                        responseLine.Response_Id = responseId;
                                        _UpsertRequests.Add(responseLine);
                                    }

                                }

                            }
                            catch (Exception ex)
                            {

                            }
                        }

                        if (_CreateRequests.Any())
                        {
                            HttpResponseMessage BulkCreateResponse = await CreateBatchRecords("aacn_response_lines", _CreateRequests, _logger);
                            if (!BulkCreateResponse.IsSuccessStatusCode)
                            {
                                BulkCreateResponse.StatusCode = HttpStatusCode.BadRequest;
                            }
                            _httpResponseList.Add(BulkCreateResponse);
                        }

                        if (_UpsertRequests.Any())
                        {
                            HttpResponseMessage BulkCreateResponse = await UpdateBatchRecords("aacn_response_lines", _UpsertRequests, _logger);

                            if (!BulkCreateResponse.IsSuccessStatusCode)
                            {
                                BulkCreateResponse.StatusCode = HttpStatusCode.BadRequest;
                            }
                            _httpResponseList.Add(BulkCreateResponse);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _httpResponseList.Add(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent(ex.Message)
                });
            }

            return _httpResponseList;
        }

        private async Task<string> getOrganizationByUnit(string unitKey)
        {
            string organizationquery = string.Format(GetOrganizationbyunitid, unitKey);
            HttpResponseMessage organizationresponse = GetRecords(organizationquery);
            if (organizationresponse.IsSuccessStatusCode)
            {
                Getorganization organizationroot = JsonConvert.DeserializeObject<Getorganization>(await organizationresponse.Content.ReadAsStringAsync());
                if (organizationroot != null)
                {
                    if (organizationroot.value.Count > 0)
                    {
                        OrganizationValuesByunitId organizationmodel = organizationroot.value.FirstOrDefault();
                        if (organizationmodel._parentaccountid_value != null)
                        {
                            return organizationmodel._parentaccountid_value;
                        }
                    }
                }
            }
            return null;
        }

        private void createApiLog(RemoveResponse responsemodel, List<ReturnResponse> _returnResponse, ILogger<beaconController> _logger)
        {
            ApiAuditLog apiAuditModel = new ApiAuditLog();
            try
            {
                string odataQuery = "aacn_api_integration_audit_logs";
                apiAuditModel.aacn_api_endpoint = "RemoveResponse";
                apiAuditModel.aacn_request_body = JsonConvert.SerializeObject(responsemodel);
                apiAuditModel.aacn_response_body = JsonConvert.SerializeObject(_returnResponse);
                HttpResponseMessage auditResponse = CreateRecord(odataQuery, JsonConvert.SerializeObject(apiAuditModel));
                if (auditResponse.IsSuccessStatusCode)
                {
                    string _recordUrl = auditResponse.Headers.GetValues("OData-EntityId").FirstOrDefault();
                    string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                    _logger.LogInformation("D365 ApiAuditLog Created with Id" + splitRetrievedData[1].ToString());
                }
                else
                {
                    string failedreason = auditResponse.Content.ReadAsStringAsync().Result;
                    _logger.LogInformation(failedreason);
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Catch exception =" + ex.Message.ToString());
            }
        }

        private async Task<List<ReturnResponse>> deleteResponseLines(List<SurveyStatus> filtereddeleteresponse, List<ReturnResponse> _returnResponse, ILogger<beaconController> _logger)
        {
            foreach (var item in filtereddeleteresponse)
            {
                ReturnResponse returnresponse = new ReturnResponse();
                returnresponse.responsekey = item.Key;
                returnresponse.responsestatus = "Not Exists";
                if (item.Key != null)
                {
                    ResponseLines responseLinesModel = new ResponseLines();
                    try
                    {
                        string responseLineQuery = string.Format(GetResponseLines, item.Key);
                        HttpResponseMessage responseLinesHttpResponse = GetRecords(responseLineQuery);
                        if (responseLinesHttpResponse.IsSuccessStatusCode)
                        {

                            responseLinesModel = JsonConvert.DeserializeObject<ResponseLines>(responseLinesHttpResponse.Content.ReadAsStringAsync().Result);
                            if (responseLinesModel != null && responseLinesModel.value.Count > 0)
                            {
                                List<ResponseLinesdataValues> responseLineDataValuesList = responseLinesModel.value;
                                List<string> deleteresponselineguids = responseLineDataValuesList.Select(x => x.aacn_response_lineid).ToList();
                                if (deleteresponselineguids.Count > 0)
                                {
                                    //Delete ResponseLines
                                    try
                                    {
                                        HttpResponseMessage deleteResponsesLines = await BulkDeleterecordsAsync("aacn_response_lines", deleteresponselineguids, _logger);

                                        if (!deleteResponsesLines.IsSuccessStatusCode)
                                        {
                                            returnresponse.responsestatus = "failed";
                                            returnresponse.failedreason = deleteResponsesLines.Content.ReadAsStringAsync().Result;
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        returnresponse.responsestatus = "failed";
                                        returnresponse.failedreason = ex.Message.ToString();
                                    }
                                }
                            }
                        }

                        var reviewerResponseLineQuery = string.Format(GetAsrReviewerResponseandLines, item.Key);
                        HttpResponseMessage reviewerLineResponse = GetRecords(reviewerResponseLineQuery);
                        if (reviewerLineResponse.IsSuccessStatusCode)
                        {
                            AsrReviewerResponse asrReviewerResponseModel = JsonConvert.DeserializeObject<AsrReviewerResponse>(reviewerLineResponse.Content.ReadAsStringAsync().Result);
                            if (asrReviewerResponseModel != null && asrReviewerResponseModel.value.Count > 0)
                            {
                                List<AsrReviewerResponseValues> assessmentReviewActivityList = asrReviewerResponseModel.value;
                                foreach (var asr in assessmentReviewActivityList)
                                {
                                    AacnReviewersResponse reviewerResponse = asr.aacn_reviewers_response;
                                    if (reviewerResponse != null)
                                    {
                                        if (reviewerResponse.aacn_response_line_response_aacn_response != null)
                                        {
                                            if (reviewerResponse.aacn_response_line_response_aacn_response.Count > 0)
                                            {
                                                List<AacnResponseLineResponseAacnResponseValues> reviewerResponseLinesList = reviewerResponse.aacn_response_line_response_aacn_response;
                                                List<string> reviewerResponseLine = reviewerResponseLinesList.Select(x => x.aacn_response_lineid).ToList();
                                                if (reviewerResponseLine.Count > 0)
                                                {
                                                    try
                                                    {
                                                        HttpResponseMessage deleteResponsesLinesHttpresponse = await BulkDeleterecordsAsync("aacn_response_lines", reviewerResponseLine, _logger);
                                                        if (!deleteResponsesLinesHttpresponse.IsSuccessStatusCode)
                                                        {
                                                            returnresponse.responsestatus = "failed";
                                                        }
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        returnresponse.responsestatus = "failed";
                                                        returnresponse.failedreason = ex.Message.ToString();
                                                    }
                                                }
                                            }
                                        }

                                        if (reviewerResponse.aacn_responseid != null)
                                        {
                                            try
                                            {
                                                HttpResponseMessage deleteReviewerResponsesHttpResponse = await DeleteRecordsAsync($"aacn_responses({reviewerResponse.aacn_responseid})", _logger);
                                                if (!deleteReviewerResponsesHttpResponse.IsSuccessStatusCode)
                                                {
                                                    returnresponse.responsestatus = "failed";
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                returnresponse.responsestatus = "failed";
                                                returnresponse.failedreason = ex.Message.ToString();
                                            }

                                        }
                                    }
                                    if (asr.aacn_assessment_review_activityid != null)
                                    {
                                        try
                                        {
                                            HttpResponseMessage deleteAraResponsesHttpResponse = await DeleteRecordsAsync($"aacn_assessment_review_activities({asr.aacn_assessment_review_activityid})", _logger);
                                            if (!deleteAraResponsesHttpResponse.IsSuccessStatusCode)
                                            {
                                                returnresponse.responsestatus = "failed";
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            returnresponse.responsestatus = "failed";
                                            returnresponse.failedreason = ex.Message.ToString();
                                            _logger.LogInformation(ex.Message.ToString());
                                        }
                                    }
                                }
                            }
                        }
                        if (responseLinesModel != null && responseLinesModel.value.Count > 0)
                        {
                            try
                            {
                                HttpResponseMessage deleteResponsesHttpResponse = await DeleteRecordsAsync($"aacn_responses({item.Key})", _logger);
                                if (deleteResponsesHttpResponse.IsSuccessStatusCode)
                                {
                                    returnresponse.responsestatus = "success";
                                }
                                else
                                {
                                    returnresponse.responsestatus = "failed";
                                }
                            }
                            catch (Exception ex)
                            {
                                returnresponse.responsestatus = "failed";
                                returnresponse.failedreason = ex.Message.ToString();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        returnresponse.responsestatus = "failed";
                        returnresponse.failedreason = ex.Message.ToString();
                        _logger.LogInformation("Exception Error - " + ex.Message.ToString());
                    }

                    _returnResponse.Add(returnresponse);
                }
            }
            return _returnResponse;
        }

        private string getSqlQuery(ManagerDashboardRequest managerRequestModel)
        {
            var query = string.Empty;
            var contactFilter = string.Empty;
            int skip = 0;
            if (managerRequestModel.PageNumber > 1)
            {
                skip = (managerRequestModel.PageNumber - 1) * managerRequestModel.PageSize;
            }
            if (!string.IsNullOrEmpty(managerRequestModel.ContactFilter))
            {
                if (managerRequestModel.ContactFilter.Contains("fullname") && managerRequestModel.ContactFilter.Contains("aacn_contact_number"))
                    contactFilter = "AND e.fullname IS NOT NULL AND e.aacn_contact_number IS NOT NULL";
                if (managerRequestModel.ContactFilter.Contains("fullname"))
                    contactFilter = "AND e.fullname IS NOT NULL";
                if (managerRequestModel.ContactFilter.Contains("aacn_contact_number"))
                    contactFilter = "AND e.aacn_contact_number IS NOT NULL";
            }

            query = $@"
        SELECT 
            aacn_accepted_date,aacn_assessment_review_activity.aacn_year AS Year, aacn_assessment, aacn_assessment_review_activity_number, aacn_application_id,
            aacn_assessment_review_activityid, aacn_assigned_date, aacn_beacon_cycle_year, 
            aacn_completed_date, aacn_inprogress_date, aacn_module_completion_status, 
             aacn_rejected_date, aacn_rejected_reason,
CASE aacn_rejected_reason
        WHEN ********* THEN 'Conflict of Interest'
        WHEN ********* THEN 'Busy'
        WHEN ********* THEN 'Vacation'
        WHEN ********* THEN 'Other'
        ELSE 'Unknown'
    END AS aacn_rejected_reason_label,
            aacn_request_status, aacn_reviewer, aacn_reviewers_response, aacn_unit, 
            aacn_user_response, a.aacn_assessment_description, a.aacn_assessment_number, 
            a.aacn_assessment_title, a.aacn_assessmentid, 
            b.aacn_assessment_description AS reviewer_description, b.aacn_assessment_number AS reviewer_number, b.aacn_assessment_title AS reviewer_title, 
            b.aacn_assessmentid AS reviewer_module_assessment_id,  
             c.accountid, c.accountnumber, 
            c.address1_city, c.address1_stateorprovince, c.name, d.accountid AS parent_accountid, 
            d.accountnumber AS parent_accountnumber, d.name AS parent_name, 
            e.aacn_contact_number, e.contactid, e.firstname, e.fullname, e.lastname
        FROM aacn_assessment_review_activity
        JOIN aacn_assessment a ON a.aacn_assessmentid = aacn_assessment_review_activity.aacn_unit_module {managerRequestModel.UnitModuleFilter}
        JOIN aacn_assessment b ON b.aacn_assessmentid = aacn_assessment_review_activity.aacn_reviewer_module {managerRequestModel.ReviewerModuleFilter}
        JOIN account c ON c.accountid = aacn_assessment_review_activity.aacn_unit {managerRequestModel.UnitFilter}
        JOIN account d ON d.accountid = c.parentaccountid {managerRequestModel.OrganizationFilter}
        LEFT JOIN contact e ON e.contactid = aacn_assessment_review_activity.aacn_reviewer {managerRequestModel.ContactFilter}
        WHERE (aacn_request_status IN ({managerRequestModel.StatusValue}) {managerRequestModel.AraFilter} {contactFilter})
         ORDER BY {managerRequestModel.OrderFilter}
        OFFSET {skip} ROWS FETCH NEXT {managerRequestModel.PageSize} ROWS ONLY;";

            return query;
        }

        private async Task<string> GetYearId(string year)
        {
            try
            {
                YearModel yearModel = new YearModel();
                if (!string.IsNullOrEmpty(year))
                {
                    var query = string.Format(Utility.GetYear, year);
                    HttpResponseMessage yearResponse = GetRecords(query);
                    if (yearResponse.IsSuccessStatusCode)
                    {
                        yearModel = JsonConvert.DeserializeObject<YearModel>(await yearResponse.Content.ReadAsStringAsync());
                        if (yearModel.value.Count > 0)
                        {
                            return yearModel.value[0].aacn_yearid;
                        }
                    }
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }

        private async Task<string> GetAssessmentKey(string year)
        {
            var assessmentId = string.Empty;
            AssessmentMainModel assessmentModel = new AssessmentMainModel();
            var query = string.Format(Utility.GetAssessmentsByCurrentYear, year);
            HttpResponseMessage assessmnetResponse = GetRecords(query);

            if (assessmnetResponse.IsSuccessStatusCode)
            {
                assessmentModel = JsonConvert.DeserializeObject<AssessmentMainModel>(await assessmnetResponse.Content.ReadAsStringAsync());
                if(assessmentModel.Value.Count > 0)
                {

                    var demographicsLower = Utility.constants.AssessmentConstants.Demographics.ToLower();

                    assessmentId = assessmentModel.Value
                        .FirstOrDefault(x => x.AacnAssessmentTitle?.ToLower().Contains(demographicsLower) == true)?
                        .AacnAssessmentid ?? string.Empty;

                }
            }
            return assessmentId;
        }
        #endregion Private-Methods

    }
}

﻿namespace AACN.API.Service.Beacon_Service
{
    using AACN.API.Controllers;
    using AACN.API.Model;
    using AACN.API.Model.Manager;
    using AACN.API.Model.OrganisationModel;
    using AACN.API.Model.ReviewerAssessmentLoadModel;
    using AACN.API.Model.SiteCoreSyncModel;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading.Tasks;

    public interface IBeaconService
    {
        #region SiteCore_TO_D365 API's
        BeaconResponse UserVerification(string jwtToken, ILogger<beaconController> _logger);
        Task<List<APIResponse>> SyncUnits(PostUnitCommand postunitmodel, ILogger<beaconController> _logger);
        Task<List<ReturnResponse>> RemoveResponses(RemoveResponse postunitmodel, ILogger<beaconController> _logger);
        Task<List<APIResponse>> SyncOrganizationData(PostOrganizationCommand organizationModel, ILogger<beaconController> _logger);
        Task<List<APIResponse>> SyncReviewerData(SyncReviwerCommand syncReviwerCommand, ILogger<beaconController> _logger);
        Task<SiteCoreResponseModelbatchResponses> SyncSiteCoreResponsesBatchRequests(SyncResponsesCommand siteCoreRequestModel, ILogger<beaconController> _logger);
        Task<List<SiteCoreResponseModel>> SyncSiteCoreResponses(SyncResponsesCommand siteCoreRequestModel, ILogger<beaconController> _logger);

        #endregion SiteCore_TO_D365 API's

        #region D365_Portal_Internal_API's
        Task<ReviewerAssessmentLoadResponseModel> GetUnitModule(string reviewActivityId, ILogger<beaconController> _logger);
        Task<AssessmentReviewActivtyRootModel> GetAssessmentActivityByReviwerId(string reviewerId, string yearId, ILogger<beaconController> _logger);
        Task<ReviewerAssessmentLoadResponseModel> GetReviewerAndUserResponses(string reviewNumber, ILogger<beaconController> _logger);
        Task<AssessmentReviewActivtyRootModel> GetManagerDashboardByStatusWithPagination(ManagerDashboardRequest managerRequestModel, ILogger<beaconController> _logger);
        Task<AssessmentReviewActivtyRootModel> GetManagerDashboardByStatus(string statusValue, ILogger<beaconController> _logger);
        Task<AssessmentReviewActivtyRootModel> GetManagerDashboard(string yearId,ILogger<beaconController> _logger);
        Task<GetAuditRoot> GetAuditReport(ILogger<beaconController> _logger);
        Task<StatusCodeResult> RandomizeData(ILogger<beaconController> _logger);
        Task<ReviewerAssessmentLoadResponseModel> GetReviewerModule(string reviewActivityId, ILogger<beaconController> _logger);
        Task<ReviewerAssessmentLoadResponseModel> GetReviewerModuleData(string reviewActivityId, ILogger<beaconController> _logger);
        Task<UnitVerificationResponse> VerfiyUnitResponses(string reviewerKey, ILogger<beaconController> _logger);
        Task<ManagerDataModel> LoadManagerData(string reviewActivityId, string requestPage, string previousModule,string yearId, ILogger<beaconController> _logger);
        Task<List<APIResponse>> UpdateManagerResponses(UpdateResponseModel UpdateResponseModel, ILogger<beaconController> _logger);
        Task<List<APIResponse>> UpdateManagerResponsesApproach2(UpdateResponseModel UpdateResponseModel, ILogger<beaconController> _logger);
        Task<ResponseSubmitResponse> UpdateManagerResponsesApproach3(UpdateResponseModel UpdateResponseModel, ILogger<beaconController> _logger);
        #endregion D365_Portal_Internal_API's

        #region Common API's D365_Portal_Internal_API's
        Task<BeaconResponseByAction> GetResponseCounterByMemberId(string reviewerId, ILogger<beaconController> _logger);
        Task<APIResponse> Post_AssessmentReview(PostAssessmentReviewActivity postAssessmentReviewActivity, ILogger<beaconController> _logger);
        Task<APIResponse> Update_Assessment_Review(PostAssessmentReviewActivity postAssessmentReviewActivity, ILogger<beaconController> _logger);
        Task<List<APIResponse>> Update_Assessment_ReviewBatch(PostAssessmentReviewActivityCommand postAssessmentReviewActivityCommand, ILogger<beaconController> _logger);
        Task<ContactListModelByRole> GetContactListByRole(string requestType, ILogger<beaconController> _logger);
        Task<BeaconAPIResponse> CreateBeaconResponse(BeaconRespnseData beaconRespnseData, string RequestFor, ILogger<beaconController> _logger);
        #endregion Common API's D365_Portal_Internal_API's

    }
}

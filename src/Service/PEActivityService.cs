﻿namespace AACN.API.Service
{
    using AACN.API.Model;
    using Newtonsoft.Json.Linq;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;
    using AACN.Services;
    using Microsoft.Extensions.Logging;

    public class PEActivityService : BaseService
    {
        public CEActivityResponseModel GetEvaluationData(string customerKey, string eventKey)
        {
            DynamicsJsonValue dynamicsJsonValue = new DynamicsJsonValue();
            CEActivityResponseModel ceActivityResponseModel = new CEActivityResponseModel();

            if (!string.IsNullOrEmpty(eventKey))
            {
                string query = string.Format(Utility.getEventResponseNew, eventKey);
                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        dynamicsJsonValue = JsonConvert.DeserializeObject<DynamicsJsonValue>(answerResponse.Content.ReadAsStringAsync().Result);
                        string jsonResponse = Convert.ToString(dynamicsJsonValue.jsonResponse);
                        dynamicsJsonValue.jsonResponse = null;
                        Event_SessionResponse event_SessionResponse = new Event_SessionResponse();

                        List<EventResponse> eventResponse = JsonConvert.DeserializeObject<List<EventResponse>>(jsonResponse);
                        if (eventResponse.Count > 0)
                        {
                            //ceActivityResponseModel.eventResponse = eventResponse;
                            event_SessionResponse.RecordType = "event";
                            event_SessionResponse.Name = eventResponse[0].aacn_event_name;
                            event_SessionResponse.RecordId = eventResponse[0].aacn_eventid;
                            event_SessionResponse.Record_Code = eventResponse[0].aacn_event_id;
                            event_SessionResponse.Record_Name = eventResponse[0].aacn_event_name;
                            event_SessionResponse.aacn_assessment_code = eventResponse[0].aacn_assessment_code;
                            event_SessionResponse._aacn_survey_provider_value = eventResponse[0]._aacn_survey_provider_value;
                            event_SessionResponse.statecode = eventResponse[0].statecode;
                            event_SessionResponse.aacn_start_date = eventResponse[0].aacn_start_date;
                            event_SessionResponse.aacn_end_date = eventResponse[0].aacn_end_date;
                            event_SessionResponse.childEvents = eventResponse[0].childEvents;
                        }

                        if (!string.IsNullOrEmpty(customerKey))
                        {
                            List<contactRecords> _recordData = getContactData(customerKey);
                            if (_recordData != null && _recordData.Count > 0)
                            {
                                ceActivityResponseModel.contactResponse = _recordData[0];
                            }
                        }

                        if (event_SessionResponse != null && event_SessionResponse.RecordId != null)
                        {
                            ceActivityResponseModel.responseData = new List<Event_SessionResponse>();
                            ceActivityResponseModel.responseData.Add(event_SessionResponse);
                        }
                    }
                }

                #region sorting
                List<assessmentHeaderLineRecords> sortedList = new List<assessmentHeaderLineRecords>();
                //For Sorting Purpose
                if (ceActivityResponseModel.responseData != null && ceActivityResponseModel.responseData.Any())
                {
                    if (ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines != null)
                    {
                        sortedList = ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines.OrderBy(x => string.IsNullOrEmpty(x.assessment_Line_Order) == true ? int.MaxValue.ToString() : x.assessment_Line_Order)
                        .ToList();
                        if (sortedList != null)
                        {
                            ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines = sortedList;
                        }

                    }
                    var assessmentLines = ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines;
                    if (assessmentLines != null && assessmentLines.Any())
                    {
                        foreach (var assessmentLine in assessmentLines)
                        {
                            if (assessmentLine.assessment_Line_Order != null)
                            {  //First Level Sorting - Section(By Order)
                                assessmentLine.referenceLines = SortLines(assessmentLine.referenceLines);
                                assessmentLine.options = SortOptions(assessmentLine.options, "LineOrder");

                                //Second Level Sorting - Question/Group (By Order)
                                foreach (var referenceLine in assessmentLine.referenceLines)
                                {   //Third Level Sorting for Questions/Options.

                                    referenceLine.childLines = SortLines(referenceLine.childLines);

                                    foreach (var childOptions in referenceLine.childLines)
                                    {
                                        childOptions.optionLines = SortOptions(childOptions.optionLines, "LineOrder");
                                    }
                                    referenceLine.optionLines = SortOptions(referenceLine.optionLines, "LineOrder");

                                }
                            }
                        }
                        ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines = assessmentLines;
                    }
                }

                // Sorting method for lines
                List<referenceLines> SortLines(List<referenceLines> lines)
                {
                    if (lines == null)
                        return new List<referenceLines>();

                    return lines.OrderBy(x => string.IsNullOrEmpty(x.assessment_Line_Order) == true ? int.MaxValue.ToString() : x.assessment_Line_Order)
                                 .ToList();
                }

                //Sorting method for options
                List<assessmentLineOptionRecords> SortOptions(List<assessmentLineOptionRecords> options, string sortType)
                {
                    if (sortType == "LineOrder")
                    {
                        if (options == null)
                            return new List<assessmentLineOptionRecords>();
                        return options.OrderBy(x => string.IsNullOrEmpty(x.aacn_assessment_line_option_sort_order) == true ? int.MaxValue : int.Parse(x.aacn_assessment_line_option_sort_order))
                                                                              .ToList();
                    }
                    else
                    {
                        if (options == null)
                            return new List<assessmentLineOptionRecords>();
                        return options.OrderBy(x => string.IsNullOrEmpty(x.aacn_assessment_line_question_option_text) == true ? "Z".ToString() : x.aacn_assessment_line_question_option_text)
                                                                              .ToList();
                    }
                }
                #endregion
            }
            return ceActivityResponseModel;

        }


        public string checkSession_EventByName(string Name)
        {
            string result = "";
            EventSessionResponseData eventSessionResponseData = new EventSessionResponseData();

            string query = string.Format(Utility.getEventsData, Name);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    eventSessionResponseData = JsonConvert.DeserializeObject<EventSessionResponseData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    if (eventSessionResponseData.value != null && eventSessionResponseData.value.Count > 0)
                    {
                        result = "event";
                    }
                    else
                    {
                        string query2 = string.Format(Utility.getSessionData, Name);
                        HttpResponseMessage sessionResponse = GetRecords(query2);
                        if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                        {
                            eventSessionResponseData = JsonConvert.DeserializeObject<EventSessionResponseData>(Utility.RemoveJsonNulls(sessionResponse.Content.ReadAsStringAsync().Result));
                            if (eventSessionResponseData.value != null && eventSessionResponseData.value.Count > 0)
                            {
                                result = "session";
                            }
                        }

                    }
                }
                else
                {

                }


            }
            return result;

        }

        public List<contactRecords> getContactData(string userId)
        {

            ContactResponseData _contactRecord = new ContactResponseData();
            string query = string.Format(Utility.getContactRecords, userId);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    _contactRecord = JsonConvert.DeserializeObject<ContactResponseData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));

                }
                else
                {

                }


            }
            return _contactRecord.value;

        }

        public HttpResponseMessage CheckProgramStatus(string CustomerKey, string EventKey)
        {
            string EventCode = EventKey;
            List<contactRecords> _recordData = getContactData(CustomerKey);
            if (_recordData != null && _recordData.Count > 0)
            {
                CustomerKey = _recordData[0].contactid.ToString();
                EventRootModel eventdata = check_EventinD365ByName(EventKey);
                if (eventdata != null && eventdata.value.Count > 0)
                {
                    if (!string.IsNullOrEmpty(eventdata.value[0].parent_Event_Id))
                    {
                        EventKey = eventdata.value[0].parent_Event_Id;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(eventdata.value[0].event_Id))
                        {
                            EventKey = eventdata.value[0].event_Id;
                        }
                    }
                }

                //string baseUrl = "https://func-eventeval-dev-001.azurewebsites.net/api/CanEvaluateProgram?code=oz1sL7hR4EMFshdPY7OxxH1re_Q1fmLZUiwKZBlA3mYqAzFuyKHWSw==&";
                string baseUrl = PEBusinessRule_AzureEndpoint;
                string url = $"CustomerKey={CustomerKey}&ParentEventKey={EventKey}&ChildEventCode={EventCode}";
                using (HttpClient client = new HttpClient())
                {
                    HttpResponseMessage response = client.GetAsync(baseUrl + url).Result;
                    return response;
                }
            }
            else
            {
                string ErrorMessage = "The member number " + CustomerKey + " does not exist in the assessment platform";
                string Error = $"{{\"error\":[\"{ErrorMessage}\"]}}";

                return new HttpResponseMessage { StatusCode = System.Net.HttpStatusCode.BadRequest, Content = new StringContent(Error) };
                //return new HttpResponseMessage { StatusCode = System.Net.HttpStatusCode.BadRequest, Content = new StringContent("Contact Number - " + CustomerKey + "doesn't exists into System") };

            }
        }

        public string check_EventinD365(string Name)
        {
            EventSessionResponseData eventSessionResponseData = new EventSessionResponseData();
            string evnetId = string.Empty;
            try
            {

                string query = string.Format(Utility.getEventsData, Name);
                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        eventSessionResponseData = JsonConvert.DeserializeObject<EventSessionResponseData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                        if (eventSessionResponseData.value != null && eventSessionResponseData.value.Count > 0)
                        {
                            evnetId = eventSessionResponseData.value[0].aacn_eventid;
                        }
                    }
                    else
                    {

                    }


                }
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
            return evnetId;

        }

        public EventRootModel check_EventinD365ByName(string Name)
        {
            EventSessionResponseData eventSessionResponseData = new EventSessionResponseData();
            EventRootModel eventmodel = new EventRootModel();
            try
            {

                string query = string.Format(Utility.getparenteventIdbyName, Name);
                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        eventmodel = JsonConvert.DeserializeObject<EventRootModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    }
                    else
                    {

                    }


                }
            }
            catch (Exception ex)
            {
                return eventmodel;
            }
            return eventmodel;

        }
    }
}

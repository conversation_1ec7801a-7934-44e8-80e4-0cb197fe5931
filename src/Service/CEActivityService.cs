﻿namespace AACN.API.Service
{
    using AACN.API.Model;
    using AACN.Services;
    using Microsoft.Extensions.Options;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Data.Entity.Infrastructure;
    using System.Linq;
    using System.Net.Http;
    using System.Security.Cryptography.Xml;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;

    public class CEActivityService : BaseService
    {

        public CEActivityResponseModel GetEvaluationData(string customernumber, string sessionCode, string parentEventCode)
        {
            List<tempSpeakerData> speakerData = new List<tempSpeakerData>(); //Initialize Empty List Of Speakers 
            DynamicsJsonValue dynamicsJsonValue = new DynamicsJsonValue();
            CEActivityResponseModel ceActivityResponseModel = new CEActivityResponseModel();
            EventRootModel eventdata = check_EventinD365(parentEventCode);
            string query = "";
            if (!string.IsNullOrEmpty(sessionCode))
            {
                query = string.Format(Utility.getSessionResponseNew, sessionCode);
                if (eventdata != null && eventdata.value.Count > 0)
                {
                    if (!string.IsNullOrEmpty(eventdata.value[0].parent_Event_Id))
                    {
                        query = string.Format(Utility.getSessionResponseNewwitheventCode, sessionCode, eventdata.value[0].parent_Event_Id);
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(eventdata.value[0].event_Id))
                        {
                            query = string.Format(Utility.getSessionResponseNewwitheventCode, sessionCode, eventdata.value[0].event_Id);
                        }
                    }

                }

                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        dynamicsJsonValue = JsonConvert.DeserializeObject<DynamicsJsonValue>(answerResponse.Content.ReadAsStringAsync().Result);
                        string jsonResponse = Convert.ToString(dynamicsJsonValue.jsonResponse);
                        dynamicsJsonValue.jsonResponse = null;
                        Event_SessionResponse event_SessionResponse = new Event_SessionResponse();
                        List<SessionResponse> sessionResponse = JsonConvert.DeserializeObject<List<SessionResponse>>(jsonResponse);
                        if (sessionResponse.Count > 0)
                        {
                            event_SessionResponse.RecordType = "session";
                            event_SessionResponse.Name = sessionResponse[0].aacn_session_name;
                            event_SessionResponse.RecordId = sessionResponse[0].aacn_sessionid;
                            event_SessionResponse.Record_Code = sessionResponse[0].aacn_session_id;
                            event_SessionResponse.Record_Name = sessionResponse[0].aacn_session_name;
                            event_SessionResponse.aacn_assessment_code = sessionResponse[0].aacn_assessment_code;
                            event_SessionResponse._aacn_survey_provider_value = sessionResponse[0]._aacn_survey_provider_value;
                            event_SessionResponse.statecode = sessionResponse[0].statecode;
                            event_SessionResponse.aacn_start_date = sessionResponse[0].aacn_start_date;
                            event_SessionResponse.aacn_end_date = sessionResponse[0].aacn_end_date;
                            event_SessionResponse.aacn_speaker_session_aacn_session = sessionResponse[0].aacn_speaker_session_aacn_session;
                            event_SessionResponse.event_code = sessionResponse[0].event_Id;
                            event_SessionResponse.aacn_event_id = sessionResponse[0].aacn_event_id;
                            event_SessionResponse.record_Description = sessionResponse[0].aacn_session_description;
                        }

                        if (!string.IsNullOrEmpty(customernumber))
                        {
                            List<contactRecords> _recordData = getContactData(customernumber);
                            if (_recordData != null && _recordData.Count > 0)
                            {
                                ceActivityResponseModel.contactResponse = _recordData[0];
                            }
                        }

                        //Add My Converted Model 
                        if (event_SessionResponse != null && event_SessionResponse.RecordId != null)
                        {
                            ceActivityResponseModel.responseData = new List<Event_SessionResponse>();
                            ceActivityResponseModel.responseData.Add(event_SessionResponse);
                            //Conversion of data into temporary speaker Model

                            if (ceActivityResponseModel.responseData[0].aacn_speaker_session_aacn_session != null)
                            {
                                speakerData = ceActivityResponseModel.responseData[0].aacn_speaker_session_aacn_session.Select(o => new tempSpeakerData
                                {
                                    speaker_Name = o.aacn_speaker_name,
                                    speakerId = o.aacn_speakerid,
                                    speaker_Number = o.aacn_speaker_number,
                                    speaker_Value = o._aacn_speaker_value,
                                    property_Used = false,
                                    speaker_Sort_Order = o.speaker_Sort_Order,


                                }).OrderBy(o => o.speaker_Sort_Order).ToList();
                            }

                            //Old Logic
                            //var primarySpeakers = speakerData.Where(o => o.speaker_Sort_Order == 1)
                            //          .OrderBy(o => o.speaker_Name);
                            //var secondarySpeakers = speakerData.Where(o => o.speaker_Sort_Order != 1).OrderBy(o => o.speaker_Name);
                            //var sortedSpeakerData = primarySpeakers.Concat(secondarySpeakers).ToList();
                            //speakerData = sortedSpeakerData;
                        }
                    }
                }

                #region sorting
                List<assessmentHeaderLineRecords> sortedList = new List<assessmentHeaderLineRecords>();

                //For Sorting Purpose
                if (ceActivityResponseModel.responseData != null && ceActivityResponseModel.responseData.Any())

                {
                    if (ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines != null)
                    {
                        sortedList = ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines.OrderBy(x => string.IsNullOrEmpty(x.assessment_Line_Order) == true ? int.MaxValue.ToString() : x.assessment_Line_Order)
                        .ToList();
                        if (sortedList != null)
                        {
                            ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines = sortedList;
                        }

                    }
                    var assessmentLines = ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines;
                    if (assessmentLines != null && assessmentLines.Any())
                    {
                        foreach (var assessmentLine in assessmentLines)
                        {
                            if (assessmentLine.assessment_Line_Order != null)
                            {    //First Level Sorting - Section(By Order)
                                assessmentLine.referenceLines = SortLines(assessmentLine.referenceLines);
                                assessmentLine.options = SortOptions(assessmentLine.options, "LineOrder");

                                //Second Level Sorting - Question/Group (By Order)
                                foreach (var referenceLine in assessmentLine.referenceLines)
                                {
                                    //Third Level Sorting for Questions/Options.
                                    referenceLine.childLines = SortLines(referenceLine.childLines);
                                    foreach (var childOptions in referenceLine.childLines)
                                    {
                                        childOptions.optionLines = SortOptions(childOptions.optionLines, "LineOrder");

                                    }
                                    referenceLine.optionLines = SortOptions(referenceLine.optionLines, "LineOrder");
                                    #region SpeakerLogic
                                    if (referenceLine.aacn_assessment_line_type == "*********") //Question
                                    {
                                        if (referenceLine.aacn_assessment_line_question_text != null && Regex.IsMatch(referenceLine.aacn_assessment_line_question_text, @"\[Speaker(?: \d+)?\]"))
                                        {
                                            int usedCounter = speakerData.Count(speaker => speaker.property_Used);
                                            if (usedCounter >= speakerData.Count || usedCounter > 5)
                                            {
                                                referenceLine.status = "Inactive";
                                                continue;
                                            }
                                            else
                                            {
                                                for (int l = 0; l < speakerData.Count; l++)
                                                {
                                                    if (speakerData[l].property_Used != true)
                                                    {
                                                        referenceLine.aacn_assessment_line_question_text = speakerData[l].speaker_Name;
                                                        referenceLine.speakerId = speakerData[l].speakerId;
                                                        speakerData[l].property_Used = true;
                                                        continue;
                                                    }
                                                }
                                            }

                                        }
                                    }
                                    else
                                    {

                                        foreach (var GroupQuestions in referenceLine.childLines)
                                        {
                                            if (GroupQuestions.aacn_assessment_line_type == "*********") //Question
                                            {
                                                if (GroupQuestions.aacn_assessment_line_question_text != null && Regex.IsMatch(GroupQuestions.aacn_assessment_line_question_text, @"\[Speaker(?:\s?\d+)?\]"))
                                                {
                                                    int usedCounter = speakerData.Count(speaker => speaker.property_Used);
                                                    if (usedCounter >= speakerData.Count || usedCounter > 5)
                                                    {
                                                        GroupQuestions.status = "Inactive";
                                                        continue;
                                                    }
                                                    else
                                                    {
                                                        for (int l = 0; l < speakerData.Count; l++)
                                                        {
                                                            if (speakerData[l].property_Used != true)
                                                            {
                                                                GroupQuestions.aacn_assessment_line_question_text = speakerData[l].speaker_Name;
                                                                GroupQuestions.speakerId = speakerData[l].speakerId;
                                                                speakerData[l].property_Used = true;
                                                                break;
                                                            }
                                                        }
                                                    }

                                                }
                                            }
                                        }
                                    }
                                    #endregion
                                }
                            }
                        }
                        ceActivityResponseModel.responseData[0].aacn_assessment_code.AssessmentsLines = assessmentLines;
                    }
                }

                // Sorting method for lines
                List<referenceLines> SortLines(List<referenceLines> lines)
                {
                    if (lines == null)
                        return new List<referenceLines>();

                    return lines.OrderBy(x => string.IsNullOrEmpty(x.assessment_Line_Order) == true ? int.MaxValue.ToString() : x.assessment_Line_Order)
                                 .ToList();
                }

                // Sorting method for options
                List<assessmentLineOptionRecords> SortOptions(List<assessmentLineOptionRecords> options, string sortType)
                {
                    if (sortType == "LineOrder")
                    {
                        if (options == null)
                            return new List<assessmentLineOptionRecords>();
                        return options.OrderBy(x => string.IsNullOrEmpty(x.aacn_assessment_line_option_sort_order) == true ? int.MaxValue.ToString() : x.aacn_assessment_line_option_sort_order)
                                                                              .ToList();
                    }
                    else
                    {
                        if (options == null)
                            return new List<assessmentLineOptionRecords>();
                        return options.OrderBy(x => string.IsNullOrEmpty(x.aacn_assessment_line_question_option_text) == true ? "Z".ToString() : x.aacn_assessment_line_question_option_text)
                                                                              .ToList();
                    }
                }
                #endregion


            }
            return ceActivityResponseModel;

        }
        public string checkSession_EventByName(string Name)
        {
            string result = "";
            EventSessionResponseData eventSessionResponseData = new EventSessionResponseData();

            string query = string.Format(Utility.getEventsData, Name);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    eventSessionResponseData = JsonConvert.DeserializeObject<EventSessionResponseData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    if (eventSessionResponseData.value != null && eventSessionResponseData.value.Count > 0)
                    {
                        result = "event";
                    }
                    else
                    {
                        string query2 = string.Format(Utility.getSessionData, Name);
                        HttpResponseMessage sessionResponse = GetRecords(query2);
                        if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                        {
                            eventSessionResponseData = JsonConvert.DeserializeObject<EventSessionResponseData>(Utility.RemoveJsonNulls(sessionResponse.Content.ReadAsStringAsync().Result));
                            if (eventSessionResponseData.value != null && eventSessionResponseData.value.Count > 0)
                            {
                                result = "session";
                            }
                        }

                    }
                }
                else
                {

                }


            }
            return result;

        }

        public List<contactRecords> getContactData(string userId)
        {

            ContactResponseData _contactRecord = new ContactResponseData();
            string query = string.Format(Utility.getContactRecords, userId);
            HttpResponseMessage answerResponse = GetRecords(query);
            if (answerResponse.IsSuccessStatusCode == true)
            {
                if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                {
                    _contactRecord = JsonConvert.DeserializeObject<ContactResponseData>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));

                }
                else
                {

                }


            }
            return _contactRecord.value;
        }

        public HttpResponseMessage CheckSessionStatus(string CustomerKey, string SessionCode, string CeActivityCode)
        {
            List<contactRecords> _recordData = getContactData(CustomerKey);
            if (_recordData != null && _recordData.Count > 0)
            {
                CustomerKey = _recordData[0].contactid.ToString();
                EventRootModel eventdata = check_EventinD365(CeActivityCode);
                if (eventdata != null && eventdata.value.Count > 0)
                {
                    if (!string.IsNullOrEmpty(eventdata.value[0].parent_Event_Id))
                    {
                        CeActivityCode = eventdata.value[0].parent_Event_Code;

                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(eventdata.value[0].event_Id))
                        {
                            CeActivityCode = eventdata.value[0].event_Code;
                        }
                    }
                }
                //string baseUrl = "https://func-eventeval-dev-001.azurewebsites.net/api/CanEvaluateSession?code=oz1sL7hR4EMFshdPY7OxxH1re_Q1fmLZUiwKZBlA3mYqAzFuyKHWSw==&";
                string baseUrl = CEBusinessRule_AzureEndpoint;
                string url = $"CustomerKey={CustomerKey}&SessionCode={SessionCode}&ParentEventCode={CeActivityCode}";
                using (HttpClient client = new HttpClient())
                {
                    HttpResponseMessage response = client.GetAsync(baseUrl + url).Result;
                    return response;
                }
            }

            else
            {
                string ErrorMessage = "The member number " + CustomerKey + " does not exist in the assessment platform";
                string JsonError = $"{{\"error\":[\"{ErrorMessage}\"]}}";

                return new HttpResponseMessage { StatusCode = System.Net.HttpStatusCode.BadRequest, Content = new StringContent(JsonError) };
            }

        }

        public EventRootModel check_EventinD365(string Name)
        {
            EventSessionResponseData eventSessionResponseData = new EventSessionResponseData();
            EventRootModel eventmodel = new EventRootModel();
            try
            {

                string query = string.Format(Utility.getparenteventIdbyName, Name);
                HttpResponseMessage answerResponse = GetRecords(query);
                if (answerResponse.IsSuccessStatusCode == true)
                {
                    if (!string.IsNullOrEmpty(answerResponse.Content.ReadAsStringAsync().Result))
                    {
                        eventmodel = JsonConvert.DeserializeObject<EventRootModel>(Utility.RemoveJsonNulls(answerResponse.Content.ReadAsStringAsync().Result));
                    }
                    else
                    {

                    }


                }
            }
            catch (Exception ex)
            {
                return eventmodel;
            }
            return eventmodel;

        }


    }
}

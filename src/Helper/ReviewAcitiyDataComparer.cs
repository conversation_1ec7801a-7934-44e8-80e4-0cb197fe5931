using AACN.API.Model.Manager;
using System.Collections.Generic;

public class ReviewAcitiyDataComparer : IComparer<ReviewAcitiyData>
{
    private readonly List<string> _order = new List<string>
    {
        "Demographics",
        "NursingWorkforce",
        "PatientOutcomes",
        "WorkEnvironment"
    };

    public int Compare(ReviewAcitiyData x, ReviewAcitiyData y)
    {
        if (x == null || y == null)
        {
            return 0;
        }

        int indexX = _order.IndexOf(x.aacn_reviewer_module.aacn_assessment_title);
        int indexY = _order.IndexOf(y.aacn_reviewer_module.aacn_assessment_title);

        return indexX.CompareTo(indexY);
    }
}

﻿namespace AACN.API.Helper.AssessmentHelper
{
    using Newtonsoft.Json;
    using System.Collections.Generic;
    using System;
    using static AACN.API.Model.ReviewerAssessmentLoadModel.ReviewerAssessmentLoadResponseModel;
    using AACN.API.Model;
    using System.Linq;

    public class AacnAssessmentLineAssessmentHeaderAacnA
    {
        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_header_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_header_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_header_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_assessment_header_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_header_value { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string statecodeODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("statecode", NullValueHandling = NullValueHandling.Ignore)]
        public int statecode { get; set; }

        [JsonProperty("aacn_assessment_line_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_number { get; set; }

        [JsonProperty("aacn_assessment_line_order", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_order { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_requiredODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("aacn_assessment_line_required", NullValueHandling = NullValueHandling.Ignore)]
        public bool aacn_assessment_line_required { get; set; }

        [JsonProperty("aacn_assessment_line_title", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_title { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_typeODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("aacn_assessment_line_type", NullValueHandling = NullValueHandling.Ignore)]
        public int aacn_assessment_line_type { get; set; }

        [JsonProperty("aacn_assessment_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_lineid { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _createdby_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _createdby_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_createdby_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _createdby_value { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string createdonODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("createdon", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime createdon { get; set; }

        [JsonProperty("aacn_assessment_line_option_assessment_line_aa", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> aacn_assessment_line_option_assessment_line_aa { get; set; }

        [JsonProperty("aacn_assessment_line_assessment_line_reference", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnAssessmentLineAssessmentLineReference> aacn_assessment_line_assessment_line_reference { get; set; }
    }

    public class AacnAssessmentLineAssessmentLineReference
    {
        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string statecodeODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("statecode", NullValueHandling = NullValueHandling.Ignore)]
        public int statecode { get; set; }

        [JsonProperty("aacn_assessment_line_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_number { get; set; }

        [JsonProperty("aacn_assessment_line_order", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_order { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_orientationODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("aacn_assessment_line_orientation", NullValueHandling = NullValueHandling.Ignore)]
        public int aacn_assessment_line_orientation { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_reference_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_reference_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_reference_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_assessment_line_reference_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_reference_value { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_requiredODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("aacn_assessment_line_required", NullValueHandling = NullValueHandling.Ignore)]
        public bool aacn_assessment_line_required { get; set; }

        [JsonProperty("aacn_assessment_line_title", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_title { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_typeODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("aacn_assessment_line_type", NullValueHandling = NullValueHandling.Ignore)]
        public int aacn_assessment_line_type { get; set; }

        [JsonProperty("aacn_assessment_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_lineid { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _createdby_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _createdby_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_createdby_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _createdby_value { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string createdonODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("createdon", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime createdon { get; set; }

        [JsonProperty("aacn_assessment_line_option_assessment_line_aa", NullValueHandling = NullValueHandling.Ignore)]
        public List<object> aacn_assessment_line_option_assessment_line_aa { get; set; }

        [JsonProperty("aacn_assessment_line_assessment_line_reference", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnAssessmentLineAssessmentLineReference> aacn_assessment_line_assessment_line_reference { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_controlODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("aacn_assessment_line_control", NullValueHandling = NullValueHandling.Ignore)]
        public int aacn_assessment_line_control { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_question_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_question_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_question_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_assessment_line_question_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_question_value { get; set; }

        [JsonProperty("aacn_assessment_line_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_question_text { get; set; }
    }

    public class AacnAssessmentLineOptionAssessmentLineAa
    {
        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_assessment_line_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_value { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string statecodeODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("statecode", NullValueHandling = NullValueHandling.Ignore)]
        public int statecode { get; set; }

        [JsonProperty("aacn_assessment_line_option_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_option_number { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_option_requiredODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("aacn_assessment_line_option_required", NullValueHandling = NullValueHandling.Ignore)]
        public bool aacn_assessment_line_option_required { get; set; }

        [JsonProperty("aacn_assessment_line_optionid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_optionid { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_question_option_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_question_option_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_question_option_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_assessment_line_question_option_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_question_option_value { get; set; }

        [JsonProperty("aacn_assessment_line_question_option_text", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_question_option_text { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string createdonODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("createdon", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime createdon { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string modifiedonODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("modifiedon", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime modifiedon { get; set; }
    }

    public class AssessmentHelper
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcount", NullValueHandling = NullValueHandling.Ignore)]
        public int MicrosoftDynamicsCRMtotalrecordcount { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcountlimitexceeded", NullValueHandling = NullValueHandling.Ignore)]
        public bool MicrosoftDynamicsCRMtotalrecordcountlimitexceeded { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.globalmetadataversion", NullValueHandling = NullValueHandling.Ignore)]
        public string MicrosoftDynamicsCRMglobalmetadataversion { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<AssessmentdataRecords> assessmentData { get; set; }
    }

    public static class AssessmentHelperExtensions
    {
        public static List<assessmentHeaderLineRecords> SortAssessments(List<assessmentHeaderLineRecords> assessmentLines)
        {
            // Check if the input list is null or empty
            if (assessmentLines == null || !assessmentLines.Any())
            {
                return new List<assessmentHeaderLineRecords>();
            }

            // Sort the top-level assessment lines by the assessment line order
            var sortedList = assessmentLines
                .OrderBy(x => string.IsNullOrEmpty(x.assessment_Line_Order) ? int.MaxValue.ToString() : x.assessment_Line_Order)
                .ToList();

            // Iterate through each assessment line for further sorting
            foreach (var assessmentLine in sortedList)
            {
                if (assessmentLine.assessment_Line_Order != null)
                {
                    // Sort reference lines
                    assessmentLine.referenceLines = SortLines(assessmentLine.referenceLines);
                    // Sort options
                    assessmentLine.options = SortOptions(assessmentLine.options, "LineOrder");

                    // Sort within each reference line
                    foreach (var referenceLine in assessmentLine.referenceLines)
                    {
                        // Sort child lines
                        referenceLine.childLines = SortLines(referenceLine.childLines);
                        foreach (var childOptions in referenceLine.childLines)
                        {
                            // Sort options within child lines
                            childOptions.optionLines = SortOptions(childOptions.optionLines, "LineOrder");
                        }
                        // Sort options within reference lines
                        referenceLine.optionLines = SortOptions(referenceLine.optionLines, "LineOrder");
                    }
                }
            }

            return sortedList;
        }


        private static List<referenceLines> SortLines(List<referenceLines> lines)
        {
            if (lines == null)
                return new List<referenceLines>();

            return lines.OrderBy(x => string.IsNullOrEmpty(x.assessment_Line_Order) == true ? int.MaxValue.ToString() : x.assessment_Line_Order)
                         .ToList();
        }
        private static List<assessmentLineOptionRecords> SortOptions(List<assessmentLineOptionRecords> options, string sortType)
        {
            if (sortType == "LineOrder")
            {
                if (options == null)
                    return new List<assessmentLineOptionRecords>();
                return options.OrderBy(x => string.IsNullOrEmpty(x.aacn_assessment_line_option_sort_order) == true ? int.MaxValue.ToString() : x.aacn_assessment_line_option_sort_order)
                                                                      .ToList();
            }
            else
            {
                if (options == null)
                    return new List<assessmentLineOptionRecords>();
                return options.OrderBy(x => string.IsNullOrEmpty(x.aacn_assessment_line_question_option_text) == true ? "Z".ToString() : x.aacn_assessment_line_question_option_text)
                                                                      .ToList();
            }
        }

    }


}

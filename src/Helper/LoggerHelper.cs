﻿namespace AACN.API.Helper
{
    using Microsoft.Extensions.Logging;
    using System.Diagnostics;
    using System.Reflection;
    using System.Text;
    using System;

    public static class LoggerHelper
    {
        public static string LogMethodStart(ILogger logger)
        {
            var stackFrame = new StackFrame(1, true);
            var method = stackFrame.GetMethod();

            string className = method.DeclaringType?.Name ?? "UnknownClass";
            string methodName = method.Name;

            if (methodName.Contains("MoveNext"))
            {
                methodName = "AsyncMethod";
            }
            string logMessage = $"Method started: {className}.{methodName}";
            logger.LogInformation(logMessage);
            return logMessage;
        }
        public static string LogMethodEnd(ILogger logger)
        {
            var stackFrame = new StackFrame(1, true);
            var method = stackFrame.GetMethod();

            string className = method.DeclaringType?.Name ?? "UnknownClass";
            string methodName = method.Name;

            if (methodName.Contains("MoveNext"))
            {
                methodName = "AsyncMethod";
            }
            string logMessage = $"Method ended: {className}.{methodName}";
            logger.LogInformation(logMessage);
            return logMessage;
        }
        public static string LogObjectValue(Object objectName, string objectValue = "Not provided")
        {
            var stackFrame = new StackFrame(1, true);
            var method = stackFrame.GetMethod();
            string className = method.DeclaringType?.Name ?? "UnknownClass";
            string methodName = method.Name;

            Type type = objectName.GetType();
            if (type != null)
            {
                objectName = type.Name;
            }
            // Create the log message
            string logMessage = $"Class: {className}, Method: {methodName}, Object: {objectName}, Value: {objectValue}";
            return logMessage;
        }
        public static void LogException<T>(ILogger<T> logger, Exception ex, string additionalInfo = null)
        {
            var messageBuilder = new StringBuilder();
            messageBuilder.AppendLine($"Exception Type: {ex.GetType().Name}");
            messageBuilder.AppendLine($"Message: {ex.Message}");
            messageBuilder.AppendLine($"Stack Trace: {ex.StackTrace}");
            var innerException = ex.InnerException;
            while (innerException != null)
            {
                messageBuilder.AppendLine("Inner Exception:");
                messageBuilder.AppendLine($"   Exception Type: {innerException.GetType().Name}");
                messageBuilder.AppendLine($"   Message: {innerException.Message}");
                messageBuilder.AppendLine($"   Stack Trace: {innerException.StackTrace}");
                innerException = innerException.InnerException;
            }

            logger.LogError(
                 messageBuilder.ToString(),
                 typeof(T).Name,
                 MethodBase.GetCurrentMethod()?.Name,
                 additionalInfo ?? "No additional info provided");
        }
    }
}

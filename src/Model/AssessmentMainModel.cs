﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class AssessmentMainModel
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string OdataContext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<AssessmentValueList> Value { get; set; }
    }

    public class AssessmentValueList
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string OdataEtag { get; set; }

        [JsonProperty("aacn_assessment_description", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnAssessmentDescription { get; set; }

        [JsonProperty("aacn_assessmentid", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnAssessmentid { get; set; }

        [JsonProperty("aacn_assessment_title", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnAssessmentTitle { get; set; }

        [JsonProperty("_aacn_year_value", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnYearValue { get; set; }

        [JsonProperty("_aacn_reviewer_module_value", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnReviewerModuleValue { get; set; }

        [JsonProperty("_aacn_unit_module_value", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnUnitModuleValue { get; set; }

        [JsonProperty("aacn_assessment_number", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnAssessmentNumber { get; set; }

        [JsonProperty("aacn_passing_score", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnPassingScore { get; set; }

        [JsonProperty("_aacn_survey_provider_value", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnSurveyProviderValue { get; set; }
    }


}

﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;

    using System.Collections.Generic;

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class RemoveResponse
    {
        [JsonProperty("SurveyStatuses", NullValueHandling = NullValueHandling.Ignore)]
        public List<SurveyStatus> SurveyStatuses { get; set; }
    }

    public class SurveyStatus
    {
        [JsonProperty("Key", NullValueHandling = NullValueHandling.Ignore)]
        public string Key { get; set; }

        [JsonProperty("Status", NullValueHandling = NullValueHandling.Ignore)]
        public string Status { get; set; }
    }

    public class ResponseLines
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<ResponseLinesdataValues> value { get; set; }
    }

    public class ResponseLinesdataValues
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("aacn_response_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_lineid { get; set; }
    }




    public class ReturnResponse
    {
        public string responsekey {  get; set; }
        public string responsestatus { get; set; }
        public string failedreason { get; set; } = "";
    
    }


    //get ASR, Reviewerresponse, ReviewerResponseline

    public class AsrReviewerResponse
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<AsrReviewerResponseValues> value { get; set; }
    }

    public class AsrReviewerResponseValues
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("aacn_assessment_review_activityid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_review_activityid { get; set; }

        [JsonProperty("_aacn_reviewers_response_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_reviewers_response_value { get; set; }

        [JsonProperty("aacn_reviewers_response", NullValueHandling = NullValueHandling.Ignore)]
        public AacnReviewersResponse aacn_reviewers_response { get; set; }
    }

    public class AacnReviewersResponse
    {
        [JsonProperty("aacn_responseid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_responseid { get; set; }

        [JsonProperty("aacn_response_line_response_aacn_response", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnResponseLineResponseAacnResponseValues> aacn_response_line_response_aacn_response { get; set; }
    }

    public class AacnResponseLineResponseAacnResponseValues
    {
        [JsonProperty("aacn_response_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_lineid { get; set; }
    }

}
﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class CheckSessionCountModel
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string ODataContext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<ValueSessionAssessment> Value { get; set; }
    }

    public class AacnAssessmentLineAssessmentHeaderAacnAA
    {
        [JsonProperty("aacn_assessment_line_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentLineQuestionText { get; set; }

        [JsonProperty("aacn_assessment_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentLineId { get; set; }

        [JsonProperty("aacn_response_line_assesment_line_aacn_assessm", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnResponseLineAssessmentLineAacnAssessment> ResponseLines { get; set; }
    }

    public class AacnResponseLineAssessmentLineAacnAssessment
    {
        [JsonProperty("aacn_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string QuestionText { get; set; }

        [JsonProperty("aacn_response_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseLineId { get; set; }

        [JsonProperty("aacn_response_text", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseText { get; set; }
    }

    public class ValueSessionAssessment
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string ODataEtag { get; set; }

        [JsonProperty("aacn_assessmentid", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentId { get; set; }

        [JsonProperty("aacn_assessment_title", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentTitle { get; set; }

        [JsonProperty("aacn_passing_score", NullValueHandling = NullValueHandling.Ignore)]
        public int PassingScore { get; set; }

        [JsonProperty("aacn_assessment_line_assessment_header_aacn_as", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnAssessmentLineAssessmentHeaderAacnAA> AssessmentLines { get; set; }
    }



}

﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;

    public class ApiAuditLog
    {

        [JsonProperty("aacn_audit_log_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_audit_log_number { get; set; }

        [JsonProperty("aacn_api_endpoint", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_api_endpoint { get; set; }

        [JsonProperty("aacn_request_body", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_request_body { get; set; }

        [JsonProperty("aacn_response_body", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_body { get; set; }

    }
}
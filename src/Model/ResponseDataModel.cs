﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    public class ResponseDataModel
    {

        public InstanceData instance { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<value> responseValue { get; set; }
    }

    public class value
    {
        [JsonProperty("_aacn_assessment_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_value { get; set; }

        [JsonProperty("_aacn_event_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_event_value { get; set; }

        [JsonProperty("_aacn_member_value", NullValueHandling = NullValueHandling.Ignore)]
        public object _aacn_member_value { get; set; }

        [JsonProperty("aacn_response_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_number { get; set; }

        [JsonProperty("aacn_responseid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_responseid { get; set; }

        [JsonProperty("_aacn_session_value", NullValueHandling = NullValueHandling.Ignore)]
        public object _aacn_session_value { get; set; }

        [JsonProperty("_aacn_survey_provider_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_survey_provider_value { get; set; }

        [JsonProperty("aacn_response_line_response_aacn_response", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnResponseLineResponseAacnResponse> aacn_response_line_response_aacn_response { get; set; }

    }

    public class AacnResponseLineResponseAacnResponse
    {

        [JsonProperty("_aacn_answer_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_answer_value { get; set; }

        [JsonProperty("_aacn_assesment_line_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assesment_line_value { get; set; }

        [JsonProperty("_aacn_assessment_line_option_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_option_value { get; set; }

        [JsonProperty("_aacn_question_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_question_value { get; set; }

        [JsonProperty("aacn_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_question_text { get; set; }

        [JsonProperty("_aacn_response_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_response_value { get; set; }

        [JsonProperty("aacn_response_line_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_line_number { get; set; }

        [JsonProperty("aacn_response_line_text_area", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_line_text_area { get; set; }

        [JsonProperty("aacn_response_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_lineid { get; set; }

        [JsonProperty("aacn_response_text", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_text { get; set; }
        public string GroupName { get; set; }

        [JsonProperty("aacn_assesment_line", NullValueHandling = NullValueHandling.Ignore)]
        public AacnAssesmentLine aacn_assesment_line { get; set; }
    }

    public class AacnAssesmentLine
    {
        [JsonProperty("aacn_tool_tip_description", NullValueHandling = NullValueHandling.Ignore)]
        public string GroupName { get; set; }

        [JsonProperty("aacn_assessment_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_lineid { get; set; }
    }

    public class InstanceData
    {

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<instanceData> instancerecord { get; set; }

        public class instanceData
        {
            [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
            public string odataetag { get; set; }

            [JsonProperty("_aacn_assessment_code_value", NullValueHandling = NullValueHandling.Ignore)]
            public string Assessment_Code { get; set; }

            [JsonProperty("aacn_end_date", NullValueHandling = NullValueHandling.Ignore)]
            public DateTime aacn_end_date { get; set; }

            [JsonProperty("aacn_instance_id", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_instance_id { get; set; }

            [JsonProperty("aacn_start_date", NullValueHandling = NullValueHandling.Ignore)]
            public DateTime aacn_start_date { get; set; }

            [JsonProperty("aacn_instanceid", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_instanceid { get; set; }
        }

    }
}

﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class NtiSessionResponseModel
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string ODataContext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<ValueResponse> Value { get; set; }
    }

    public class AacnAssessment
    {
        [JsonProperty("aacn_assessmentid", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentId { get; set; }

        [JsonProperty("aacn_assessment_title", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentTitle { get; set; }
    }

    public class AacnResponseLineResponseAacnResponseValue
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string ODataETag { get; set; }

        [JsonProperty("aacn_response_text", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseText { get; set; }

        [JsonProperty("aacn_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string QuestionText { get; set; }

        [JsonProperty("_aacn_response_value", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseValue { get; set; }

        [JsonProperty("aacn_response_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseLineId { get; set; }
    }

    public class ValueResponse
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string ODataETag { get; set; }

        [JsonProperty("_aacn_assessment_value", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentValue { get; set; }

        [JsonProperty("_aacn_survey_provider_value", NullValueHandling = NullValueHandling.Ignore)]
        public string SurveyProviderValue { get; set; }

        [JsonProperty("aacn_responseid", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseId { get; set; }

        [JsonProperty("aacn_assessment", NullValueHandling = NullValueHandling.Ignore)]
        public AacnAssessment Assessment { get; set; }

        [JsonProperty("aacn_response_line_response_aacn_response", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnResponseLineResponseAacnResponseValue> ResponseLines { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseLinesNextLink { get; set; }
    }

}

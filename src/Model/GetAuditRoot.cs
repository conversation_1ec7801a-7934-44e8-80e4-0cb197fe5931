﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);

    public class GetAuditRoot
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("@totalcount", NullValueHandling = NullValueHandling.Ignore)]
        public string totalcount { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<AuditReportValues> auditReportvalue { get; set; }
    }

    public class AuditReportValues
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("aacn_audit_report_dataid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_audit_report_dataid { get; set; }

        [JsonProperty("aacn_assessment_review_activity_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_review_activity_number { get; set; }
        
        [JsonProperty("aacn_application_id", NullValueHandling = NullValueHandling.Ignore)]
        public int ApplicationId { get; set; }

        [JsonProperty("aacn_Unit", NullValueHandling = NullValueHandling.Ignore)]
        public AacnAuditUnit aacn_Unit { get; set; }

        [JsonProperty("aacn_reviewer", NullValueHandling = NullValueHandling.Ignore)]
        public AacnAuditReviewer aacn_reviewer { get; set; }

        [JsonProperty("aacn_unit_module", NullValueHandling = NullValueHandling.Ignore)]
        public AacnAuditUnitModule aacn_unit_module { get; set; }

        [JsonProperty("aacn_year", NullValueHandling = NullValueHandling.Ignore)]
        public AacnBeaconCycleYear aacn_beacon_cycle_year { get; set; }
    }

    public class AacnBeaconCycleYear
    {
        [JsonProperty("aacn_year", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_year { get; set; }

        [JsonProperty("aacn_yearid", NullValueHandling = NullValueHandling.Ignore)]
        public string YearId { get; set; }
    }
    public class ParentaccountidAudit
    {
        [JsonProperty("accountnumber", NullValueHandling = NullValueHandling.Ignore)]
        public string accountnumber { get; set; }

        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string name { get; set; }

        [JsonProperty("accountid", NullValueHandling = NullValueHandling.Ignore)]
        public string parent_accountid { get; set; }
    }

    public class AacnAuditReviewer
    {
        [JsonProperty("aacn_contact_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_contact_number { get; set; }

        [JsonProperty("emailaddress1", NullValueHandling = NullValueHandling.Ignore)]
        public string emailaddress1 { get; set; }

        [JsonProperty("firstname", NullValueHandling = NullValueHandling.Ignore)]
        public string firstname { get; set; }

        [JsonProperty("lastname", NullValueHandling = NullValueHandling.Ignore)]
        public string lastname { get; set; }

        [JsonProperty("contactid", NullValueHandling = NullValueHandling.Ignore)]
        public string contactid { get; set; }
    }

    public class AacnAuditUnit
    {
        [JsonProperty("accountnumber", NullValueHandling = NullValueHandling.Ignore)]
        public string accountnumber { get; set; }

        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string name { get; set; }

        [JsonProperty("accountid", NullValueHandling = NullValueHandling.Ignore)]
        public string accountid { get; set; }

        [JsonProperty("parentaccountid", NullValueHandling = NullValueHandling.Ignore)]
        public ParentaccountidAudit parentaccountid { get; set; }
    }

    public class AacnAuditUnitModule
    {
        [JsonProperty("aacn_assessment_title", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_title { get; set; }

        [JsonProperty("aacn_assessmentid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessmentid { get; set; }
    }


}
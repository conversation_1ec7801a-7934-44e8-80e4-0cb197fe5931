﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    public class responsereqModel_PE
    {
        [JsonProperty("ResponseId")]
        public string ResponseId { get; set; }

        [JsonProperty("CustomerKey")]
        public string CustomerKey { get; set; }

        [JsonProperty("ChildEventCode")]
        public string ChildEventCode { get; set; }

        [JsonProperty("ParentEventCode")]
        public string ParentEventCode { get; set; }

        [JsonProperty("AssessmentId")]
        public string AssessmentId { get; set; }

        [JsonProperty("Responses")]
        public List<Response> Responses { get; set; }
        public class Response
        {
            [JsonProperty("ResponseLineId")]
            public string ResponseLineId { get; set; }

            [JsonProperty("AssessmentLineId")]
            public string AssessmentLineId { get; set; }

            [JsonProperty("AssessmentLineOptionId")]
            public string AssessmentLineOptionId { get; set; }

            [Json<PERSON>roperty("AnswerText", NullValueHandling = NullValueHandling.Ignore)]
            public string AnswerText { get; set; }
        }
    }
   

}

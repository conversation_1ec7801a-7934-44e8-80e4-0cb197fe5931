﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class AccountModelRoot
    {
        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<AccountDetails> value { get; set; }
    }

    public class AccountDetails
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("accountid", NullValueHandling = NullValueHandling.Ignore)]
        public string accountid { get; set; }

        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string name { get; set; }
    }
}

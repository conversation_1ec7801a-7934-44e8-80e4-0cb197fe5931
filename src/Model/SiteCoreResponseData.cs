﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;

    public class SiteCoreResponseData
    {

        //[JsonProperty("AssessmentLineID", NullValueHandling = NullValueHandling.Ignore)]
        [JsonProperty("<EMAIL>")]
        public string survey_Provider_Name { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public Guid AssessmentLineID { get; set; }

        [JsonProperty("<EMAIL>")]
        public string AssessmentLineid
        {
            get => $"/aacn_assessment_lines({AssessmentLineID})";
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    AssessmentLineID = guid;
                }
                else
                {
                    throw new ArgumentException("Invalid assessment_Id format.");
                }
            }
        }
        [JsonProperty("QuestionID", NullValueHandling = NullValueHandling.Ignore)]
        public Guid QuestionID { get; set; }
        [JsonProperty("<EMAIL>")]
        public string Questionid
        {
            get => $"/aacn_questions({QuestionID})";
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    QuestionID = guid;
                }
                else
                {
                    throw new ArgumentException("Invalid assessment_Id format.");
                }
            }
        }

        [JsonProperty("AnswerID", NullValueHandling = NullValueHandling.Ignore)]
        public Guid AnswerID { get; set; }
        [JsonProperty("<EMAIL>")]
        public string Answerid
        {
            get => $"/aacn_answers({AnswerID})";
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    AnswerID = guid;
                }
                else
                {
                    throw new ArgumentException("Invalid assessment_Id format.");
                }
            }
        }

        [JsonProperty("QuestionText", NullValueHandling = NullValueHandling.Ignore)]
        public string QuestionText { get; set; }

        [JsonProperty("AnswerText", NullValueHandling = NullValueHandling.Ignore)]
        public string AnswerText { get; set; }

        [JsonProperty("AddUser", NullValueHandling = NullValueHandling.Ignore)]
        public string AddUser { get; set; }

        [JsonProperty("AddDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime AddDate { get; set; }

        [JsonProperty("ChangeUser", NullValueHandling = NullValueHandling.Ignore)]
        public string ChangeUser { get; set; }

        [JsonProperty("ChangeDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime ChangeDate { get; set; }

        [JsonProperty("IsDeleted", NullValueHandling = NullValueHandling.Ignore)]
        public bool IsDeleted { get; set; }
    }

    public class Root
    {
        [JsonProperty("SurveyResponses", NullValueHandling = NullValueHandling.Ignore)]
        public SurveyResponses SurveyResponses { get; set; }
    }

    public class SurveyResponses
    {
        [JsonProperty("SurveyProvider", NullValueHandling = NullValueHandling.Ignore)]
        public Guid SurveyProvider { get; set; }
        [JsonProperty("<EMAIL>")]
        public string SurveyProviderid
        {
            get => $"/aacn_survey_provides({SurveyProvider})";
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    SurveyProvider = guid;
                }
                else
                {
                    throw new ArgumentException("Invalid assessment_Id format.");
                }
            }
        }

        [JsonProperty("AssessmentID", NullValueHandling = NullValueHandling.Ignore)]
        public Guid AssessmentID { get; set; }
        [JsonProperty("<EMAIL>")] //need to check
        public string Assessmentid
        {
            get => $"/aacn_assessments({AssessmentID})";
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    AssessmentID = guid;
                }
                else
                {
                    throw new ArgumentException("Invalid assessment_Id format.");
                }
            }
        }
        [JsonProperty("OrganizationID", NullValueHandling = NullValueHandling.Ignore)]
        public Guid OrganizationID { get; set; }
        [JsonProperty("<EMAIL>")]//need to check
        public string Organizationid
        {
            get => $"/aacn_assessments({OrganizationID})";
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    OrganizationID = guid;
                }
                else
                {
                    throw new ArgumentException("Invalid assessment_Id format.");
                }
            }
        }

        [JsonProperty("UnitID", NullValueHandling = NullValueHandling.Ignore)]
        public string UnitID { get; set; }

        [JsonProperty("AddUser", NullValueHandling = NullValueHandling.Ignore)]
        public string AddUser { get; set; }

        [JsonProperty("AddDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime AddDate { get; set; }

        [JsonProperty("ChangeUser", NullValueHandling = NullValueHandling.Ignore)]
        public string ChangeUser { get; set; }

        [JsonProperty("ChangeDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime ChangeDate { get; set; }

        [JsonProperty("IsDeleted", NullValueHandling = NullValueHandling.Ignore)]
        public bool IsDeleted { get; set; }

        // [JsonProperty("ResponseLines", NullValueHandling = NullValueHandling.Ignore)]
        //public List<ResponseLine> ResponseLines { get; set; }
    }


    public class ResponseActivityDetail
    {
        public string Assessment_Review_ActivityNumber { get; set; }
    }

    public class SiteCoreAPIResponse
    {
        public int Responsesynced { get; set; }
        public int ReivewAcitivitySysnced { get; set; }
        public  List<sitecoreResponses> responseKeys { get; set; }
    }

    public class sitecoreResponses
    {
        public string ResponseKey { get; set; }
        public string ReviewAcitiyKey { get; set; }
    }
}
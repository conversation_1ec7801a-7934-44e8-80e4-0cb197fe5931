﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;


    public class NtiFormSessionResponseModel
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string ODataContext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<AssessmentValue> Value { get; set; }

        [JsonProperty("counter", NullValueHandling = NullValueHandling.Ignore)]
        public CounterModel Counter { get; set; }
    }

    public class AssessmentValue
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string ODataEtag { get; set; }

        [JsonProperty("aacn_assessmentid")]
        public string AssessmentId { get; set; }

        [JsonProperty("aacn_assessment_title")]
        public string AssessmentTitle { get; set; }

        [JsonProperty("aacn_assessment_description")]
        public string AssessmentDescription { get; set; }

        [JsonProperty("aacn_assessment_number")]
        public string AssessmentNumber { get; set; }

        [JsonProperty("_aacn_unit_module_value")]
        public string UnitModuleValue { get; set; }

        [JsonProperty("_aacn_survey_provider_value")]
        public string SurveyProviderValue { get; set; }

        [JsonProperty("aacn_passing_score")]
        public int PassingScore { get; set; }

        [JsonProperty("_aacn_year_value")]
        public string YearValue { get; set; }

        [JsonProperty("_aacn_reviewer_module_value")]
        public string ReviewerModuleValue { get; set; }

        [JsonProperty("aacn_assessment_line_assessment_header_aacn_as")]
        public List<AacnAssessmentLineAssessmentHeaderAacnAQ> AssessmentLines { get; set; }
    }

    public class AacnAssessmentLineAssessmentHeaderAacnAQ
    {
        [JsonProperty("aacn_assessment_line_type")]
        public int AssessmentLineType { get; set; }

        [JsonProperty("_aacn_assessment_line_reference_value")]
        public string AssessmentLineReferenceValue { get; set; }

        [JsonProperty("aacn_assessment_line_question_text")]
        public string AssessmentLineQuestionText { get; set; }

        [JsonProperty("aacn_assessment_line_number")]
        public string AssessmentLineNumber { get; set; }

        [JsonProperty("aacn_assessment_line_control")]
        public int AssessmentLineControl { get; set; }

        [JsonProperty("_aacn_assessment_line_question_value")]
        public string AssessmentLineQuestionValue { get; set; }

        [JsonProperty("aacn_tool_tip_description")]
        public string ToolTipDescription { get; set; }

        [JsonProperty("aacn_banner_description")]
        public string BannerDescription { get; set; }

        [JsonProperty("aacn_assessment_line_required")]
        public bool AssessmentLineRequired { get; set; }

        [JsonProperty("aacn_assessment_max_responses")]
        public string AssessmentMaxResponses { get; set; }

        [JsonProperty("aacn_assessment_line_orientation")]
        public int? AssessmentLineOrientation { get; set; }

        [JsonProperty("aacn_assessment_line_title")]
        public string AssessmentLineTitle { get; set; }

        [JsonProperty("aacn_assessment_line_order")]
        public string AssessmentLineOrder { get; set; }

        [JsonProperty("aacn_assessment_lineid")]
        public string AssessmentLineId { get; set; }

        [JsonProperty("_aacn_assessment_header_value")]
        public string AssessmentHeaderValue { get; set; }

        [JsonProperty("aacn_assessment_line_option_assessment_line_aa")]
        public List<AacnAssessmentLineOptionAssessmentLineAaOption> AssessmentLineOptions { get; set; }
    }

    public class AacnAssessmentLineOptionAssessmentLineAaOption
    {
        [JsonProperty("_aacn_assessment_line_question_value")]
        public string AssessmentLineQuestionValue { get; set; }

        [JsonProperty("aacn_assessment_line_question_option_text")]
        public string AssessmentLineQuestionOptionText { get; set; }

        [JsonProperty("aacn_assessment_line_optionid")]
        public string AssessmentLineOptionId { get; set; }

        [JsonProperty("aacn_assessment_line_option_score")]
        public string AssessmentLineOptionScore { get; set; }

        [JsonProperty("aacn_assessment_line_option_sort_order")]
        public int AssessmentLineOptionSortOrder { get; set; }

        [JsonProperty("_aacn_assessment_line_value")]
        public string AssessmentLineValue { get; set; }

        [JsonProperty("_aacn_assessment_line_question_option_value")]
        public string AssessmentLineQuestionOptionValue { get; set; }

        [JsonProperty("aacn_assessment_line_option_required")]
        public bool AssessmentLineOptionRequired { get; set; }

        [JsonProperty("aacn_assessment_line_option_number")]
        public string AssessmentLineOptionNumber { get; set; }

        [JsonProperty("_aacn_assessment_lineid_skip_to_value")]
        public string AssessmentLineIdSkipToValue { get; set; }

        [JsonProperty("aacn_assessment_line_question_text")]
        public string AssessmentLineQuestionText { get; set; }
    }



}

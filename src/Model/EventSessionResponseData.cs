﻿namespace AACN.API.Model
{
    using Newtonsoft.Json.Linq;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    public class EventSessionResponseData
    {
        [JsonProperty("@odata.context")]
        public string odatacontext { get; set; }
        public List<responseValue> value { get; set; }
    }

    public class responseValue
    {
        [JsonProperty("@odata.etag")]
        public string odataetag { get; set; }
        public string aacn_event_id { get; set; }
        public string aacn_eventid { get; set; }
        public string aacn_session_id { get; set; }
        public string aacn_sessionid { get; set; }
    }


    public class sessionResponseData

    {
        [JsonProperty("@odata.context")]
        public string odatacontext { get; set; }
        public List<sessionResponse> value { get; set; }
    }
    public class sessionResponse
    {
        [JsonProperty("@odata.etag")]
        public string odataetag { get; set; }
        public string aacn_sessionid { get; set; }
         
    }


}

﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AACN.API.Model
{

    public class SyncReviwerCommand
    {
        [JsonProperty("ReviewerData", NullValueHandling = NullValueHandling.Ignore)]
        public List<PostContactModel> ReviewerData { get; set; }
    }
    public class PostContactModel
    {

        [JsonProperty("contactid", NullValueHandling = NullValueHandling.Ignore)]
        public string RecordId { get; set; }

        [JsonProperty("firstname", NullValueHandling = NullValueHandling.Ignore)]
        public string FirstName { get; set; }

        [JsonProperty("lastname", NullValueHandling = NullValueHandling.Ignore)]
        public string LastName { get; set; }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        [JsonProperty("accountrolecode", NullValueHandling = NullValueHandling.Ignore)]
        public string AccountRoleCode { get; set; }

        [JsonProperty("aacn_contact_number", NullValueHandling = NullValueHandling.Ignore)]
        public string record_Number { get; set; }

        [JsonProperty("emailaddress1", NullValueHandling = NullValueHandling.Ignore)]
        public string emailId { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string postionId { get; set; }
        
        [JsonProperty("aacn_user_start_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? userStartDate { get; set; }
        
        [JsonProperty("aacn_user_end_date")]
        public DateTime? userEndDate { get; set; }
    }
}

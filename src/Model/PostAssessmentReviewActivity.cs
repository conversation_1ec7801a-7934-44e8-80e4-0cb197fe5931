﻿namespace AACN.API.Model
{
    using AACN.API.Model.OrganisationModel;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;

    public class PostAssessmentReviewActivityCommand
    {
        [JsonProperty("ReviewAcitvities", NullValueHandling = NullValueHandling.Ignore)]
        public List<PostAssessmentReviewActivity> ReviewAcitvities { get; set; }
    }
    public class PostAssessmentReviewActivity
    {
        [JsonProperty("aacn_assessment_review_activityid", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Review_ActivityId { get; set; }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string Reviewer_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string ReviewerId
        {
            get
            {
                if (!string.IsNullOrEmpty(Reviewer_Id))
                {
                    return $"/contacts({Reviewer_Id})";
                }
                return null;
            }

            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    Reviewer_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid Reviewer_Id format.");
                }
            }
        }


        [JsonProperty("aacn_request_status", NullValueHandling = NullValueHandling.Ignore)]
        public int request_Status_Value { get; set; }


        [JsonProperty("aacn_assessment_review_activity_number", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Review_Activity_Number { get; set; } 
        
        [JsonProperty("aacn_application_id", NullValueHandling = NullValueHandling.Ignore)]
        public long ApplicationId { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Year { get; set; }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string Organization_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string OrganizatioId
        {
            get
            {
                if (!string.IsNullOrEmpty(Organization_Id))
                {
                    return $"/accounts({Organization_Id})";
                }
                return null;
            }

            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    Organization_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid OrganizationId format.");
                }
            }
        }

        [Newtonsoft.Json.JsonIgnore]
        public string Unit_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string Unit
        {
            get
            {
                if (!string.IsNullOrEmpty(Unit_Id))
                {
                    return $"/accounts({Unit_Id})";
                }
                return null;
            }

            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    Unit_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid Unit_Id format.");
                }
            }
        }

        [JsonProperty("aacn_rejected_reason", NullValueHandling = NullValueHandling.Ignore)]
        public int? Rejection_Reason { get; set; }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string UserResponseId { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string userResponseId
        {
            get
            {
                if (!string.IsNullOrEmpty(UserResponseId))
                {
                    return $"/aacn_responses({UserResponseId})"; ;
                }
                return null;
            }

            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    UserResponseId = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid UserResponseId format.");
                }
            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string ReviewerResponseId { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string reviwerResponseId
        {
            get
            {
                if (!string.IsNullOrEmpty(ReviewerResponseId))
                {
                    return $"/aacn_responses({ReviewerResponseId})";
                }
                return null;
            }

            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    ReviewerResponseId = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid ReviewerResponseId format.");
                }
            }
        }


        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string Reviewer_Module_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string reviewerModule
        {
            get
            {
                if (!string.IsNullOrEmpty(Reviewer_Module_Id))
                {
                    return $"/aacn_assessments({Reviewer_Module_Id})";
                }
                return null;
            }

            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    Reviewer_Module_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid ReviewerModule format.");
                }
            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string Unit_Module_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string UnitModuleId
        {
            get
            {
                if (!string.IsNullOrEmpty(Unit_Module_Id))
                {
                    return $"/aacn_assessments({Unit_Module_Id})";
                }
                return null;
            }

            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    Unit_Module_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid Unit_Module format.");
                }
            }
        }
        private DateTime? _acceptedDate;
        private DateTime? _rejectedDate;
        private DateTime? _completedDate;
        private DateTime? _assignedDate;
        private DateTime? _inprogressDate;

        [JsonProperty("aacn_accepted_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? AcceptedDate
        {
            get => _acceptedDate;
            set => _acceptedDate = ValidateDateTime(value);
        }

        [JsonProperty("aacn_inprogress_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? InprogressDate
        {
            get => _inprogressDate;
            set => _inprogressDate = ValidateDateTime(value);
        }

        [JsonProperty("aacn_rejected_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? RejectedDate
        {
            get => _rejectedDate;
            set => _rejectedDate = ValidateDateTime(value);
        }

        [JsonProperty("aacn_completed_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? CompletedDate
        {
            get => _completedDate;
            set => _completedDate = ValidateDateTime(value);
        }

        [JsonProperty("aacn_assigned_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? AssignedDate
        {
            get => _assignedDate;
            set => _assignedDate = ValidateDateTime(value);
        }


        private DateTime? ValidateDateTime(DateTime? dateTime)
        {
            if (dateTime == null || dateTime == DateTime.MinValue)
            {
                return null;
            }
            return dateTime;
        }

    }


}

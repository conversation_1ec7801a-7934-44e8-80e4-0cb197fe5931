﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    public class tempSpeakerData
    {
        [JsonProperty("aacn_speaker_name")]
        public string speaker_Name { get; set; }

        [JsonProperty("statecode")]
        public int statecode { get; set; }

        [JsonProperty("statuscode")]
        public int statuscode { get; set; }

        [JsonProperty("_createdby_value")]
        public string createdby_Value { get; set; }

        [JsonProperty("_ownerid_value")]
        public string ownerid_Value { get; set; }

        [JsonProperty("modifiedon")]
        public DateTime modifiedon { get; set; }

        [JsonProperty("_aacn_speaker_value")]
        public object speaker_Value { get; set; }

        [JsonProperty("_owninguser_value")]
        public string owninguser_value { get; set; }

        [JsonProperty("_modifiedby_value")]
        public string modifiedBy_Value { get; set; }

        [JsonProperty("aacn_speaker_number")]
        public string speaker_Number { get; set; }

        [JsonProperty("createdon")]
        public DateTime createdOn { get; set; }

        [Json<PERSON>roperty("aacn_speakerid")]
        public string speakerId { get; set; }

        [JsonProperty("_aacn_session_value")]
        public object session_Value { get; set; }

        [JsonProperty("_createdonbehalfby_value")]
        public object createdonbehalfby_value { get; set; }

        [JsonProperty("_modifiedonbehalfby_value")]
        public object modifiedonbehalfby_value { get; set; }

        [JsonProperty("_owningteam_value")]
        public object owningteam_Value { get; set; }
        public bool property_Used { get; set; }

        [JsonProperty("aacn_speaker_sort_order", NullValueHandling = NullValueHandling.Ignore)]
        public int speaker_Sort_Order { get; set; }
        
        [JsonProperty("aacn_lastname", NullValueHandling = NullValueHandling.Ignore)]
        public int aacn_lastname { get; set; }

    }


    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class EventRootModel
    {
        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<eventData> value { get; set; }
    }

    public class eventData
    {
        [JsonProperty("aacn_event_id", NullValueHandling = NullValueHandling.Ignore)]
        public string event_Code { get; set; }

        [JsonProperty("aacn_eventid", NullValueHandling = NullValueHandling.Ignore)]
        public string event_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string parent_Event_Code { get; set; }
 

        [JsonProperty("_aacn_parent_event_value", NullValueHandling = NullValueHandling.Ignore)]
        public string parent_Event_Id { get; set; }
    }


}

﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class PositionDataModel
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<PostionData> postiionrecords { get; set; }
    }

    public class PostionData
    {
        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string name { get; set; }

        [JsonProperty("positionid", NullValueHandling = NullValueHandling.Ignore)]
        public string positionid { get; set; }
    }

}

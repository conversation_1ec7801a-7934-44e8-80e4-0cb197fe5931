﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class UserEmailValModel
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string OdataContext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnAssessmentValue> Value { get; set; }
    }

    public class AacnAssessmentValue
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string OdataEtag { get; set; }

        [JsonProperty("aacn_assessment_title", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnAssessmentTitle { get; set; }

        [JsonProperty("aacn_assessmentid", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnAssessmentId { get; set; }

        [JsonProperty("aacn_assessment_line_assessment_header_aacn_as", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnAssessmentLineAssessmentHeaderAacnALines> AacnAssessmentLineAssessmentHeaderAacnAs { get; set; }
    }

    public class AacnAssessmentLineAssessmentHeaderAacnALines
    {
        [JsonProperty("aacn_assessment_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnAssessmentLineId { get; set; }

        [JsonProperty("aacn_assessment_line_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnAssessmentLineQuestionText { get; set; }

        [JsonProperty("aacn_response_line_assesment_line_aacn_assessm", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnResponseLineAssesmentLineAacnAssessmResponseLines> AacnResponseLineAssesmentLineAacnAssessm { get; set; }
    }

    public class AacnResponseLineAssesmentLineAacnAssessmResponseLines
    {
        [JsonProperty("aacn_response_text", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnResponseText { get; set; }

        [JsonProperty("aacn_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnQuestionText { get; set; }

        [JsonProperty("aacn_response_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string AacnResponseLineId { get; set; }
    }

}

namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;

    public class BeaconRespnseData
    {
        [JsonProperty("aacn_responseid", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        public string ResponseKey { get; set; }

        [JsonProperty("aacn_response_number", NullValueHandling = NullValueHandling.Ignore)]
        public string response_Number { get; set; }
        
        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Year { get; set; }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string assessment_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        public string assessmentId
        {
            get
            {
                if (!string.IsNullOrEmpty(assessment_Id))
                {
                    return $"/aacn_assessments({assessment_Id})";
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    assessment_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid Assessment_ID format.");
                }
            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string Unit_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        public string UnitName
        {
            get
            {
                if (!string.IsNullOrEmpty(Unit_Id))
                {
                    return $"/accounts({Unit_Id})";
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    Unit_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid UnitId format.");
                }
            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization

        [JsonProperty("event_Id", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization

        public string event_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        public string eventId
        {
            get
            {
                if (!string.IsNullOrEmpty(event_Id))
                {
                    return $"/aacn_events({event_Id})";
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    event_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid event_Id format.");
                }
            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization

        [JsonProperty("member_Id", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization

        public string member_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        public string memberId
        {
            get
            {
                if (!string.IsNullOrEmpty(member_Id))
                {
                    return $"/contacts({member_Id})";
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    member_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid member_Id format.");
                }
            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        [JsonProperty("session_Id", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization

        public string session_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]  // Map to this property during serialization/deserialization
        public string sessionId
        {
            get
            {
                if (!string.IsNullOrEmpty(session_Id))
                {
                    return $"/aacn_sessions({session_Id})";
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    session_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid session_Id format.");
                }
            }
        }

        [JsonProperty("<EMAIL>")]
        public string survey_Provider_Name { get; set; }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization

        [JsonProperty("instanceId", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        public string instanceId { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        public string instance_Id
        {
            get
            {
                if (!string.IsNullOrEmpty(instanceId))
                {
                    return $"/contacts({instanceId})";
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    instanceId = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid instanceId format.");
                }
            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        [JsonProperty("Facility_Id", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        public string Facility_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        public string Facility
        {
            get
            {
                if (!string.IsNullOrEmpty(Facility_Id))
                {
                    return $"/accounts({Facility_Id})";
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    Facility_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid Facility_Id format.");
                }
            }
        }

        [JsonProperty("aacn_response_line_response_aacn_response", NullValueHandling = NullValueHandling.Ignore)]
        public List<BeaconResponseLines> response_Line { get; set; }

        [JsonProperty("response_Activity_Details", NullValueHandling = NullValueHandling.Ignore)]
        public ResponseActivityDetails response_Activity_Details { get; set; }
    }
    public class BeaconResponseLines
    {

        [JsonProperty("aacn_response_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseLineKey { get; set; }

        [JsonProperty("aacn_response_line_number", NullValueHandling = NullValueHandling.Ignore)]
        public string response_Line_Number { get; set; }

        [JsonProperty("aacn_response_line_text_area", NullValueHandling = NullValueHandling.Ignore)]
        public string response_Text { get; set; }

        [JsonProperty("aacn_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string question_Text { get; set; }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        [JsonProperty("assessment_Line_Option_Id", NullValueHandling = NullValueHandling.Ignore)]
        public string assessment_Line_Option_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string assessment_Line_Option
        {
            get
            {
                if (!string.IsNullOrEmpty(assessment_Line_Option_Id))
                {
                    return $"/aacn_assessment_line_options({assessment_Line_Option_Id})";
                }
                return null;
            }

            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    assessment_Line_Option_Id = guid.ToString();
                }

            }
        }
        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        [JsonProperty("answer_Id", NullValueHandling = NullValueHandling.Ignore)]
        public string answer_Id { get; set; }
        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string answerId
        {
            get
            {
                if (!string.IsNullOrEmpty(answer_Id))
                {
                    return $"/aacn_answers({answer_Id})";
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    answer_Id = guid.ToString();
                }

            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        [JsonProperty("question_Id", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization

        public string question_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string questionId
        {
            get
            {

                if (!string.IsNullOrEmpty(question_Id))
                {
                    return $"/aacn_questions({question_Id})"; ;
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    question_Id = guid.ToString();
                }

            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string assessment_Line_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string assessment_LineId
        {
            get
            {

                if (!string.IsNullOrEmpty(assessment_Line_Id))
                {
                    return $"/aacn_assessment_lines({assessment_Line_Id})"; ;
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    assessment_Line_Id = guid.ToString();
                }

            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        [JsonProperty("speakerId", NullValueHandling = NullValueHandling.Ignore)]
        public string speakerId { get; set; }


        [JsonProperty("aacn_response_text", NullValueHandling = NullValueHandling.Ignore)]
        public string AnswerText { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string speakerLink
        {
            get
            {
                if (!string.IsNullOrEmpty(speakerId))
                {
                    return $"/aacn_speakers({speakerId})";
                }
                return null;
            }

            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    assessment_Line_Option_Id = guid.ToString();
                }

            }
        }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        [JsonProperty("Response_Id", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization

        public string Response_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string ResponseId
        {
            get
            {

                if (!string.IsNullOrEmpty(Response_Id))
                {
                    return $"/aacn_responses({Response_Id})"; ;
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    Response_Id = guid.ToString();
                }

            }
        }

        [JsonProperty("statecode", NullValueHandling = NullValueHandling.Ignore)]
        public int statecode { get; set; }

    }
    public class ResponseActivityDetails
    {
        public string Assessment_Review_ActivityNumber { get; set; }
        public string Assessment_reviewId { get; set; }
    }

    public class BeaconAPIResponse
    {
        public Guid ResponseId { get; set; }
        public string Status { get; set; }
        public int StatusCode { get; set; }
        public bool AcvitiyReviewUpdated { get; set; }
        public string Errors { get; set; }
    }
}




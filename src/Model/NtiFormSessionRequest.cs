﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class NtiFormSessionRequest
    {

        [JsonProperty("aacn_last_name", NullValueHandling = NullValueHandling.Ignore)]
        public string LastName { get; set; }

        [JsonProperty("aacn_email_address", NullValueHandling = NullValueHandling.Ignore)]
        public string EmailAddress { get; set; }

        [JsonProperty("aacn_session_time", NullValueHandling = NullValueHandling.Ignore)]
        public string SessionTime { get; set; }

        [JsonProperty("aacn_first_name", NullValueHandling = NullValueHandling.Ignore)]
        public string FirstName { get; set; }

        [JsonProperty("aacn_session_id", NullValueHandling = NullValueHandling.Ignore)]
        public string SessionId { get; set; }

        [JsonProperty("aacn_session_date", NullValueHandling = NullValueHandling.Ignore)]
        public string SessionDate { get; set; }
        
        [JsonProperty("date", NullValueHandling = NullValueHandling.Ignore)]
        public string Date { get; set; }

    }

    public class NtiFormSessionResponse
    {
        [JsonProperty("Status", NullValueHandling = NullValueHandling.Ignore)]
        public int Status { get; set; }

        [JsonProperty("Error", NullValueHandling = NullValueHandling.Ignore)]
        public string Error { get; set; }
    }

    public class ResponseRequestModel
    {
        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string SurveyProvider { get; set; }
    }

    public class QuestionAllModel
    {
        public List<QuestionMaster> value { get; set; }
    }
    public class QuestionMaster
    {
        [JsonProperty("aacn_question", NullValueHandling = NullValueHandling.Ignore)]
        public string Question { get; set; }

        [JsonProperty("aacn_questionid", NullValueHandling = NullValueHandling.Ignore)]
        public string QuestionId { get; set; }
    }

    public class ResponseLineBatchCreateModel
    {
        [JsonProperty("aacn_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string QuestionText { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Question { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Response { get; set; }

        [JsonProperty("aacn_response_text", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseText { get; set; }
        
        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentLine { get; set; }
        
        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentLineOption { get; set; }
    }
}

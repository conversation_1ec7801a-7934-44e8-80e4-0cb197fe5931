﻿using AACN.API.Model.SiteCoreSyncModel;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace AACN.API.Model.OrganisationModel
{

    public class PostOrganizationCommand
    {
        [JsonProperty("OrganizationData", NullValueHandling = NullValueHandling.Ignore)]
        public List<PostOrganizationModel> OrganizationData { get; set; }
    }

    public class PostOrganizationModel
    {
        [JsonProperty("accountid", NullValueHandling = NullValueHandling.Ignore)]
        public string OrgnizationId { get; set; }

        [JsonProperty("accountnumber", NullValueHandling = NullValueHandling.Ignore)]
        public string record_Number { get; set; }

        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string OrganizationName { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string PostionId { get; set; }



    }
}

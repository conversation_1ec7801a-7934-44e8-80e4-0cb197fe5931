﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class BeaconResponseCounterModel
    {
        [JsonProperty("value")]
        public List<BeaconResponseCounter> beaconResponses { get; set; }
    }
    public class BeaconResponseCounter
    {
        [JsonProperty("aacn_assessment_review_activity_number")]
        public string Review_Activity_Number { get; set; }

        [JsonProperty("<EMAIL>")]
        public string Request_Status_Formatted_Value { get; set; }

        [JsonProperty("aacn_request_status")]
        public int? Request_Status { get; set; }

        [JsonProperty("<EMAIL>")]
        public string Reviwer_Name { get; set; }

        [JsonProperty("_aacn_reviewer_value")]
        public string Reviwer_Id { get; set; }

        [JsonProperty("aacn_assessment_review_activityid")]
        public string Review_Activity_Id { get; set; }

    }

    public class BeaconResponseByAction
    {
        public string EmailId { get; set; }
        public string Ready { get; set; }
        public string Assigned { get; set; }
        public string Accepted { get; set; }
        public string Rejected { get; set; }
        public string In_Progress { get; set; }
        public string Complete { get; set; }
        public string Submitted { get; set; }
        public string All { get; set; }
    }
}



﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace AACN.API.Model
{

    public class PostUnitCommand
    {
        [JsonProperty("UnitData", NullValueHandling = NullValueHandling.Ignore)]
        public List<PostUnitModel> UnitData { get; set; }
    }
    public class PostUnitModel
    {
        [JsonProperty("accountid", NullValueHandling = NullValueHandling.Ignore)]
        public Guid Unit_Id { get; set; }

        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string Name { get; set; }

        [JsonProperty("address1_stateorprovince", NullValueHandling = NullValueHandling.Ignore)]
        public string State { get; set; }

        [JsonProperty("address1_city", NullValueHandling = NullValueHandling.Ignore)]
        public string City { get; set; }

        [JsonProperty("accountnumber", NullValueHandling = NullValueHandling.Ignore)]
        public string record_Number { get; set; }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string Parentaccount_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)] // Map to this property during serialization/deserialization
        private string Parentaccountid
        {
            get
            {
                if (!string.IsNullOrEmpty(Parentaccount_Id))
                {
                    return $"/accounts({Parentaccount_Id})";
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    Parentaccount_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid Assessment_ID format.");
                }
            }
        }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string PostionId { get; set; }


    }
}

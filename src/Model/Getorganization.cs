﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;


    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class Getorganization
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<OrganizationValuesByunitId> value { get; set; }
    }

    public class OrganizationValuesByunitId
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("accountid", NullValueHandling = NullValueHandling.Ignore)]
        public string accountid { get; set; }

        [JsonProperty("_parentaccountid_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _parentaccountid_value { get; set; }
    }

   
}

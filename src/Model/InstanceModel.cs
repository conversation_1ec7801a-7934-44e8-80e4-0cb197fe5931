﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    public class PostInstanceData
    {
        [JsonProperty("aacn_instanceid", NullValueHandling = NullValueHandling.Ignore)]
        public string instanceId { get; set; }

        [JsonProperty("aacn_instance_id")]
        public string instance_Id { get; set; }

        [JsonProperty("aacn_start_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime start_Date { get; set; }

        [JsonProperty("aacn_end_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime end_Date { get; set; }

        [Newtonsoft.Json.JsonIgnore] // Ignore this property during serialization
        public string assessment_Code_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        private string assessment_Code
        {
            get
            {

                if (!string.IsNullOrEmpty(assessment_Code_Id))
                {
                    return $"/aacn_assessments({assessment_Code_Id})";
                }
                return null;
            }
            set
            {
                if (Guid.TryParse(value, out Guid guid))
                {
                    assessment_Code_Id = guid.ToString();
                }
                else
                {
                    throw new ArgumentException("Invalid assessment_Line_Question_Id format.");
                }
            }
        }

        [JsonProperty("aacn_user_id", NullValueHandling = NullValueHandling.Ignore)]
        public string UserId { get; set; }

        [JsonProperty("aacn_unit_id", NullValueHandling = NullValueHandling.Ignore)]
        public string UnitId { get; set; }

        [JsonProperty("aacn_status", NullValueHandling = NullValueHandling.Ignore)]
        public int status { get; set; }

    }


    public class InstanceResponseData
    {
        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<Instances> value { get; set; }
    }

    public class Instances
    {

        [JsonProperty("aacn_instance_id", NullValueHandling = NullValueHandling.Ignore)]
        public string instance_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Number { get; set; }


        [JsonProperty("_aacn_assessment_code_value", NullValueHandling = NullValueHandling.Ignore)]
        public string assessment_Code_Id { get; set; }

        [JsonProperty("aacn_start_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? startDate { get; set; }

        [JsonProperty("aacn_end_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? endDate { get; set; }

        [JsonProperty("aacn_instanceid", NullValueHandling = NullValueHandling.Ignore)]
        public string recordId { get; set; }

        [JsonProperty("submittedResponses")]
        public string submittedResponses { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string status { get; set; }
    }



}

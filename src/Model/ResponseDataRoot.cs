﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class ResponseLinesData
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("_aacn_answer_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_answer_value { get; set; }

        [JsonProperty("_aacn_assesment_line_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assesment_line_value { get; set; }

        [JsonProperty("_aacn_assessment_line_option_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_option_value { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_question_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_question_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_question_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_question_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_question_value { get; set; }

        [JsonProperty("aacn_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_question_text { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_response_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_response_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_response_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_response_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_response_value { get; set; }

        [JsonProperty("aacn_response_line_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_line_number { get; set; }

        [JsonProperty("aacn_response_line_text_area", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_line_text_area { get; set; }

        [JsonProperty("aacn_response_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_lineid { get; set; }

        [JsonProperty("aacn_response_text", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_text { get; set; }

        [JsonProperty("_aacn_speaker_value", NullValueHandling = NullValueHandling.Ignore)]
        public object _aacn_speaker_value { get; set; }

        [JsonProperty("statecode", NullValueHandling = NullValueHandling.Ignore)]
        public int LineStatus { get; set; }
    }

    public class ResponseDataRoot
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcount", NullValueHandling = NullValueHandling.Ignore)]
        public int MicrosoftDynamicsCRMtotalrecordcount { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcountlimitexceeded", NullValueHandling = NullValueHandling.Ignore)]
        public bool MicrosoftDynamicsCRMtotalrecordcountlimitexceeded { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.globalmetadataversion", NullValueHandling = NullValueHandling.Ignore)]
        public string MicrosoftDynamicsCRMglobalmetadataversion { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<responses> value { get; set; }
    }

    public class responses
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("_aacn_assessment_value", NullValueHandling = NullValueHandling.Ignore)]
        public object _aacn_assessment_value { get; set; }

        [JsonProperty("_aacn_event_value", NullValueHandling = NullValueHandling.Ignore)]
        public object _aacn_event_value { get; set; }

        [JsonProperty("_aacn_member_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_member_value { get; set; }

        [JsonProperty("aacn_response_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_response_number { get; set; }

        [JsonProperty("aacn_responseid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_responseid { get; set; }

        [JsonProperty("_aacn_session_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_session_value { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_session_valueFormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_survey_provider_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_survey_provider_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_survey_provider_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string instanceNumber { get; set; }

        [JsonProperty("_aacn_instance_id_value", NullValueHandling = NullValueHandling.Ignore)]
        public string instanceId { get; set; }

        [JsonProperty("createdon", NullValueHandling = NullValueHandling.Ignore)]
        public string createdon { get; set; }

        [JsonProperty("_aacn_survey_provider_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_survey_provider_value { get; set; }

        [JsonProperty("aacn_response_line_response_aacn_response", NullValueHandling = NullValueHandling.Ignore)]
        public List<ResponseLinesData> responseLinesData { get; set; }


    }


    public class AssessmentReviewActivityRootMain
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<AssessmentReviewActivityValues> value { get; set; }
    }

    public class AssessmentReviewActivityValues
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("aacn_assessment_review_activityid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_review_activityid { get; set; }

        [JsonProperty("aacn_request_status", NullValueHandling = NullValueHandling.Ignore)]
        public int requestStatusValue { get; set; }

    }

}

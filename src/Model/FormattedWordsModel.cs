﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    public class FormattedWordsModel
    {
        [JsonProperty("value")]
        public List<formattedWordsList> formattedWords { get; set; }

        public class formattedWordsList
        {
            [JsonProperty("@odata.etag")]
            public string odataetag { get; set; }
            public string aacn_special_word { get; set; }
            public string aacn_special_word_formatid { get; set; }
            public int aacn_format_type { get; set; }

            public string updatedWord { get; set; }
        }


    }
}

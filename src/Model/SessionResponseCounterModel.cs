﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class SessionResponseCounterModel
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string ODataContext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<SessionResponseValues> Value { get; set; }
    }

    public class AacnAssessmentLineAssessmentHeaderAacnASession
    {
        [JsonProperty("aacn_assessment_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentLineId { get; set; }

        [JsonProperty("aacn_assessment_line_question_text", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentLineQuestionText { get; set; }

        [JsonProperty("aacn_assessment_line_option_assessment_line_aa", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnAssessmentLineOptionAssessmentLineAaSession> AssessmentLineOptionAssessmentLineAa { get; set; }
    }

    public class AacnAssessmentLineOptionAssessmentLineAaSession
    {
        [JsonProperty("aacn_assessment_line_question_option_text", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentLineQuestionOptionText { get; set; }

        [JsonProperty("aacn_assessment_line_optionid", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentLineOptionId { get; set; }

        [JsonProperty("aacn_response_line_assessment_line_option_aacn", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnResponseLineAssessmentLineOptionAacn> ResponseLineAssessmentLineOptionAacn { get; set; }
    }

    public class AacnResponseLineAssessmentLineOptionAacn
    {
        [JsonProperty("aacn_response_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseLineId { get; set; }
    }

    public class SessionResponseValues
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string ODataEtag { get; set; }

        [JsonProperty("aacn_assessment_title", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentTitle { get; set; }

        [JsonProperty("aacn_passing_score", NullValueHandling = NullValueHandling.Ignore)]
        public int PassingScore { get; set; }

        [JsonProperty("aacn_assessmentid", NullValueHandling = NullValueHandling.Ignore)]
        public string AssessmentId { get; set; }

        [JsonProperty("aacn_assessment_line_assessment_header_aacn_as", NullValueHandling = NullValueHandling.Ignore)]
        public List<AacnAssessmentLineAssessmentHeaderAacnASession> AssessmentLineAssessmentHeaderAacnAs { get; set; }
    }


    public class CounterModel
    {
        [JsonProperty("maxresponse", NullValueHandling = NullValueHandling.Ignore)]
        public string MaxResponse { get; set; }

        [JsonProperty("dayquestiontextvalues", NullValueHandling = NullValueHandling.Ignore)]
        public List<DayQuestionTextValues> DayQuestionTextValues { get; set; }
    }
    public class DayQuestionTextValues
    {
        [JsonProperty("dayquestiontextid", NullValueHandling = NullValueHandling.Ignore)]
        public string DayQuestionTextId { get; set; }

        [JsonProperty("dayquestiontext", NullValueHandling = NullValueHandling.Ignore)]
        public string DayQuestionText { get; set; }

        [JsonProperty("sessiontimevalues", NullValueHandling = NullValueHandling.Ignore)]
        public List<SessionTimeValues> SessionTimeValues { get; set; }
    }
    public class SessionTimeValues
    {
        [JsonProperty("sessiontimeid", NullValueHandling = NullValueHandling.Ignore)]
        public string SessionTimeId { get; set; }

        [JsonProperty("sessiontime", NullValueHandling = NullValueHandling.Ignore)]
        public string SessionTime { get; set; }

        [JsonProperty("responsecount", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseCount { get; set; }
    }
}

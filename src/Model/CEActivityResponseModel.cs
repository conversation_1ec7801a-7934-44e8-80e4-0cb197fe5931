﻿namespace AACN.API.Model
{
    using Newtonsoft.Json.Linq;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;


    public class assessmentHeaderLineRecords
    {
        [JsonProperty("_aacn_assessment_header_value")]
        public string _aacn_assessment_header_value { get; set; }

        [JsonProperty("aacn_assessment_line_control")]
        public string aacn_assessment_line_control { get; set; }

        [JsonProperty("aacn_assessment_line_number")]
        public string aacn_assessment_line_number { get; set; }

        [JsonProperty("aacn_assessment_line_order")]
        public string assessment_Line_Order { get; set; }

        [JsonProperty("aacn_assessment_line_orientation")]
        public string aacn_assessment_line_orientation { get; set; }

        [JsonProperty("_aacn_assessment_line_question_value")]
        public object _aacn_assessment_line_question_value { get; set; }

        [JsonProperty("aacn_assessment_line_question_text")]
        public object aacn_assessment_line_question_text { get; set; }

        [JsonProperty("_aacn_assessment_line_reference_value")]
        public object _aacn_assessment_line_reference_value { get; set; }

        [JsonProperty("aacn_assessment_line_required")]
        public bool aacn_assessment_line_required { get; set; }

        [JsonProperty("aacn_assessment_line_title")]
        public string aacn_assessment_line_title { get; set; }

        [JsonProperty("aacn_assessment_line_type")]
        public string aacn_assessment_line_type { get; set; }

        [JsonProperty("aacn_assessment_lineid")]
        public string aacn_assessment_lineid { get; set; }

        [JsonProperty("aacn_banner_description")]
        public string aacn_banner_description { get; set; }

        [JsonProperty("aacn_assessment_max_responses")]
        public string aacn_assessment_max_responses { get; set; }

        [JsonProperty("_createdby_value")]
        public string _createdby_value { get; set; }

        [JsonProperty("aacn_tool_tip_description")]
        public string aacn_tool_tip_description { get; set; }

        [JsonProperty("createdon")]
        public DateTime createdon { get; set; }

        [JsonProperty("<EMAIL>")]
        public string status { get; set; }

        [JsonProperty("aacn_assessment_line_option_assessment_line_aa")]
        public List<assessmentLineOptionRecords> options { get; set; }

        [JsonProperty("aacn_assessment_line_assessment_line_reference")]
        public List<referenceLines> referenceLines { get; set; }
    }

    public class referenceLines
    {
        [JsonProperty("_aacn_assessment_header_value")]
        public object _aacn_assessment_header_value { get; set; }

        [JsonProperty("aacn_assessment_line_control")]
        public string aacn_assessment_line_control { get; set; }

        [JsonProperty("aacn_assessment_line_number")]
        public string aacn_assessment_line_number { get; set; }

        [JsonProperty("aacn_tool_tip_description")]
        public string aacn_tool_tip_description { get; set; }

        [JsonProperty("aacn_assessment_line_order")]
        public string assessment_Line_Order { get; set; }

        [JsonProperty("aacn_assessment_line_orientation")]
        public string aacn_assessment_line_orientation { get; set; }

        [JsonProperty("_aacn_assessment_line_question_value")]
        public string _aacn_assessment_line_question_value { get; set; }

        [JsonProperty("aacn_assessment_line_question_text")]
        public string aacn_assessment_line_question_text { get; set; }
        public string speakerId { get; set; }

        [JsonProperty("_aacn_assessment_line_reference_value")]
        public string _aacn_assessment_line_reference_value { get; set; }

        [JsonProperty("aacn_assessment_line_required")]
        public bool aacn_assessment_line_required { get; set; }

        [JsonProperty("aacn_assessment_line_title")]
        public string aacn_assessment_line_title { get; set; }

        [JsonProperty("aacn_assessment_line_type")]
        public string aacn_assessment_line_type { get; set; }

        [JsonProperty("aacn_assessment_lineid")]
        public string aacn_assessment_lineid { get; set; }

        [JsonProperty("aacn_banner_description")]
        public string aacn_banner_description { get; set; }

        [JsonProperty("aacn_assessment_max_responses")]
        public string aacn_assessment_max_responses { get; set; }

        [JsonProperty("_createdby_value")]
        public string _createdby_value { get; set; }

        [JsonProperty("createdon")]
        public DateTime createdon { get; set; }

        [JsonProperty("<EMAIL>")]
        public string status { get; set; }

        [JsonProperty("_createdonbehalfby_value")]
        public object _createdonbehalfby_value { get; set; }

        [JsonProperty("aacn_assessment_line_option_assessment_line_aa")]
        public List<assessmentLineOptionRecords> optionLines { get; set; }

        [JsonProperty("aacn_assessment_line_assessment_line_reference")]
        public List<referenceLines> childLines { get; set; }

        [JsonProperty("overriddencreatedon")]
        public object overriddencreatedon { get; set; }


    }

    public class assessmentLineOptionRecords
    {
        [JsonProperty("_aacn_assessment_line_value")]
        public string _aacn_assessment_line_value { get; set; }

        [JsonProperty("aacn_assessment_line_option_number")]
        public string aacn_assessment_line_option_number { get; set; }

        [JsonProperty("aacn_assessment_line_option_required")]
        public bool aacn_assessment_line_option_required { get; set; }

        [JsonProperty("aacn_assessment_line_option_score")]
        public string aacn_assessment_line_option_score { get; set; }

        [JsonProperty("aacn_assessment_line_option_sort_order")]
        public string aacn_assessment_line_option_sort_order { get; set; }

        [JsonProperty("aacn_assessment_line_optionid")]
        public string aacn_assessment_line_optionid { get; set; }

        [JsonProperty("_aacn_assessment_line_question_value")]
        public string _aacn_assessment_line_question_value { get; set; }

        [JsonProperty("_aacn_assessment_line_question_option_value")]
        public string _aacn_assessment_line_question_option_value { get; set; }

        [JsonProperty("aacn_assessment_line_question_option_text")]
        public string aacn_assessment_line_question_option_text { get; set; }

        [JsonProperty("aacn_assessment_line_question_text")]
        public string aacn_assessment_line_question_text { get; set; }

        [JsonProperty("_createdby_value")]
        public string _createdby_value { get; set; }

        [JsonProperty("createdon")]
        public DateTime createdon { get; set; }

        [JsonProperty("modifiedon")]
        public DateTime modifiedon { get; set; }

        [JsonProperty("<EMAIL>")]
        public string status { get; set; }

        [JsonProperty("_aacn_assessment_lineid_skip_to_value")]
        public string assessment_Lineid_Skip_to_Id { get; set; }
        public string type { get; set; }
    }
    public class CEActivityResponseModel
    {
        public List<Event_SessionResponse> responseData { get; set; }
        public contactRecords contactResponse { get; set; }
    }

    public class DynamicsJsonValue
    {
        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public dynamic jsonResponse { get; set; }
    }
    public class CEActivityResponseModel_Session
    {
        [JsonProperty("@odata.context")]
        public string odatacontext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<SessionResponse> sessionResponse { get; set; }

        [JsonProperty("eventData", NullValueHandling = NullValueHandling.Ignore)]
        public List<EventResponse> eventData { get; set; }
        public contactRecords contactResponse { get; set; }
    }

    public class CEActivityResponseModel_Event
    {
        [JsonProperty("@odata.context")]
        public string odatacontext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<EventResponse> eventData { get; set; }
        public contactRecords contactResponse { get; set; }
    }
    public class AssessmentRecords
    {
        [JsonProperty("@odata.etag")]
        public string odataetag { get; set; }

        [JsonProperty("aacn_assessment_description")]
        public string aacn_assessment_description { get; set; }

        [JsonProperty("aacn_assessment_number")]
        public string aacn_assessment_number { get; set; }

        [JsonProperty("aacn_assessment_title")]
        public string aacn_assessment_title { get; set; }

        [JsonProperty("aacn_assessmentid")]
        public string aacn_assessmentid { get; set; }

        [JsonProperty("aacn_passing_score", NullValueHandling = NullValueHandling.Ignore)]
        public int aacn_passing_score { get; set; }

        [JsonProperty("_aacn_survey_provider_value")]
        public string _aacn_survey_provider_value { get; set; }

        [JsonProperty("<EMAIL>")]
        public string survey_Provider_Name { get; set; }

        [JsonProperty("_createdby_value")]
        public string _createdby_value { get; set; }

        [JsonProperty("modifiedon")]
        public DateTime modifiedon { get; set; }

        [JsonProperty("overriddencreatedon")]
        public object overriddencreatedon { get; set; }

        [JsonProperty("<EMAIL>")]
        public string status { get; set; }

        [JsonProperty("aacn_assessment_line_assessment_header_aacn_as")]
        public List<assessmentHeaderLineRecords> AssessmentsLines { get; set; }
    }

    public class SessionResponse
    {
        [JsonProperty("_aacn_assessment_code_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_code_value { get; set; }

        [JsonProperty("aacn_end_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_end_date { get; set; }

        [JsonProperty("aacn_end_time", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_end_time { get; set; }

        [JsonProperty("_aacn_event_id_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_event_id_value { get; set; }

        [JsonProperty("aacn_session_description", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_session_description { get; set; }

        [JsonProperty("aacn_session_id", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_session_id { get; set; }

        [JsonProperty("aacn_session_name", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_session_name { get; set; }

        [JsonProperty("aacn_sessionid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_sessionid { get; set; }

        [JsonProperty("aacn_start_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_start_date { get; set; }

        [JsonProperty("aacn_start_time", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_start_time { get; set; }

        [JsonProperty("_aacn_survey_provider_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_survey_provider_value { get; set; }

        [JsonProperty("_createdby_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _createdby_value { get; set; }

        [JsonProperty("createdon", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime createdon { get; set; }

        [JsonProperty("_createdonbehalfby_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _createdonbehalfby_value { get; set; }

        [JsonProperty("statecode", NullValueHandling = NullValueHandling.Ignore)]
        public string statecode { get; set; }

        [JsonProperty("aacn_event_id", NullValueHandling = NullValueHandling.Ignore)]
        public AacnEventId aacn_event_id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string event_Id { get; set; }

        [JsonProperty("aacn_assessment_code", NullValueHandling = NullValueHandling.Ignore)]
        public AssessmentRecords aacn_assessment_code { get; set; }

        [JsonProperty("aacn_speaker_session_aacn_session", NullValueHandling = NullValueHandling.Ignore)]
        public List<SpeakerData> aacn_speaker_session_aacn_session { get; set; }



    }

    public class AacnEventId
    {
        [JsonProperty("_aacn_assessment_code_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_code_value { get; set; }

        [JsonProperty("aacn_end_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_end_date { get; set; }

        [JsonProperty("aacn_event_id", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_event_id { get; set; }

        [JsonProperty("aacn_event_name", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_event_name { get; set; }

        [JsonProperty("aacn_eventid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_eventid { get; set; }

        [JsonProperty("_aacn_parent_event_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_parent_event_value { get; set; }

        [JsonProperty("aacn_start_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime aacn_start_date { get; set; }

        [JsonProperty("_aacn_survey_provider_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_survey_provider_value { get; set; }

        [JsonProperty("_createdby_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _createdby_value { get; set; }

        [JsonProperty("createdon", NullValueHandling = NullValueHandling.Ignore)]
        public string createdon { get; set; }

        [JsonProperty("_createdonbehalfby_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _createdonbehalfby_value { get; set; }

        [JsonProperty("overriddencreatedon", NullValueHandling = NullValueHandling.Ignore)]
        public string overriddencreatedon { get; set; }

        [JsonProperty("statecode", NullValueHandling = NullValueHandling.Ignore)]
        public string statecode { get; set; }
    }


    public class contactRecords
    {

        [JsonProperty("address1_city", NullValueHandling = NullValueHandling.Ignore)]
        public string address1_city { get; set; }

        [JsonProperty("firstname", NullValueHandling = NullValueHandling.Ignore)]
        public string firstname { get; set; }

        [JsonProperty("lastname", NullValueHandling = NullValueHandling.Ignore)]
        public string lastname { get; set; }

        [JsonProperty("middlename", NullValueHandling = NullValueHandling.Ignore)]
        public string middlename { get; set; }

        [JsonProperty("mobilephone", NullValueHandling = NullValueHandling.Ignore)]
        public string mobilephone { get; set; }

        [JsonProperty("contactid", NullValueHandling = NullValueHandling.Ignore)]
        public string contactid { get; set; }
    }

    public class EventResponse
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("_aacn_assessment_code_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_code_value { get; set; }

        [JsonProperty("aacn_start_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_start_date { get; set; }

        [JsonProperty("aacn_end_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_end_date { get; set; }

        [JsonProperty("aacn_event_id", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_event_id { get; set; }

        [JsonProperty("_aacn_survey_provider_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_survey_provider_value { get; set; }

        [JsonProperty("statecode", NullValueHandling = NullValueHandling.Ignore)]
        public string statecode { get; set; }

        [JsonProperty("aacn_event_name", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_event_name { get; set; }

        [JsonProperty("aacn_eventid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_eventid { get; set; }

        [JsonProperty("_aacn_parent_event_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_parent_event_value { get; set; }

        [JsonProperty("_ownerid_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _ownerid_value { get; set; }

        [JsonProperty("_owningbusinessunit_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _owningbusinessunit_value { get; set; }

        [JsonProperty("statuscode", NullValueHandling = NullValueHandling.Ignore)]
        public string statuscode { get; set; }

        [JsonProperty("aacn_event_parent_event_aacn_event", NullValueHandling = NullValueHandling.Ignore)]
        public List<childEvents> childEvents { get; set; }

        [JsonProperty("aacn_assessment_code", NullValueHandling = NullValueHandling.Ignore)]
        public AssessmentRecords aacn_assessment_code { get; set; }


    }

    public class childEvents
    {
        public string _aacn_assessment_code_value { get; set; }
        public string aacn_end_date { get; set; }
        public string aacn_event_id { get; set; }
        public string aacn_event_name { get; set; }
        public string aacn_eventid { get; set; }
        public string _aacn_parent_event_value { get; set; }
        public string aacn_start_date { get; set; }
        public string _aacn_survey_provider_value { get; set; }
        public string _createdby_value { get; set; }
        public DateTime createdon { get; set; }
        public string _createdonbehalfby_value { get; set; }
        public string overriddencreatedon { get; set; }
        public int statecode { get; set; }
    }

    public class ContactResponseData
    {
        [JsonProperty("@odata.context")]
        public string odatacontext { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcount")]
        public int MicrosoftDynamicsCRMtotalrecordcount { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcountlimitexceeded")]
        public bool MicrosoftDynamicsCRMtotalrecordcountlimitexceeded { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.globalmetadataversion")]
        public string MicrosoftDynamicsCRMglobalmetadataversion { get; set; }
        public List<contactRecords> value { get; set; }
    }

    public class SpeakerData
    {
        [JsonProperty("_aacn_session_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_session_value { get; set; }

        [JsonProperty("_aacn_speaker_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_speaker_value { get; set; }

        [JsonProperty("aacn_speaker_name", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_speaker_name { get; set; }

        [JsonProperty("aacn_speaker_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_speaker_number { get; set; }

        [JsonProperty("aacn_speakerid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_speakerid { get; set; }

        [JsonProperty("statecode", NullValueHandling = NullValueHandling.Ignore)]
        public int statecode { get; set; }

        [JsonProperty("versionnumber", NullValueHandling = NullValueHandling.Ignore)]
        public int versionnumber { get; set; }

        [JsonProperty("aacn_speaker_sort_order", NullValueHandling = NullValueHandling.Ignore)]
        public int speaker_Sort_Order { get; set; }
    }

    public class Event_SessionResponse
    {
        public string Name { get; set; }
        public string RecordId { get; set; }
        public string Record_Code { get; set; }
        public string Record_Name { get; set; }
        public string RecordType { get; set; }
        public string event_code { get; set; }
        public string record_Description { get; set; }

        [JsonProperty("_aacn_assessment_code_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_code_value { get; set; }

        [JsonProperty("_aacn_survey_provider_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_survey_provider_value { get; set; }

        [JsonProperty("statecode", NullValueHandling = NullValueHandling.Ignore)]
        public string statecode { get; set; }

        [JsonProperty("aacn_assessment_code", NullValueHandling = NullValueHandling.Ignore)]
        public AssessmentRecords aacn_assessment_code { get; set; }

        [JsonProperty("aacn_start_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_start_date { get; set; }

        [JsonProperty("aacn_end_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_end_date { get; set; }

        [JsonProperty("statuscode", NullValueHandling = NullValueHandling.Ignore)]
        public string statuscode { get; set; }

        [JsonProperty("aacn_event_parent_event_aacn_event", NullValueHandling = NullValueHandling.Ignore)]
        public List<childEvents> childEvents { get; set; }

        [JsonProperty("aacn_speaker_session_aacn_session", NullValueHandling = NullValueHandling.Ignore)]
        public List<SpeakerData> aacn_speaker_session_aacn_session { get; set; }

        [JsonProperty("aacn_event_id", NullValueHandling = NullValueHandling.Ignore)]
        public AacnEventId aacn_event_id { get; set; }
    }



    public class InstanceDataModel
    {
        [JsonProperty("@odata.etag")]
        public string odataetag { get; set; }

        [JsonProperty("aacn_end_date")]
        public string aacn_end_date { get; set; }

        [JsonProperty("_aacn_assessment_code_value")]
        public string _aacn_assessment_code_value { get; set; }

        [JsonProperty("aacn_instanceid")]
        public string aacn_instanceid { get; set; }

        [JsonProperty("aacn_user_id")]
        public string aacn_user_id { get; set; }

        [JsonProperty("aacn_start_date")]
        public string aacn_start_date { get; set; }

        [JsonProperty("aacn_unit_id")]
        public string aacn_unit_id { get; set; }

        [JsonProperty("aacn_instance_id")]
        public string aacn_instance_id { get; set; }

        [JsonProperty("aacn_assessment_code", NullValueHandling = NullValueHandling.Ignore)]
        public AssessmentRecords aacn_assessment_code { get; set; }
    }


}

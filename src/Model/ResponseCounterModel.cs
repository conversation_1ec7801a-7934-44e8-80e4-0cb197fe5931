﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class ResponseCounterModel
    {
        [JsonProperty("@odata.context")]
        public string odatacontext { get; set; }
        public List<Value> value { get; set; }
    }
    public class Value
    {
        [JsonProperty("@odata.etag")]
        public string odataetag { get; set; }
        public string _aacn_assessment_value { get; set; }
        public string aacn_responseid { get; set; }
    }

    public class ResponseCounterData
    {

        public string startDate { get; set; }
        public string endDate { get; set; }
        public string submittedResponses { get; set; }
        


    }


}

﻿namespace AACN.API.Model
{
using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    public class NFRequestModel
    {
        [JsonProperty("ResponseId")]
        public string ResponseId { get; set; }

        [JsonProperty("CustomerKey")]
        public string CustomerKey { get; set; }

        [JsonProperty("SessionCode")]
        public string SessionCode { get; set; }

        [JsonProperty("ChildEventCode")]
        public string ChildEventCode { get; set; }

        [JsonProperty("VendorUserId")]
        public int VendorUserId { get; set; }

        [JsonProperty("VendorSessionId")]
        public int VendorSessionId { get; set; }

        [JsonProperty("AssessmentId")]
        public string AssessmentId { get; set; }

        [JsonProperty("Responses")]
        public List<Responses> responses { get; set; }

        public class Responses
        {
            [JsonProperty("ResponseLineId")]
            public string ResponseLineId { get; set; }

            [JsonProperty("ResponseId")]
            public string ResponseId { get; set; }

            [JsonProperty("AssessmentLineId")]
            public string AssessmentLineId { get; set; }

            [JsonProperty("AssessmentLineOptionId")]
            public string AssessmentLineOptionId { get; set; }

            [JsonProperty("AnswerText")]
            public string AnswerText { get; set; }
        }

    }
}

﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class ContactListModelByRole
    {
        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcount", NullValueHandling = NullValueHandling.Ignore)]
        public int TotalRecords { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<ContactList> contactList { get; set; }
    }
    public class ContactList
    {
        [JsonProperty("firstname", NullValueHandling = NullValueHandling.Ignore)]
        public string FirstName { get; set; }

        [JsonProperty("lastname", NullValueHandling = NullValueHandling.Ignore)]
        public string LastName { get; set; }

        [JsonProperty("aacn_contact_number", NullValueHandling = NullValueHandling.Ignore)]
        public string Contact_Number { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Role_DisplayValue { get; set; }

        [JsonProperty("_aacn_position_value", NullValueHandling = NullValueHandling.Ignore)]
        public string positionId { get; set; }

        [JsonProperty("contactid", NullValueHandling = NullValueHandling.Ignore)]
        public string contactId { get; set; }

        [JsonProperty("emailaddress1", NullValueHandling = NullValueHandling.Ignore)]
        public string EmailAddress { get; set; }

        [JsonProperty("aacn_user_start_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? StartDate { get; set; } 

        [JsonProperty("aacn_user_end_date", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? EndDate { get; set; }
    }


}

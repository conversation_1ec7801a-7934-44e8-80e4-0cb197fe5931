﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class YearModel
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<YearValue> value { get; set; }
    }

    public class YearValue
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("aacn_yearid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_yearid { get; set; }

        [JsonProperty("aacn_year", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_year { get; set; }

        [JsonProperty("aacn_name", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_name { get; set; }
    }
}

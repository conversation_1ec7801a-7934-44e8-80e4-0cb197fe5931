using System.Threading.Channels;

namespace AACN.API.Model
{
    using Microsoft.Extensions.FileSystemGlobbing.Internal;
    using Newtonsoft.Json;
    using System;

    using System.Collections.Generic;


    public class AssessmentReviewActivtyRoot
    {
        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<AssessmentReviews> assessmentReviews { get; set; }
        public BeaconResponseByAction status_Counter { get; set; }
        public List<AssessmentReviews> NoStatusChanges { get; set; }
    }

    public class AssessmentReviews
    {
        [JsonProperty("_aacn_assessment_value", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Id { get; set; }

        [JsonProperty("aacn_assessment_review_activity_number", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Review_Activity_Number { get; set; }

        [JsonProperty("aacn_application_id", NullValueHandling = NullValueHandling.Ignore)]
        public string ApplicationId { get; set; }

        [JsonProperty("aacn_assessment_review_activityid", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Review_ActivityId { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Organization_Name { get; set; }

        [JsonProperty("_aacn_organization_value", NullValueHandling = NullValueHandling.Ignore)]
        public string Organization_Id { get; set; }

        [JsonProperty("aacn_rejected_reason", NullValueHandling = NullValueHandling.Ignore)]
        public int? Rejected_Reason_EnumValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Request_Staus_Display_Value { get; set; }

        [JsonProperty("aacn_request_status", NullValueHandling = NullValueHandling.Ignore)]
        public int? Request_Status_EnumValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Reviewer_Name { get; set; }

        [JsonProperty("_aacn_reviewer_value", NullValueHandling = NullValueHandling.Ignore)]
        public string Reviwer_Id { get; set; }

        [JsonProperty("_aacn_unit_value", NullValueHandling = NullValueHandling.Ignore)]
        public string Unit_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Unit_Name { get; set; }

        [JsonProperty("aacn_unit_response", NullValueHandling = NullValueHandling.Ignore)]
        public string Unit_Response { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Name { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Reject_Reason_DisplayValue { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string Reviewer_Response_DisplayValue { get; set; }

        [JsonProperty("_aacn_reviewers_response_value", NullValueHandling = NullValueHandling.Ignore)]
        public string Reviewer_Response_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string User_Response_DisplayValue { get; set; }

        [JsonProperty("_aacn_user_response_value", NullValueHandling = NullValueHandling.Ignore)]
        public string User_Response_Id { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string createdonODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("createdon", NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTime createdon { get; set; }

        [JsonProperty("modifiedon", NullValueHandling = NullValueHandling.Ignore)]
        public System.DateTime modifiedon { get; set; }

        [JsonProperty("aacn_assessment", NullValueHandling = NullValueHandling.Ignore)]
        public AssessmentDetails assessmentDetails { get; set; }

        [JsonProperty("aacn_accepted_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_accepted_date { get; set; }

        [JsonProperty("aacn_rejected_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_rejected_date { get; set; }
        [JsonProperty("aacn_assigned_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assigned_date { get; set; }
        [JsonProperty("aacn_inprogress_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_inprogress_date { get; set; }

        [JsonProperty("aacn_completed_date", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_completed_date { get; set; }

        [JsonProperty("aacn_unit_module", NullValueHandling = NullValueHandling.Ignore)]
        public AssessmentDetails aacn_unit_module { get; set; }

        [JsonProperty("aacn_reviewer_module", NullValueHandling = NullValueHandling.Ignore)]
        public AssessmentDetails aacn_reviewer_module { get; set; }

        [JsonProperty("aacn_Unit", NullValueHandling = NullValueHandling.Ignore)]
        public AacnUnit aacn_Unit { get; set; }

        [JsonProperty("aacn_reviewer", NullValueHandling = NullValueHandling.Ignore)]
        public AacnReviewer aacn_reviewer { get; set; }


        [JsonProperty("aacn_module_completion_status", NullValueHandling = NullValueHandling.Ignore)]
        public bool Module_Completion_Status { get; set; }
    }

    public class AacnUnit
    {
        [JsonProperty("accountid", NullValueHandling = NullValueHandling.Ignore)]
        public string accountid { get; set; }

        [JsonProperty("accountnumber", NullValueHandling = NullValueHandling.Ignore)]
        public string accountnumber { get; set; }

        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string name { get; set; }

        [JsonProperty("address1_city", NullValueHandling = NullValueHandling.Ignore)]
        public string City { get; set; }

        [JsonProperty("address1_stateorprovince", NullValueHandling = NullValueHandling.Ignore)]
        public string State { get; set; }
    }
    public class AssessmentDetails
    {
        [JsonProperty("aacn_assessment_description", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Description { get; set; }

        [JsonProperty("aacn_assessment_number", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Number { get; set; }

        [JsonProperty("aacn_assessment_title", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Title { get; set; }

        [JsonProperty("aacn_assessmentid", NullValueHandling = NullValueHandling.Ignore)]
        public string Assessment_Id { get; set; }
    }
    public class AacnReviewer
    {
        [JsonProperty("aacn_contact_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_contact_number { get; set; }

        [JsonProperty("contactid", NullValueHandling = NullValueHandling.Ignore)]
        public string contactid { get; set; }

        [JsonProperty("firstname", NullValueHandling = NullValueHandling.Ignore)]
        public string firstname { get; set; }

        [JsonProperty("lastname", NullValueHandling = NullValueHandling.Ignore)]
        public string lastname { get; set; }
    }
}

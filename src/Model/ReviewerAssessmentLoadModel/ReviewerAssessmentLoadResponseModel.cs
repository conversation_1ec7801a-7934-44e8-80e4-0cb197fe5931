﻿namespace AACN.API.Model.ReviewerAssessmentLoadModel
{
    using Newtonsoft.Json;
    using System.Collections.Generic;
    using System;

    public class ReviewerAssessmentLoadResponseModel
    {
        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcount", NullValueHandling = NullValueHandling.Ignore)]
        public int? MicrosoftDynamicsCRMtotalrecordcount { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcountlimitexceeded", NullValueHandling = NullValueHandling.Ignore)]
        public bool? MicrosoftDynamicsCRMtotalrecordcountlimitexceeded { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.globalmetadataversion", NullValueHandling = NullValueHandling.Ignore)]
        public string MicrosoftDynamicsCRMglobalmetadataversion { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<Value> assessmentReviewActivity { get; set; }
        public string ErrorValue { get; set; } = string.Empty;
        public class AacnUserResponse
        {
            [JsonProperty("aacn_aacn_assigned_date", NullValueHandling = NullValueHandling.Ignore)]
            public object aacn_aacn_assigned_date { get; set; }

            [JsonProperty("_aacn_facility_name_value", NullValueHandling = NullValueHandling.Ignore)]
            public object _aacn_facility_name_value { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_instance_id_valueODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("_aacn_instance_id_value", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_instance_id_value { get; set; }

            [JsonProperty("_aacn_member_value", NullValueHandling = NullValueHandling.Ignore)]
            public object _aacn_member_value { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_nf_minipeak_response_statusODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("aacn_nf_minipeak_response_status", NullValueHandling = NullValueHandling.Ignore)]
            public bool? aacn_nf_minipeak_response_status { get; set; }

            [JsonProperty("aacn_response_message", NullValueHandling = NullValueHandling.Ignore)]
            public object aacn_response_message { get; set; }

            [JsonProperty("aacn_response_number", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_response_number { get; set; }

            [JsonProperty("aacn_responseid", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_responseid { get; set; }

            [JsonProperty("_aacn_unit_name_value", NullValueHandling = NullValueHandling.Ignore)]
            public object _aacn_unit_name_value { get; set; }

            [JsonProperty("aacn_member", NullValueHandling = NullValueHandling.Ignore)]
            public PointOfContactData POC_Data { get; set; }

            [JsonProperty("aacn_response_line_response_aacn_response", NullValueHandling = NullValueHandling.Ignore)]
            public List<AacnResponseLineResponseAacnResponse> ResponseLines { get; set; }

        }

        public class PointOfContactData
        {
            [JsonProperty("aacn_contact_number", NullValueHandling = NullValueHandling.Ignore)]
            public string PocCustomerId { get; set; }

            [JsonProperty("contactid", NullValueHandling = NullValueHandling.Ignore)]
            public string PocCustomerKey { get; set; }

            [JsonProperty("firstname", NullValueHandling = NullValueHandling.Ignore)]
            public string PocFirstName { get; set; }

            [JsonProperty("lastname", NullValueHandling = NullValueHandling.Ignore)]
            public string PocLastName { get; set; }


        }

        public class Value
        {

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_assessment_valueODataCommunityDisplayV1FormattedValue { get; set; }


            [JsonProperty("_aacn_assessment_value", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_assessment_value { get; set; }

            [JsonProperty("aacn_assessment_review_activity_number", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_assessment_review_activity_number { get; set; }

            [JsonProperty("aacn_assessment_review_activityid", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_assessment_review_activityid { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_organization_valueODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("_aacn_organization_value", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_organization_value { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_request_statusODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("aacn_request_status", NullValueHandling = NullValueHandling.Ignore)]
            public int? aacn_request_status { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_response_statusODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("aacn_response_status", NullValueHandling = NullValueHandling.Ignore)]
            public int? aacn_response_status { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_reviewer_Name { get; set; }

            [JsonProperty("_aacn_reviewer_value", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_reviewer_value { get; set; }

            [JsonProperty("_aacn_reviewers_response_value", NullValueHandling = NullValueHandling.Ignore)]
            public object _aacn_reviewers_response_value { get; set; }

            [JsonProperty("aacn_unit", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_unit { get; set; }

            [JsonProperty("aacn_assigned_date", NullValueHandling = NullValueHandling.Ignore)]
            public string Assigned_Date { get; set; }

            [JsonProperty("aacn_unit_response", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_unit_response { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_user_response_valueODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("_aacn_user_response_value", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_user_response_value { get; set; }

            [JsonProperty("aacn_reviewer_module", NullValueHandling = NullValueHandling.Ignore)]
            public AssessmentdataRecords Reviewer_Module { get; set; }

            [JsonProperty("aacn_unit_module", NullValueHandling = NullValueHandling.Ignore)]
            public AssessmentdataRecords unit_Module { get; set; }

            [JsonProperty("aacn_user_response", NullValueHandling = NullValueHandling.Ignore)]
            public AacnUserResponse UserResponses { get; set; }

            [JsonProperty("aacn_reviewers_response", NullValueHandling = NullValueHandling.Ignore)]
            public AacnUserResponse reviewerReponses { get; set; }
        }

        public class HandleResponses
        {
            public List<AssessmentdataRecords> assessmentdataRecords { get; set; }
        }
        public class AssessmentdataRecords
        {
            [JsonProperty("aacn_assessment_description")]
            public string aacn_assessment_description { get; set; }

            [JsonProperty("aacn_assessment_number")]
            public string aacn_assessment_number { get; set; }

            [JsonProperty("aacn_assessment_title")]
            public string aacn_assessment_title { get; set; }

            [JsonProperty("aacn_assessmentid")]
            public string aacn_assessmentid { get; set; }

            [JsonProperty("aacn_passing_score", NullValueHandling = NullValueHandling.Ignore)]
            public int aacn_passing_score { get; set; }

            [JsonProperty("_aacn_survey_provider_value")]
            public string _aacn_survey_provider_value { get; set; }

            [JsonProperty("<EMAIL>")]
            public string survey_Provider_Name { get; set; }

            [JsonProperty("_createdby_value")]
            public string _createdby_value { get; set; }

            [JsonProperty("modifiedon")]
            public DateTime modifiedon { get; set; }

            [JsonProperty("overriddencreatedon")]
            public object overriddencreatedon { get; set; }

            [JsonProperty("<EMAIL>")]
            public string status { get; set; }

            [JsonProperty("aacn_assessment_line_assessment_header_aacn_as")]
            public List<assessmentHeaderLineRecords> AssessmentsLines { get; set; }
        }
    }
}

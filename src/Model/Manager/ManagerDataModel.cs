﻿namespace AACN.API.Model.Manager
{
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using static AACN.API.Model.ReviewerAssessmentLoadModel.ReviewerAssessmentLoadResponseModel;
    public class ManagerDataModel
    {
        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcount", NullValueHandling = NullValueHandling.Ignore)]
        public int? MicrosoftDynamicsCRMtotalrecordcount { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcountlimitexceeded", NullValueHandling = NullValueHandling.Ignore)]
        public bool? MicrosoftDynamicsCRMtotalrecordcountlimitexceeded { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.globalmetadataversion", NullValueHandling = NullValueHandling.Ignore)]
        public string MicrosoftDynamicsCRMglobalmetadataversion { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<Acvitydata> assessmentReviewActivity { get; set; }
        public string ErrorValue { get; set; } = string.Empty;

        public class Acvitydata
        {
            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_assessment_valueODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("_aacn_assessment_value", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_assessment_value { get; set; }

            [JsonProperty("aacn_assessment_review_activity_number", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_assessment_review_activity_number { get; set; }

            [JsonProperty("aacn_assessment_review_activityid", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_assessment_review_activityid { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_organization_valueODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("_aacn_organization_value", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_organization_value { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_request_statusODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("aacn_request_status", NullValueHandling = NullValueHandling.Ignore)]
            public int? aacn_request_status { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_response_statusODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("aacn_response_status", NullValueHandling = NullValueHandling.Ignore)]
            public int? aacn_response_status { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_reviewer_Name { get; set; }

            [JsonProperty("_aacn_reviewer_value", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_reviewer_value { get; set; }

            [JsonProperty("_aacn_reviewers_response_value", NullValueHandling = NullValueHandling.Ignore)]
            public object _aacn_reviewers_response_value { get; set; }

            [JsonProperty("aacn_assigned_date", NullValueHandling = NullValueHandling.Ignore)]
            public string Assigned_Date { get; set; }

            [JsonProperty("aacn_unit_response", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_unit_response { get; set; }

            [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_user_response_valueODataCommunityDisplayV1FormattedValue { get; set; }

            [JsonProperty("_aacn_user_response_value", NullValueHandling = NullValueHandling.Ignore)]
            public string _aacn_user_response_value { get; set; }

            [JsonProperty("aacn_reviewer_module", NullValueHandling = NullValueHandling.Ignore)]
            public AssessmentdataRecords Reviewer_Module { get; set; }

            [JsonProperty("aacn_unit_module", NullValueHandling = NullValueHandling.Ignore)]
            public AssessmentdataRecords unit_Module { get; set; }

            [JsonProperty("aacn_user_response", NullValueHandling = NullValueHandling.Ignore)]
            public AacnUserResponse UserResponses { get; set; }

            [JsonProperty("aacn_reviewers_response", NullValueHandling = NullValueHandling.Ignore)]
            public AacnUserResponse reviewerReponses { get; set; }

            [JsonProperty("AssignedModules", NullValueHandling = NullValueHandling.Ignore)]
            public AssignedModuleDetails assignedModules { get; set; }

            [JsonProperty("aacn_Unit", NullValueHandling = NullValueHandling.Ignore)]
            public UnitData aacn_Unit { get; set; }

            [JsonProperty("aacn_reviewer", NullValueHandling = NullValueHandling.Ignore)]
            public ReviewerData aacn_reviewer { get; set; }
        }

        public void SortReviewAcitiyData(List<ReviewAcitiyData> reviewAcitiyData)
        {
            reviewAcitiyData.Sort(new ReviewAcitiyDataComparer());
        }
        public class UnitData
        {
            [JsonProperty("accountid", NullValueHandling = NullValueHandling.Ignore)]
            public string accountid { get; set; }

            [JsonProperty("accountnumber", NullValueHandling = NullValueHandling.Ignore)]
            public string accountnumber { get; set; }

            [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
            public string name { get; set; }
        }

        public class ReviewerData
        {
            [JsonProperty("aacn_contact_number", NullValueHandling = NullValueHandling.Ignore)]
            public string aacn_contact_number { get; set; }

            [JsonProperty("contactid", NullValueHandling = NullValueHandling.Ignore)]
            public string contactid { get; set; }

            [JsonProperty("firstname", NullValueHandling = NullValueHandling.Ignore)]
            public string firstname { get; set; }

            [JsonProperty("lastname", NullValueHandling = NullValueHandling.Ignore)]
            public string lastname { get; set; }
        }
    }
    public class AacnReviewerModule
    {
        [JsonProperty("aacn_assessment_description", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_description { get; set; }

        [JsonProperty("aacn_assessment_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_number { get; set; }

        [JsonProperty("aacn_assessment_title", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_title { get; set; }

        [JsonProperty("aacn_assessmentid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessmentid { get; set; }
    }

    public class ReviewAcitiyDataModel
    {
        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<ReviewAcitiyData> reviewAcitiyData { get; set; }
    }

    public class ReviewAcitiyData
    {
        [JsonProperty("aacn_assessment_review_activityid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_review_activityid { get; set; }

        [JsonProperty("aacn_request_status", NullValueHandling = NullValueHandling.Ignore)]
        public int aacn_request_status { get; set; }

        [JsonProperty("_aacn_reviewer_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_reviewer_value { get; set; }

        [JsonProperty("_aacn_reviewer_module_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_reviewer_module_value { get; set; }

        [JsonProperty("aacn_assessment_review_activity_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_review_activity_number { get; set; }

        [JsonProperty("_aacn_unit_module_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_unit_module_value { get; set; }

        [JsonProperty("_aacn_user_response_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_user_response_value { get; set; }

        [JsonProperty("aacn_reviewer_module", NullValueHandling = NullValueHandling.Ignore)]
        public AacnReviewerModule aacn_reviewer_module { get; set; }

        [JsonProperty("aacn_Unit", NullValueHandling = NullValueHandling.Ignore)]
        public AacnUnit aacn_Unit { get; set; }

        [JsonProperty("aacn_reviewer", NullValueHandling = NullValueHandling.Ignore)]
        public AacnReviewer aacn_reviewer { get; set; }
    }

    public class AacnUnit
    {
        [JsonProperty("accountid", NullValueHandling = NullValueHandling.Ignore)]
        public string accountid { get; set; }

        [JsonProperty("accountnumber", NullValueHandling = NullValueHandling.Ignore)]
        public string accountnumber { get; set; }

        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string name { get; set; }
    }

    public class AacnReviewer
    {
        [JsonProperty("aacn_contact_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_contact_number { get; set; }

        [JsonProperty("contactid", NullValueHandling = NullValueHandling.Ignore)]
        public string contactid { get; set; }

        [JsonProperty("firstname", NullValueHandling = NullValueHandling.Ignore)]
        public string firstname { get; set; }

        [JsonProperty("lastname", NullValueHandling = NullValueHandling.Ignore)]
        public string lastname { get; set; }
    }

    public class AssignedModuleDetails
    {
        public int assignedModuleCounter { get; set; }

    }
}
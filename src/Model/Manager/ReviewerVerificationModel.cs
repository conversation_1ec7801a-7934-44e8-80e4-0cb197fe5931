﻿namespace AACN.API.Model.Manager
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class ReviewerVerificationModel
    {

        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcount", NullValueHandling = NullValueHandling.Ignore)]
        public int MicrosoftDynamicsCRMtotalrecordcount { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.totalrecordcountlimitexceeded", NullValueHandling = NullValueHandling.Ignore)]
        public bool MicrosoftDynamicsCRMtotalrecordcountlimitexceeded { get; set; }

        [JsonProperty("@Microsoft.Dynamics.CRM.globalmetadataversion", NullValueHandling = NullValueHandling.Ignore)]
        public string MicrosoftDynamicsCRMglobalmetadataversion { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<ReviewerVerificationModelData> value { get; set; }
    }
    public class ReviewerVerificationModelData
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("_aacn_assessment_value", NullValueHandling = NullValueHandling.Ignore)]
        public object _aacn_assessment_value { get; set; }

        [JsonProperty("aacn_assessment_review_activity_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_review_activity_number { get; set; }

        [JsonProperty("aacn_assessment_review_activityid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_review_activityid { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_request_statusODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("aacn_request_status", NullValueHandling = NullValueHandling.Ignore)]
        public int aacn_request_status { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_reviewer_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("_aacn_reviewer_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_reviewer_value { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string ModuleName { get; set; }

        [JsonProperty("_aacn_reviewer_module_value", NullValueHandling = NullValueHandling.Ignore)]
        public string Reviewer_ModuleId { get; set; }
    }

    public class UnitVerificationResponse
    {
        public bool IsSuccess { get; set; } = false;
        public int NotCompletedModules { get; set; }
        public int CompletedModules { get; set; }
        public string CompletedModulesName { get; set; }
        public string NotCompletedModulesName { get; set; }

    }
}

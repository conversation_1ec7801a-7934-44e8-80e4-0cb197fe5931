﻿namespace AACN.API.Model.Manager
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class UpdateResponseModel
    {
        [JsonProperty("Update_Response", NullValueHandling = NullValueHandling.Ignore)]
        public List<UpdateResponse> Update_Response { get; set; }
    }

    public class UpdateResponse
    {
        [JsonProperty("responseId", NullValueHandling = NullValueHandling.Ignore)]
        public string responseId { get; set; }

        [JsonProperty("responselineId", NullValueHandling = NullValueHandling.Ignore)]
        public string responselineId { get; set; }

        [JsonProperty("questionId", NullValueHandling = NullValueHandling.Ignore)]
        public string questionId { get; set; }

        [JsonProperty("answerdId", NullValueHandling = NullValueHandling.Ignore)]
        public string answerdId { get; set; }

        [JsonProperty("questionText", NullValueHandling = NullValueHandling.Ignore)]
        public string questionText { get; set; }

        [JsonProperty("answerText", NullValueHandling = NullValueHandling.Ignore)]
        public string answerText { get; set; }

        [JsonProperty("assessmentOptionId", NullValueHandling = NullValueHandling.Ignore)]
        public string assessmentOptionId { get; set; }

        [JsonProperty("assessemntLineID", NullValueHandling = NullValueHandling.Ignore)]
        public string assessemntLineID { get; set; }

        [JsonProperty("ResponseTextArea", NullValueHandling = NullValueHandling.Ignore)]
        public string ResponseTextArea { get; set; }
    }

    public class UpdateResponseModelResponse
    {
        [JsonProperty("value")]
        public List<LinesData> _lineData { get; set; }
    }

    public class LinesData
    {
        [JsonProperty("@odata.etag")]
        public string odataetag { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_answer_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_answer_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_answer_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_answer_value")]
        public string _aacn_answer_value { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_assesment_line_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_assesment_line_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_assesment_line_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_assesment_line_value")]
        public string _aacn_assesment_line_value { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_assessment_line_option_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_assessment_line_option_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_assessment_line_option_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_assessment_line_option_value")]
        public string _aacn_assessment_line_option_value { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_question_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_question_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_question_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_question_value")]
        public string _aacn_question_value { get; set; }

        [JsonProperty("aacn_question_text")]
        public string aacn_question_text { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_response_valueODataCommunityDisplayV1FormattedValue { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_response_valueMicrosoftDynamicsCRMassociatednavigationproperty { get; set; }

        [JsonProperty("<EMAIL>")]
        public string _aacn_response_valueMicrosoftDynamicsCRMlookuplogicalname { get; set; }

        [JsonProperty("_aacn_response_value")]
        public string _aacn_response_value { get; set; }

        [JsonProperty("aacn_response_line_text_area")]
        public object aacn_response_line_text_area { get; set; }

        [JsonProperty("aacn_response_lineid")]
        public string aacn_response_lineid { get; set; }

        [JsonProperty("aacn_response_text")]
        public string aacn_response_text { get; set; }
    }


    public class UpdateOperationModel
    {
        public string HeaderKey { get; set; }
        public List<UpdateResponse> Response_Lines { get; set; }

    }

    public class ResponseSubmitResponse
    {
        public bool SyncStatus { get; set; } = false;
        public int UpdatedLines { get; set; }
        public int CreatedLines { get; set; }
        public string Error { get; set; }

    }

}

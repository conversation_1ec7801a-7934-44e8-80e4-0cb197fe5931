﻿namespace AACN.API.Model.Manager
{
    using Newtonsoft.Json;
    using System.Collections.Generic;

    public class GroupNameModel
    {
        [JsonProperty("@odata.context", NullValueHandling = NullValueHandling.Ignore)]
        public string odatacontext { get; set; }

        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<GroupData> groupData { get; set; }
    }
    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class ReferenceLine
    {
        [JsonProperty("aacn_assessment_line_title", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_title { get; set; }

        [JsonProperty("aacn_tool_tip_description", NullValueHandling = NullValueHandling.Ignore)]
        public string tool_tipValue { get; set; }

        [JsonProperty("aacn_assessment_line_type", NullValueHandling = NullValueHandling.Ignore)]
        public int aacn_assessment_line_type { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string LineTypeName { get; set; }

        [JsonProperty("aacn_assessment_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_lineid { get; set; }

        [JsonProperty("aacn_assessment_line_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_number { get; set; }
    }


    public class GroupData
    {

        [JsonProperty("_aacn_assessment_line_reference_value", NullValueHandling = NullValueHandling.Ignore)]
        public string _aacn_assessment_line_reference_value { get; set; }

        [JsonProperty("aacn_assessment_line_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_line_number { get; set; }

        [JsonProperty("aacn_assessment_lineid", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_lineid { get; set; }

        [JsonProperty("aacn_assessment_line_reference", NullValueHandling = NullValueHandling.Ignore)]
        public ReferenceLine referenceLines { get; set; }

        [JsonProperty("aacn_tool_tip_description", NullValueHandling = NullValueHandling.Ignore)]
        public string tool_tipValue { get; set; }
    }


}



﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;
    using System.Collections.Generic;
    public class ContactModelRoot
    {
        [JsonProperty("value", NullValueHandling = NullValueHandling.Ignore)]
        public List<ContactDetails> value { get; set; }
    }

    public class ContactDetails
    {
        [JsonProperty("@odata.etag", NullValueHandling = NullValueHandling.Ignore)]
        public string odataetag { get; set; }

        [JsonProperty("contactid", NullValueHandling = NullValueHandling.Ignore)]
        public string contactid { get; set; }

        [JsonProperty("firstname", NullValueHandling = NullValueHandling.Ignore)]
        public string firstname { get; set; }
    }
}

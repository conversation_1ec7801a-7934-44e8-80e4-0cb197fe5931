﻿namespace AACN.API.Model
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    public class SurveyResponse
    {
        public Guid ResponseId { get; set; }
        public string RandomizeQuestionSequence { get; set; }
        public DateTime SubmissionDate { get; set; }
        public string SurveyInstanceToken { get; set; }
        public List<Response> Responses { get; set; }


        public class Response
        {
            public Guid ResponseLineId { get; set; }
            public string AssessmentLineOptionId { get; set; }
            public string AssessmentLineId { get; set; }
            public string AnswerText { get; set; }
        }
    }
}

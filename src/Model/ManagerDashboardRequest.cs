﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;

    public class ManagerDashboardRequest
    {
        [JsonProperty("statusvalue", NullValueHandling = NullValueHandling.Ignore)]
        public string StatusValue { get; set; }

        [JsonProperty("pagesize", NullValueHandling = NullValueHandling.Ignore)]
        public int PageSize { get; set; }

        [JsonProperty("pagenumber", NullValueHandling = NullValueHandling.Ignore)]
        public int PageNumber { get; set; }

        [JsonProperty("unitmodulefilter", NullValueHandling = NullValueHandling.Ignore)]
        public string UnitModuleFilter { get; set; }

        [JsonProperty("reviewermodulefilter", NullValueHandling = NullValueHandling.Ignore)]
        public string ReviewerModuleFilter { get; set; }

        [JsonProperty("unitfilter", NullValueHandling = NullValueHandling.Ignore)]
        public string UnitFilter { get; set; }

        [JsonProperty("organizationfilter", NullValueHandling = NullValueHandling.Ignore)]
        public string OrganizationFilter { get; set; }

        [JsonProperty("contactfilter", NullValueHandling = NullValueHandling.Ignore)]
        public string ContactFilter { get; set; }

        [JsonProperty("orderfilter", NullValueHandling = NullValueHandling.Ignore)]
        public string OrderFilter { get; set; }

        [JsonProperty("arafilter", NullValueHandling = NullValueHandling.Ignore)]
        public string AraFilter { get; set; }
    }
}

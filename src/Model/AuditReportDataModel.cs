﻿namespace AACN.API.Model
{
    using Newtonsoft.Json;

    public class AuditReportDataModel
    {
        [JsonProperty("aacn_assessment_review_activity_number", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_assessment_review_activity_number { get; set; } 
        
        [JsonProperty("aacn_application_id", NullValueHandling = NullValueHandling.Ignore)]
        public int ApplicationId { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_unit_module { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_unit { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_year { get; set; }

        [JsonProperty("<EMAIL>", NullValueHandling = NullValueHandling.Ignore)]
        public string aacn_reviewer { get; set; }

    }
}
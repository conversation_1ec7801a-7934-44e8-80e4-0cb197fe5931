﻿namespace AACN.API.Model.SiteCoreSyncModel
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;

    public class SyncResponsesCommand
    {
        [JsonProperty("SurveyResponses", NullValueHandling = NullValueHandling.Ignore)]
        public List<SurveyResponseDto> SurveyResponses { get; set; }
    }

    public class SurveyResponseDto
    {
        [JsonProperty("Key", NullValueHandling = NullValueHandling.Ignore)]
        public Guid Key { get; set; }

        [JsonProperty("AssessmentKey", NullValueHandling = NullValueHandling.Ignore)]
        public Guid AssessmentKey { get; set; }

        [JsonProperty("PocCustomerKey", NullValueHandling = NullValueHandling.Ignore)]
        public Guid PocCustomerKey { get; set; }

        [JsonProperty("StatusDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime StatusDate { get; set; }

        [JsonProperty("AddDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime AddDate { get; set; }

        [JsonProperty("ChangeDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime ChangeDate { get; set; }

        [JsonProperty("cycleYear", NullValueHandling = NullValueHandling.Ignore)]
        public int CycleYear { get; set; }

        [JsonProperty("IsDeleted", NullValueHandling = NullValueHandling.Ignore)]
        public int IsDeleted { get; set; }

        [JsonProperty("ResponseLines", NullValueHandling = NullValueHandling.Ignore)]
        public List<ResponseLineDto> ResponseLines { get; set; }

        [JsonProperty("UnitKey", NullValueHandling = NullValueHandling.Ignore)]
        public Guid UnitKey { get; set; }

        [JsonProperty("OrganizationKey", NullValueHandling = NullValueHandling.Ignore)]
        public Guid OrganizationKey { get; set; }

        [JsonProperty("ApplicationId", NullValueHandling = NullValueHandling.Ignore)]
        public long ApplicationId { get; set; }

        [JsonProperty("moduleId", NullValueHandling = NullValueHandling.Ignore)]
        public int ModuleId { get; set; }

        [JsonProperty("PocFirstName", NullValueHandling = NullValueHandling.Ignore)]
        public string PocFirstName { get; set; }

        [JsonProperty("PocLastName", NullValueHandling = NullValueHandling.Ignore)]
        public string PocLastName { get; set; }

        [JsonProperty("PocCustomerId", NullValueHandling = NullValueHandling.Ignore)]
        public long PocCustomerId { get; set; }
    }
    public class ResponseLineDto
    {
        [JsonProperty("Key", NullValueHandling = NullValueHandling.Ignore)]
        public Guid Key { get; set; }

        [JsonProperty("QuestionKey", NullValueHandling = NullValueHandling.Ignore)]
        public Guid QuestionKey { get; set; }

        [JsonProperty("SubquestionKey", NullValueHandling = NullValueHandling.Ignore)]
        public Guid SubquestionKey { get; set; }

        [JsonProperty("StatusDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime StatusDate { get; set; }

        [JsonProperty("AnswerCode", NullValueHandling = NullValueHandling.Ignore)]
        public string AnswerCode { get; set; }

        [JsonProperty("AnswerText", NullValueHandling = NullValueHandling.Ignore)]
        public string AnswerText { get; set; }

        [JsonProperty("AnswerNumber", NullValueHandling = NullValueHandling.Ignore)]
        public int AnswerNumber { get; set; }

        [JsonProperty("AddDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime AddDate { get; set; }

        [JsonProperty("ChangeDate", NullValueHandling = NullValueHandling.Ignore)]
        public DateTime ChangeDate { get; set; }

        [JsonProperty("IsDeleted", NullValueHandling = NullValueHandling.Ignore)]
        public int IsDeleted { get; set; }
    }
    public class SiteCoreResponseModel
    {
        public string ResponseKey { get; set; }
        public bool SyncStatus { get; set; } = false;
        public int RequestResponseLinesCounter { get; set; }
        public int SyncedLinesCounter { get; set; }
        public string OperationType { get; set; }
        public ReviewAcitityResponseModel ReviewAcitityKey { get; set; }
        public string ErrorMessage { get; set; }
        public string UpdateReviewActivity { get; set; }
        public bool YearSyncStatus {  get; set; } = true;
    }

    public class ReviewAcitityResponseModel
    {
        public string ReviewAcitivityKey { get; set; }
        public string Status { get; set; }
        public string ErrorMessage { get; set; }
    }
    public class SiteCoreResponseModelbatchResponses
    {
        public int toalResponseSynced { get; set; }
        public int toalAcitivigtyCreated { get; set; }
        public string ErrorMessage { get; set; }
        public ResponseKeys responseKeys { get; set; }
        public ReviewAcitivityKeys reviewAcitivityKeys { get; set; }
    }

    public class ResponseKeys
    {
        public string[] ResponseArray { get; set; }
    }

    public class ReviewAcitivityKeys
    {
        public string[] ReviewAcitivityArray { get; set; }
    }


}

﻿namespace AACN.API.Controllers
{
    using AACN.API.Model;
    using AACN.API.Service.NtiInteractiveSessionService;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using System.Threading.Tasks;
    using System;
    using System.Net;
    using AACN.API.Helper;
    using Microsoft.AspNetCore.Authorization;

    [Route("api/")]
    [ApiController]
    public class NTIInteractiveSessionController : Controller
    {
        INtiInteractiveSessionService _ntiSessionService;
        private readonly ILogger<NTIInteractiveSessionController> _logger;
        public NTIInteractiveSessionController(INtiInteractiveSessionService ntiSessionService, ILogger<NTIInteractiveSessionController> logger)
        {
            _logger = logger;
            _ntiSessionService = ntiSessionService;
        }

        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpGet("[controller]/getAssessmentBySessionCode")]
        public async Task<ActionResult<NtiFormSessionResponseModel>> GetFormBySessionCode(string sessionCode)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionCode))
                    return BadRequest("Please provide sessionCode !");

                NtiFormSessionResponseModel response = await _ntiSessionService.GetFormBySessionCode(sessionCode);

                if (response.Value.Count > 0)
                    return response;

                return NotFound("No Data Found For " + sessionCode);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpPost("[controller]/createSessionResponse")]
        public async Task<ActionResult<NtiFormSessionResponse>> CreateSessionResponse([FromBody] NtiFormSessionRequest ntiFormSessionRequestModel)
        {
            try
            {
                if (string.IsNullOrEmpty(ntiFormSessionRequestModel.FirstName) || string.IsNullOrEmpty(ntiFormSessionRequestModel.LastName)
                    || string.IsNullOrEmpty(ntiFormSessionRequestModel.SessionId) || string.IsNullOrEmpty(ntiFormSessionRequestModel.SessionTime)
                    || string.IsNullOrEmpty(ntiFormSessionRequestModel.EmailAddress) || string.IsNullOrEmpty(ntiFormSessionRequestModel.SessionDate)
                    || string.IsNullOrEmpty(ntiFormSessionRequestModel.Date))
                {
                    return BadRequest("Complete data is required to proceed further");
                }
                else
                {
                    return await _ntiSessionService.CreateSessionResponse(ntiFormSessionRequestModel);
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpGet("[controller]/getResponseBySessionCode")]
        public async Task<ActionResult<NtiSessionResponseModel>> GetResponseBySessionCode(string sessionCode)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionCode))
                    return BadRequest("Please provide sessionCode !");

                NtiSessionResponseModel response = await _ntiSessionService.GetResponseBySessionCode(sessionCode);

                if (response.Value.Count > 0)
                    return response;

                return NotFound("No Data Found For " + sessionCode);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }
    }
}

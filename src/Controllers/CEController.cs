﻿namespace AACN.API.Controllers
{
    using AACN.API.Model;
    using AACN.API.Service;
    using Microsoft.AspNetCore.Mvc;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Threading.Tasks;


    [Route("api/")]
    [ApiController]
    public class CEController : Controller
    {
        CEActivityService ceActivityService = new CEActivityService();
        [HttpGet("ce")]
        public ActionResult<CEActivityResponseModel> GetAnswerById(string customernumber, string eventcode, string sessioncode, string extuserid, string extceid, string skipbusinessrule)
        {
            if (string.IsNullOrEmpty(customernumber) && string.IsNullOrEmpty(eventcode))
            {
                return ValidationProblem("Please Prvoide customernumber and eventcode");
            }
            CEActivityResponseModel _answerModel = new CEActivityResponseModel();

            var responseData = new HttpResponseMessage { StatusCode = System.Net.HttpStatusCode.OK };
            if (!string.IsNullOrEmpty(skipbusinessrule))
            {
                responseData = new HttpResponseMessage { StatusCode = System.Net.HttpStatusCode.OK };
            }
            else
            {
                responseData = ceActivityService.CheckSessionStatus(customernumber, sessioncode, eventcode);
            }
            if (responseData.StatusCode == System.Net.HttpStatusCode.OK) // Sesssion is Valid 
            {
                _answerModel = ceActivityService.GetEvaluationData(customernumber, sessioncode, eventcode);
                if (_answerModel.responseData == null && _answerModel.contactResponse == null)
                {
                    return BadRequest("No data Found!");
                }
                return _answerModel;
            }
            else
            {
                if (responseData.StatusCode == System.Net.HttpStatusCode.Conflict)  //Return Conflict to FrontEnd!
                {
                    return Conflict(responseData.Content.ReadAsStringAsync());
                }
                else if (responseData.StatusCode == System.Net.HttpStatusCode.BadRequest) //Return BadRequest to FrontEnd!
                {
                    return BadRequest(responseData.Content.ReadAsStringAsync());
                }
            }

            return _answerModel;
        }
    }
}

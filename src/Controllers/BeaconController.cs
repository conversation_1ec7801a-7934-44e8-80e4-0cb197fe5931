﻿namespace AACN.API.Controllers
{
    using AACN.API.Helper;
    using AACN.API.Model;
    using AACN.API.Model.Manager;
    using AACN.API.Model.OrganisationModel;
    using AACN.API.Model.ReviewerAssessmentLoadModel;
    using AACN.API.Model.SiteCoreSyncModel;
    using AACN.API.Service;
    using AACN.API.Service.Beacon_Service;
    using AACN.Services;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;
    using static AACN.API.Service.Utility;

    [Route("api/")]
    [ApiController]
    public class beaconController : Controller
    {
        IBeaconService _beaconService;
        private readonly ILogger<beaconController> _logger;
        public beaconController(IBeaconService beaconService, ILogger<beaconController> logger)
        {
            _logger = logger;
            _beaconService = beaconService;
        }

        #region SiteCore_TO_D365 API's
        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpGet("[controller]/verifyUser")]
        public ActionResult<BeaconResponse> VerifyUser(string JWTtoken)
        {
            try
            {
                BeaconResponse serviceResponse = _beaconService.UserVerification(JWTtoken, _logger);
                if (serviceResponse.Error != string.Empty)
                {
                    return BadRequest(new { Error = serviceResponse.Error });
                }
                else
                {
                    return _beaconService.UserVerification(JWTtoken, _logger);
                }
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, "An unexpected error occurred.");
            }
        }

        [HttpPost("[controller]/SyncResponses")]
        public async Task<ActionResult<List<SiteCoreResponseModel>>> SyncSiteCoreResponses(SyncResponsesCommand siteCoreRequestModel)
        {
            try
            {
                var _apiResponse = await _beaconService.SyncSiteCoreResponses(siteCoreRequestModel, _logger);
                if (_apiResponse.Any())
                {
                    var yearVersionRequests = _apiResponse.Where(r => r.YearSyncStatus == false).ToList();
                    if (yearVersionRequests.Count > 0 && yearVersionRequests != null)
                    {
                        return StatusCode((int)HttpStatusCode.BadRequest, "Provided year does not exists in D365!");
                    }
                    var failedRequests = _apiResponse.Where(r => r.SyncStatus == false).ToList();
                    if (failedRequests.Count > 0 && failedRequests != null)
                    {
                        return StatusCode((int)HttpStatusCode.InternalServerError, "Something went wrong!");
                    }
                    return Ok(_apiResponse);
                }
                else
                {
                    return StatusCode((int)HttpStatusCode.InternalServerError, "Something went wrong!");
                }
            }
            catch (ArgumentException ex)
            {
                //For Catching wrong GUID Format..
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, $"Something went wrong - {ex.Message}!");
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }
        [HttpPost("[controller]/SyncResponsesBatch")]
        public async Task<ActionResult<SiteCoreResponseModelbatchResponses>> SyncSiteCoreResponsesBatch(SyncResponsesCommand siteCoreRequestModel)
        {
            try
            {
                SiteCoreResponseModelbatchResponses responses = new SiteCoreResponseModelbatchResponses();
                if (!string.IsNullOrEmpty(responses.ErrorMessage))
                {
                    return BadRequest("responses.ErrorMessage");
                }

                else
                {
                    return await _beaconService.SyncSiteCoreResponsesBatchRequests(siteCoreRequestModel, _logger);
                }

            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }

        }

        [HttpPost("[controller]/SyncOrganizationData")]
        public async Task<ActionResult<List<APIResponse>>> SyncOrganizationData(PostOrganizationCommand organizationModel)
        {
            try
            {
                return await _beaconService.SyncOrganizationData(organizationModel, _logger);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }

        }

        [HttpPost("[controller]/SyncReviewerData")]
        public async Task<ActionResult<List<APIResponse>>> SyncReviewerData(SyncReviwerCommand syncReviwerCommand)
        {
            try
            {
                return await _beaconService.SyncReviewerData(syncReviwerCommand, _logger);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        [HttpPost("[controller]/SyncUnit")]
        public async Task<ActionResult<List<APIResponse>>> SyncUnits(PostUnitCommand postunitmodel)
        {
            try
            {
                return await _beaconService.SyncUnits(postunitmodel, _logger);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        [HttpPost("[controller]/RemoveResponse")]
        public async Task<ActionResult<List<ReturnResponse>>> RemoveResponses(RemoveResponse responsemodel)
        {
            try
            {
                if (responsemodel != null && responsemodel.SurveyStatuses != null && responsemodel.SurveyStatuses.Count >= 1)
                {
                    List<ReturnResponse> _returnresponselines = await _beaconService.RemoveResponses(responsemodel, _logger);

                    return _returnresponselines;
                }
                return BadRequest("Please Provide a data");

            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        #endregion

        #region D365_Portal_Internal_API's

        #region ReviewerLoad -
        [HttpGet("[controller]/aacn_internal/getAssessmentActivitybyReviwerId")]
        public async Task<ActionResult<AssessmentReviewActivtyRootModel>> GetReviwerAssessments(string reviewerId, string yearId)
        {
            if (string.IsNullOrEmpty(reviewerId))
                return BadRequest("Please provide ReviewerId !");
            if (string.IsNullOrEmpty(yearId))
                return BadRequest("Please provide YearId.");

            AssessmentReviewActivtyRootModel assessmentReviewModel = await _beaconService.GetAssessmentActivityByReviwerId(reviewerId, yearId, _logger);
            if ((assessmentReviewModel != null && assessmentReviewModel.assessmentReviews.Count > 0) || assessmentReviewModel.status_Counter != null)
            {
                return assessmentReviewModel;
            }
            else
            {
                return BadRequest($"No Data found for {reviewerId}");

            }
        }

        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpGet("[controller]/aacn_internal/getReviewerandUserReponses")]
        public async Task<ActionResult<ReviewerAssessmentLoadResponseModel>> GetAssessmentReviewsByReviewNumber(string assessmentReviewNumber)
        {
            try
            {
                return await _beaconService.GetReviewerAndUserResponses(assessmentReviewNumber, _logger);

            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, "An unexpected error occurred.");
            }
        }

        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpGet("[controller]/aacn_internal/getReviewerModule")]
        public async Task<ActionResult<ReviewerAssessmentLoadResponseModel>> GetReviewerModule([FromQuery] string ReviewerId)
        {
            try
            {
                ReviewerAssessmentLoadResponseModel reviewerAssessmentLoadResponseModel = await _beaconService.GetReviewerModuleData(ReviewerId, _logger);
                if (reviewerAssessmentLoadResponseModel != null && reviewerAssessmentLoadResponseModel.assessmentReviewActivity.Count > 0)
                {
                    return reviewerAssessmentLoadResponseModel;
                }
                else
                {
                    return StatusCode((int)HttpStatusCode.BadRequest, "No data Found !");

                }

            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        #endregion ReviewerLoad -

        #region ManagerLoad - 

        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpGet("[controller]/aacn_internal/getAuditReport")]
        public async Task<ActionResult<GetAuditRoot>> GetAuditReport()
        {
            try
            {
                GetAuditRoot getAuditModel = await _beaconService.GetAuditReport(_logger);
                if (getAuditModel != null)
                {
                    if (getAuditModel.auditReportvalue.Count > 0)
                    {
                        return getAuditModel;
                    }
                    return NotFound($"No Data found for Audit");

                }
                else
                {
                    return NotFound($"No Data found for Audit");
                }
            }
            catch (Exception ex)
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpPost("[controller]/aacn_internal/randomizedataforaudit")]
        public async Task<StatusCodeResult> RandomizeDataForAuditReport()
        {
            try
            {
                StatusCodeResult randomizeReturnValue = await _beaconService.RandomizeData(_logger);
                if (randomizeReturnValue.StatusCode == 200)
                {
                    return new StatusCodeResult(200);
                }
                else
                {
                    return new StatusCodeResult(400);
                }
            }
            catch (Exception ex)
            {
                return new StatusCodeResult(500);
            }
        }

        [HttpGet("[controller]/aacn_internal/getManagerDashboardByStatus")]
        public async Task<ActionResult<AssessmentReviewActivtyRootModel>> GetManagerDashboardByStatus(string statusValue)
        {
            if (string.IsNullOrEmpty(statusValue))
            {
                return BadRequest("Please provide statusValue !");
            }
            else
            {
                int? statusIntValue = EnumStatusHelper.GetRequestStatusValue(statusValue);
                if (!statusIntValue.HasValue)
                {
                    return BadRequest($"Invalid status value: {statusValue}");
                }
            }
            AssessmentReviewActivtyRootModel assessmentReviewModel = await _beaconService.GetManagerDashboardByStatus(statusValue, _logger);
            if (assessmentReviewModel != null && assessmentReviewModel.assessmentReviews.Count > 0)
            {
                return assessmentReviewModel;
            }
            else
            {
                return BadRequest($"No Data found for {statusValue}");
            }
        }

        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpGet("[controller]/aacn_internal/getManagerDashboardByStatusWithPagination")]
        public async Task<ActionResult<AssessmentReviewActivtyRootModel>> GetManagerDashboardByStatusWithPagination([FromQuery] ManagerDashboardRequest managerRequestModel)
        {
            try
            {
                if (string.IsNullOrEmpty(managerRequestModel.StatusValue))
                {
                    return BadRequest("Please provide statusValue !");
                }
                if (managerRequestModel.PageSize == 0)
                {
                    return BadRequest("PageSize can't be zero");
                }
                if (managerRequestModel.PageNumber == 0)
                {
                    return BadRequest("PageNumber can't be zero");
                }

                int? statusIntValue = EnumStatusHelper.GetRequestStatusValue(managerRequestModel.StatusValue);
                if (!statusIntValue.HasValue)
                {
                    return BadRequest($"Invalid status value: {managerRequestModel.StatusValue}");
                }
                if (statusIntValue == (int)RequestStatus.all)
                {
                    managerRequestModel.StatusValue = string.Join(",", (int)RequestStatus.Ready, (int)RequestStatus.Assigned, (int)RequestStatus.Accepted, (int)RequestStatus.Rejected,(int)RequestStatus.InProgress,(int)RequestStatus.Complete);
                }
                else if (statusIntValue == (int)RequestStatus.Complete)
                {
                    managerRequestModel.StatusValue = $"{(int)RequestStatus.Complete}";
                }
                else if (statusIntValue == (int)RequestStatus.InProgress)
                {
                    managerRequestModel.StatusValue = $"{(int)RequestStatus.InProgress}";
                }
                else if (statusIntValue == (int)RequestStatus.Rejected)
                {
                    managerRequestModel.StatusValue = $"{(int)RequestStatus.Rejected}";
                }
                else if (statusIntValue == (int)RequestStatus.Accepted)
                {
                    managerRequestModel.StatusValue = $"{(int)RequestStatus.Accepted}";
                }
                else if (statusIntValue == (int)RequestStatus.Assigned)
                {
                    managerRequestModel.StatusValue = $"{(int)RequestStatus.Assigned}";
                }
                else if (statusIntValue == (int)RequestStatus.Ready)
                {
                    managerRequestModel.StatusValue = $"{(int)RequestStatus.Ready}";
                }

                AssessmentReviewActivtyRootModel assessmentReviewModel = await _beaconService.GetManagerDashboardByStatusWithPagination(managerRequestModel, _logger);
                if (assessmentReviewModel != null && assessmentReviewModel.assessmentReviews.Count > 0)
                {
                    return assessmentReviewModel;
                }

                return NotFound($"No Data found for {managerRequestModel.StatusValue}");
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpGet("[controller]/aacn_internal/getManagerDashboard")]
        public async Task<ActionResult<AssessmentReviewActivtyRootModel>> GetManagerDashboard([FromQuery] string yearId)
        {
            try
            {
                if (string.IsNullOrEmpty(yearId))
                    return StatusCode((int)HttpStatusCode.BadRequest, "YearId can't be null.");

                AssessmentReviewActivtyRootModel assessmentReviewModel = await _beaconService.GetManagerDashboard(yearId, _logger);
                if (assessmentReviewModel != null && (assessmentReviewModel.assessmentReviews.Count > 0 || assessmentReviewModel.status_Counter != null))
                {
                    return assessmentReviewModel;
                }
                else
                {
                    return StatusCode((int)HttpStatusCode.BadRequest, "No data Found !");
                }

            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        [Authorize(AuthenticationSchemes = "CustomScheme")]
        [HttpGet("[controller]/aacn_internal/getUnitModule")]
        public async Task<ActionResult<ReviewerAssessmentLoadResponseModel>> GetUnitModule([FromQuery] string ReviewerId)
        {
            try
            {
                ReviewerAssessmentLoadResponseModel reviewerAssessmentLoadResponseModel = await _beaconService.GetUnitModule(ReviewerId, _logger);
                if (reviewerAssessmentLoadResponseModel != null && reviewerAssessmentLoadResponseModel.assessmentReviewActivity.Count > 0)
                {
                    return reviewerAssessmentLoadResponseModel;
                }
                else
                {
                    return StatusCode((int)HttpStatusCode.BadRequest, "No data Found !");

                }

            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        [HttpGet("[controller]/VerfiyUnitResponses")]
        public async Task<ActionResult<UnitVerificationResponse>> VerfiyUnitResponses(string unitId)
        {
            try
            {
                if (string.IsNullOrEmpty(unitId))
                {
                    return StatusCode((int)HttpStatusCode.BadRequest, "UnitId Required !");
                }
                return await _beaconService.VerfiyUnitResponses(unitId, _logger);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        [HttpGet("[controller]/loadManagerData")]
        public async Task<ActionResult<ManagerDataModel>> LoadManagerPage(string UnitKey, string requestPage, string previousModule, string yearId)
        {
            try
            {
                if (string.IsNullOrEmpty(yearId))
                    return StatusCode((int)HttpStatusCode.BadRequest, "Year is required!");

                return await _beaconService.LoadManagerData(UnitKey, requestPage, previousModule, yearId, _logger);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        [HttpPost("[controller]/managerSubmit")]
        public async Task<ActionResult<ResponseSubmitResponse>> UpdateManagerResponses([FromBody] UpdateResponseModel updateResponseModel)
        {
            try
            {
                ResponseSubmitResponse response = await _beaconService.UpdateManagerResponsesApproach3(updateResponseModel, _logger);
                if (response.SyncStatus)
                {
                    return response;
                }
                else
                {
                    return BadRequest(response);
                }
                //return await _beaconService.UpdateManagerResponses(updateResponseModel, _logger); //Use for Second Approach
                //return await _beaconService.UpdateManagerResponsesApproach3(updateResponseModel, _logger);
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, ex.Message);
            }
        }

        #endregion ManagerLoad -

        #region Common API's for Portal - D365 Data Sync  
        [HttpGet("[controller]/aacn_internal/getContactListByRole")]
        public async Task<ActionResult<ContactListModelByRole>> GetContactListByRole(string requestType)
        {
            if (string.IsNullOrEmpty(requestType))
            {
                return BadRequest("Please provide RequestType");
            }
            else
            {
                if (requestType.ToLower() != Utility.roleType.Manager.ToString().ToLower() && requestType.ToLower() != Utility.roleType.Reviewer.ToString().ToLower())
                {
                    return BadRequest($"Invalid Request Type, {requestType}");
                }
                return await _beaconService.GetContactListByRole(requestType, _logger);
            }
        }
        [HttpPost("[controller]/reviewerSubmit")]
        public async Task<ActionResult<BeaconAPIResponse>> PostBeaconResponse([FromBody] BeaconRespnseData beaconResponseData, [FromQuery] string requestFor)
        {
            try
            {
                BeaconAPIResponse genericResponse = await _beaconService.CreateBeaconResponse(beaconResponseData, requestFor, _logger);
                if (genericResponse.StatusCode == (int)HttpStatusCode.NoContent)
                {
                    return genericResponse;
                }
                else
                {
                    return BadRequest(new APIResponse { RecordId = Guid.Empty, StatusCode = (int)genericResponse.StatusCode, Status = genericResponse.Status });
                }
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
            catch (Exception ex)
            {
                LoggerHelper.LogException(_logger, ex);
                return StatusCode((int)HttpStatusCode.InternalServerError, "An unexpected error occurred.");
            }
        }

        [HttpPost("[controller]/aacn_internal/createAssessmentActivityResponse")]
        public async Task<ActionResult<APIResponse>> PostAssessment_Acitivity_Response([FromBody] PostAssessmentReviewActivity postAssessmentReviewActivity)
        {
            if (postAssessmentReviewActivity == null)
            {
                return BadRequest("Please provide Data!");
            }
            APIResponse response = await _beaconService.Post_AssessmentReview(postAssessmentReviewActivity, _logger);
            if (response.StatusCode == (int)HttpStatusCode.NoContent && response.RecordId != Guid.Empty)
            {
                return response;
            }
            else
            {
                return BadRequest(response.ToString());
            }
        }

        [HttpPost("[controller]/aacn_internal/updateAssessmentActivityResponse")]
        public async Task<ActionResult<APIResponse>> Update_ActivityResponse([FromBody] PostAssessmentReviewActivity postAssessmentReviewActivity)
        {
            if (postAssessmentReviewActivity.Assessment_Review_ActivityId == null)
            {
                return BadRequest("Please provide review id for update!");
            }
            APIResponse response = await _beaconService.Update_Assessment_Review(postAssessmentReviewActivity, _logger);
            if (response.StatusCode == (int)HttpStatusCode.NoContent && response.RecordId != Guid.Empty)
            {
                return response;
            }
            else
            {
                return BadRequest(response.ToString());
            }
        }

        [HttpPost("[controller]/aacn_internal/updateAssessmentActivityResponseBatch")]
        public async Task<ActionResult<List<APIResponse>>> Update_ActivityResponseBatch([FromBody] PostAssessmentReviewActivityCommand postAssessmentReviewActivityCommand)
        {
            List<APIResponse> response = await _beaconService.Update_Assessment_ReviewBatch(postAssessmentReviewActivityCommand, _logger);
            if (response.Any())
            {
                var badRequestList = response.Where(r => r.StatusCode == (int)HttpStatusCode.BadRequest).ToList();
                if (badRequestList.Count > 0 && badRequestList != null)
                {
                    return BadRequest(badRequestList);

                }
                else
                {
                    return response;
                }
            }
            else
            {
                return BadRequest(response.ToString());
            }
        }
        #endregion Common API's for Portal - D365 Data Sync

        #endregion

    }
}

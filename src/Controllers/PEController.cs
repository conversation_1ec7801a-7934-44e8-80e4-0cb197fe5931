﻿namespace AACN.API.Controllers
{
    using AACN.API.Model;
    using AACN.API.Service;
    using Microsoft.AspNetCore.Mvc;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Threading.Tasks;


    [Route("api/")]
    [ApiController]
    public class PEController : Controller
    {
        HweatService hweatService = new HweatService();
        PEActivityService peActivityService = new PEActivityService();

        [HttpGet("pe")]
        public ActionResult<CEActivityResponseModel> GetAnswerById(string customernumber, string eventcode)
        {
            if (string.IsNullOrEmpty(customernumber) && string.IsNullOrEmpty(eventcode))
            {
                return ValidationProblem("Please Prvoide customernumber and eventcode");
            }
            CEActivityResponseModel _answerModel = new CEActivityResponseModel();
            string eventKey = peActivityService.check_EventinD365(eventcode);
            if (string.IsNullOrEmpty(eventKey))
            {
                String Error = "Event Code '" + eventcode + "' does not exist into the System";
                string JsonError = $"{{\"error\":[\"{eventcode}\"]}}";

                return BadRequest("No data Found with event Id - "+ JsonError);
            }
            var responseData = peActivityService.CheckProgramStatus(customernumber, eventcode);
            //var responseData = new HttpResponseMessage { StatusCode = System.Net.HttpStatusCode.OK };
            if (responseData.StatusCode == System.Net.HttpStatusCode.OK) // Program is Valid 
            {
                _answerModel = peActivityService.GetEvaluationData(customernumber, eventcode);
                if (_answerModel.responseData == null && _answerModel.contactResponse == null)
                {
                    return BadRequest("No data Found!");
                }
                return _answerModel;
            }
            else
            {
                if (responseData.StatusCode == System.Net.HttpStatusCode.Conflict)  //Return Conflict to FrontEnd!
                {
                    return Conflict(responseData.Content.ReadAsStringAsync());
                }

                else if (responseData.StatusCode == System.Net.HttpStatusCode.BadRequest) //Return BadRequest to FrontEnd!
                {
                    return BadRequest(responseData.Content.ReadAsStringAsync());
                }
            }

            return _answerModel;
        }
    }
}

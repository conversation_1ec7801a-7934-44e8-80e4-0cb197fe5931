﻿namespace AACN.API.Controllers
{
    using AACN.API.Model;
    using AACN.API.Service;
    using AACN.Services;
    using System.Net.Http;
    using Microsoft.AspNetCore.Mvc;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;

    [Route("api/")]
    [ApiController]
    public class answerController : Controller
    {
        AnswerService _answerService = new AnswerService();
        private readonly ILogger<answerController> _logger;
        public answerController(ILogger<answerController> logger)
        {
            _logger = logger;
            _answerService = new AnswerService();
        }

        [HttpGet("answer")]
        public ActionResult<List<AnswerData>> GetAnswerById(string answerId)
        {
            AnswerModel _answerModel = new AnswerModel();
            if (answerId != null)
            {
                _answerModel = _answerService.getAnswerbyId(answerId, _logger);
            }
            else
            {
                return ValidationProblem("Missing Anser Id");
            }
            if (_answerModel.value == null || (_answerModel.value != null && _answerModel.value.Count == 0))
            {
                return ValidationProblem("No Data Found!");
            }
            return _answerModel.value;
        }

        [HttpPost("answer")]
        public ActionResult<APIResponse> CreateAnswer([FromBody] PostAnswer answerData)

        {
            var result = _answerService.createAnswer(answerData);
            if (result.StatusCode == System.Net.HttpStatusCode.NoContent)
            {
                string _recordUrl = result.Headers.GetValues("OData-EntityId").FirstOrDefault();
                string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                return new APIResponse { RecordId = new Guid(splitRetrievedData[1].ToString()), StatusCode = (int)result.StatusCode, Status = "Created" };
                //return Ok(result.Content.ReadAsStringAsync().Result);
            }
            else
            {
                return BadRequest(new APIResponse
                { RecordId = Guid.Empty, StatusCode = (int)result.StatusCode, Status = result.Content.ReadAsStringAsync().Result });
            }
        }

        [HttpPatch("answer")]
        public ActionResult<APIResponse> UpdateAnswer([FromBody] PostAnswer answerData)
        {
            if (answerData.answerId == null)
            {
                return ValidationProblem("Please provide answerId for Update!");
            }

            var result = _answerService.updateAnswer(answerData, answerData.answerId);
            if (result.StatusCode == System.Net.HttpStatusCode.NoContent)
            {
                string _recordUrl = result.Headers.GetValues("OData-EntityId").FirstOrDefault();
                string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                return new APIResponse { RecordId = new Guid(splitRetrievedData[1].ToString()), StatusCode = (int)result.StatusCode, Status = "Updated" };
                //return Ok(result.Content.ReadAsStringAsync().Result);
            }
            else
            {
                return BadRequest(new APIResponse
                { RecordId = Guid.Empty, StatusCode = (int)result.StatusCode, Status = result.Content.ReadAsStringAsync().Result });
            }
        }


    }
}

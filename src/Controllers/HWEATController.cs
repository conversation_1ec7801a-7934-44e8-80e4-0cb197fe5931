﻿namespace AACN.API.Controllers
{
    using AACN.API.Model;
    using AACN.API.Service;
    using Microsoft.AspNetCore.Authentication.JwtBearer;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using Microsoft.Identity.Client;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;


    [Route("api/")]
    [ApiController]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class hweatactivitiesController : Controller
    {
        HweatService hweatService = new HweatService();
        private readonly ILogger<hweatactivitiesController> _logger;
        public hweatactivitiesController(ILogger<hweatactivitiesController> logger)
        {
            _logger = logger;
            hweatService = new HweatService();
        }

        [HttpGet("hweat")]
        [AllowAnonymous]
        public ActionResult<CEActivityResponseModel> GetAnswerById(string instanceid, string assessmentid, string providerid)
        {
            if (string.IsNullOrEmpty(instanceid) && string.IsNullOrEmpty(assessmentid))
            {
                return ValidationProblem("Please Prvoide MemberId and Event Code");
            }

            bool instanceStatus = hweatService.InstanceValidation(instanceid, _logger);
            if (instanceStatus == false)
            {
                return BadRequest("This activity has ended");
            }

            CEActivityResponseModel _answerModel = new CEActivityResponseModel();
            _answerModel = hweatService.GetEvaluationData(instanceid, assessmentid, providerid);
            if (_answerModel.responseData == null && _answerModel.contactResponse == null)
            {
                return BadRequest("No data Found!");
            }
            return _answerModel;
        }

        [HttpPost("[controller]/createInstance")]
        public ActionResult<APIResponse> createInstanceData([FromBody] PostInstanceData postinstance)
        {

            var result = hweatService.createInstanceData(postinstance);
            if (result.StatusCode == System.Net.HttpStatusCode.NoContent)
            {

                string _recordUrl = result.Headers.GetValues("OData-EntityId").FirstOrDefault();
                string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                return new APIResponse { RecordId = new Guid(splitRetrievedData[1].ToString()), StatusCode = (int)result.StatusCode, Status = "Created" };
                //return Ok(result.Content.ReadAsStringAsync().Result);
            }
            else
            {
                return BadRequest(new APIResponse
                { RecordId = Guid.Empty, StatusCode = (int)result.StatusCode, Status = result.Content.ReadAsStringAsync().Result });
            }

        }

        [HttpPost("[controller]/updateInstance")]
        public ActionResult<APIResponse> UpdateInstanceData([FromBody] PostInstanceData postinstance)
        {
            if (postinstance.instanceId == null)
            {
                return BadRequest("Please Specify Instance Id for Update!");
            }
            var result = hweatService.UpdateInstanceData(postinstance, _logger);
            if (result.StatusCode == System.Net.HttpStatusCode.NoContent)
            {

                string _recordUrl = result.Headers.GetValues("OData-EntityId").FirstOrDefault();
                string[] splitRetrievedData = _recordUrl.Split('[', '(', ')', ']');
                return new APIResponse { RecordId = new Guid(splitRetrievedData[1].ToString()), StatusCode = (int)result.StatusCode, Status = "Updated" };
                //return Ok(result.Content.ReadAsStringAsync().Result);
            }
            else
            {
                return BadRequest(new APIResponse
                { RecordId = Guid.Empty, StatusCode = (int)result.StatusCode, Status = result.Content.ReadAsStringAsync().Result });
            }

        }

        [HttpPost("[controller]/createResponseData")]
        [AllowAnonymous]
        public ActionResult<APIResponseNF_D365> createResponseData([FromBody] PostResponseData PostResponseData)
        {
            APIResponseNF_D365 apiResponseNF_D365 = hweatService.createResponseData(PostResponseData, _logger);
           
            if (apiResponseNF_D365.RecordId != Guid.Empty)
            {
                if (apiResponseNF_D365.StatusCode_D365 == (int)System.Net.HttpStatusCode.NoContent && apiResponseNF_D365.StatusCode_NF == (int)System.Net.HttpStatusCode.NoContent)
                {
                    return apiResponseNF_D365;
                }
                else
                {
                    return BadRequest(new APIResponseNF_D365
                    { RecordId = Guid.Empty, StatusCode_D365 = apiResponseNF_D365.StatusCode_D365, StatusCode_NF = apiResponseNF_D365.StatusCode_NF, D365_status = apiResponseNF_D365.D365_status, NF_Status = apiResponseNF_D365.NF_Status });
                }
            }
            else
            {
                return BadRequest(new APIResponseNF_D365
                { RecordId = Guid.Empty, StatusCode_D365 = apiResponseNF_D365.StatusCode_D365, StatusCode_NF = apiResponseNF_D365.StatusCode_NF, D365_status = apiResponseNF_D365.D365_status, NF_Status = apiResponseNF_D365.NF_Status });
            }
        }

        [HttpGet("[controller]/GetResponseData")]
        [AllowAnonymous]
        public ActionResult<ResponseDataModel> GetResponseData(string InstanceId)
        {
            if (string.IsNullOrEmpty(InstanceId))
            {
                return BadRequest("Please enter InstanceId ");
            }
            ResponseDataModel responsedataModel = new ResponseDataModel();
            responsedataModel = hweatService.getResponsesByInstanceId(InstanceId);
            if (responsedataModel == null && responsedataModel.responseValue.Count == 0)
            {
                return BadRequest("No data found! ");
            }
            return responsedataModel;
        }

        [HttpGet("[controller]/getResponseCounter")]
        public ActionResult<ResponseCounterData> GetResponseCounter(string instanceId)
        {
            ResponseCounterData _responseData = new ResponseCounterData();
            if (string.IsNullOrEmpty(instanceId))
            {
                return BadRequest("Please provide instanceId!");
            }
            _responseData = hweatService.checkResponseCounterbyInstanceId(instanceId);
            if (_responseData == null)
            {
                return BadRequest("Something went Wrong!");
            }
            return _responseData;
        }

        [HttpGet("[controller]/getSurveyInstances")]
        [AllowAnonymous]
        public ActionResult<List<Instances>> GetSurveyInstances(string userId, string UnitId)
        {
            List<Instances> _instanceData = new List<Instances>();
            if (string.IsNullOrEmpty(userId))
            {
                return BadRequest("Please provide userId!");
            }
            _instanceData = hweatService.checkinstanceById(userId, UnitId);
            if (_instanceData.Count == 0)
            {
                return BadRequest("No Data found with UserId - " + userId);

            }
            return _instanceData;
        }

    }
}

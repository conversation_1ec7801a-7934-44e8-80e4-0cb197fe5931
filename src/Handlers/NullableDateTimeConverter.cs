using Newtonsoft.Json;
using System;

public class NullableDateTimeConverter : JsonConverter<DateTime?>
{
    public override void Write<PERSON><PERSON>(JsonWriter writer, DateTime? value, JsonSerializer serializer)
    {
        if (value.HasValue)
        {
            writer.WriteValue(value.Value.ToString("o")); // Use ISO 8601 format
        }
        else
        {
            writer.WriteNull();
        }
    }

    public override DateTime? ReadJson(JsonReader reader, Type objectType, DateTime? existingValue, bool hasExistingValue, JsonSerializer serializer)
    {
        if (reader.TokenType == JsonToken.Null)
        {
            return null;
        }

        try
        {
            return DateTime.Parse(reader.Value.ToString());
        }
        catch (FormatException)
        {
            return null; // Return null if the value cannot be parsed
        }
    }

    //public override bool CanConvert(Type objectType)
    //{
    //    return objectType == typeof(DateTime?);
    //}
}

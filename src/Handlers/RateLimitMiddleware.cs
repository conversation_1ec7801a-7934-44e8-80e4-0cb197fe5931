using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;

public class RateLimitMiddleware
{
    private readonly RequestDelegate _next;
    private static readonly ConcurrentDictionary<string, (DateTime Timestamp, int Count)> _requestCounts = new ConcurrentDictionary<string, (DateTime Timestamp, int Count)>();
    private const int Limit = 100; // <PERSON> requests
    private static readonly TimeSpan Period = TimeSpan.FromMinutes(1); // Time period

    public RateLimitMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var clientIp = context.Connection.RemoteIpAddress?.ToString();
        if (clientIp == null)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            await context.Response.WriteAsync("Unable to determine client IP");
            return;
        }

        var now = DateTime.UtcNow;
        var requestInfo = _requestCounts.GetOrAdd(clientIp, _ => (now, 0));

        if (now - requestInfo.Timestamp < Period)
        {
            if (requestInfo.Count >= Limit)
            {
                context.Response.StatusCode = StatusCodes.Status429TooManyRequests;
                await context.Response.WriteAsync("Rate limit exceeded");
                return;
            }

            _requestCounts[clientIp] = (requestInfo.Timestamp, requestInfo.Count + 1);
        }
        else
        {
            _requestCounts[clientIp] = (now, 1);
        }

        await _next(context);
    }
}
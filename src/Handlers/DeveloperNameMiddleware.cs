using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

public class DeveloperNameMiddleware
{
    private readonly RequestDelegate _next;
    private const string DeveloperName = "<PERSON><PERSON>";
    public DeveloperNameMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        context.Response.OnStarting(() =>
        {
            context.Response.Headers.Add("X-Developed-By", DeveloperName);
            return Task.CompletedTask;
        });

        await _next(context);
    }
}

   using Microsoft.AspNetCore.Authentication;
   using Microsoft.Extensions.Logging;
   using Microsoft.Extensions.Options;
   using System.Security.Claims;
   using System.Text.Encodings.Web;
   using System.Threading.Tasks;

   public class CustomAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
   {
       public CustomAuthenticationHandler(
           IOptionsMonitor<AuthenticationSchemeOptions> options,
           ILoggerFactory logger,
           UrlEncoder encoder,
           ISystemClock clock)
           : base(options, logger, encoder, clock)
       {
       }

       protected override Task<AuthenticateResult> HandleAuthenticateAsync()
       {
           if (!Request.Headers.ContainsKey("Authentication-Key"))
           {
               return Task.FromResult(AuthenticateResult.Fail("Missing Authentication-Key Header"));
           }

           var authHeader = Request.Headers["Authentication-Key"].ToString();
           if (string.IsNullOrEmpty(authHeader) || authHeader != "beaconinternal")
           {
               return Task.FromResult(AuthenticateResult.Fail("Invalid Custom-Auth Header"));
           }

           var claims = new[] { new Claim(ClaimTypes.Name, "CustomUser") };
           var identity = new ClaimsIdentity(claims, Scheme.Name);
           var principal = new ClaimsPrincipal(identity);
           var ticket = new AuthenticationTicket(principal, Scheme.Name);

           return Task.FromResult(AuthenticateResult.Success(ticket));
       }
   }
   
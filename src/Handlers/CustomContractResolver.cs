using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Reflection;

public class CustomContractResolver : DefaultContractResolver
{
    protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
    {
        JsonProperty property = base.CreateProperty(member, memberSerialization);

        property.ShouldSerialize = instance =>
        {
            var value = property.ValueProvider.GetValue(instance);

            if (property.PropertyType == typeof(DateTime) || property.PropertyType == typeof(DateTime?))
            {
                DateTime? dateValue = (DateTime?)value;
                return dateValue.HasValue && dateValue.Value.Date != DateTime.MinValue.Date;
            }
            else if (property.PropertyType == typeof(int) || property.PropertyType == typeof(int?))
            {
                int? intValue = (int?)value;
                return intValue.HasValue && intValue.Value != default(int);
            }
            else if (property.PropertyType == typeof(string))
            {
                string stringValue = (string)value;
                return !string.IsNullOrEmpty(stringValue);
            }

            return value != null;
        };

        return property;
    }
}

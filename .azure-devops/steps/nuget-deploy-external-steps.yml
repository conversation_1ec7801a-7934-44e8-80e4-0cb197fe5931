parameters:
  - name: artifactsPath
    type: string 
    default: "."
  - name: nugetFile
    type: string 
    default: "*.nupkg"
  - name: nugetType
    type: string 
    default: "external"
  - name: nugetService
    type: string 
    default: ""
  - name: nugetFeeds
    type: string 
    default: "select"

steps:
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: 'current'
        downloadType: 'single'
        artifactName: 'drop'
        downloadPath: '${{ parameters.artifactsPath }}'

    - task: NuGetToolInstaller@1
      inputs:
        versionSpec: '*'
        checkLatest: true

    - task: NuGetCommand@2
      inputs:
        command: 'push'
        feedsToUse: '${{ parameters.nugetFeeds }}'
        nuGetFeedType: '${{ parameters.nugetType }}'
        includeNuGetOrg: true
        publishFeedCredentials: '${{ parameters.nugetService }}'
        packagesToPush: '${{ parameters.artifactsPath }}/**/${{ parameters.nugetFile }}'

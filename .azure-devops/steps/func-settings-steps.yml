parameters:
  - name: subscriptionService
    type: string 
    default: 'COMPANY-PRODUCT-001'
  - name: rgEnvironment
    type: string 
    default: 'Development'
  - name: funcName
    type: string 
    default: "func-PRODUCT-ENVIRONMENT-001"
  - name: appSettings
    type: object
    default: 
      - name: 'APPINSIGHTS_INSTRUMENTATIONKEY'
        value: '$(appiKey)'
        slotSetting: false
      - name: 'APPLICATIONINSIGHTS_CONNECTION_STRING'
        value: '$(appiConnection)'
        slotSetting: false        
      - name: 'AZURE_FUNCTIONS_ENVIRONMENT'
        value: '$(rgEnvironment)'
        slotSetting: false
      - name: 'FUNCTIONS_EXTENSION_VERSION'
        value: '~$(funcVersion)'
        slotSetting: false
      - name: 'FUNCTIONS_WORKER_RUNTIME'
        value: '$(funcRuntime)'
        slotSetting: false
      - name: '$(appcsEnvironmentVariable)'
        value: '$(appcsConnection)'
        slotSetting: false
      - name: '$(stEnvironmentVariable)'
        value: '$(stConnection)'
        slotSetting: false

steps:
  - ${{ each appSetting in parameters.appSettings }}:
    - task: AzureAppServiceSettings@1
      displayName: 'set_appsettings ${{ appSetting.name }}'
      inputs:
        azureSubscription: ${{ parameters.subscriptionService }}
        appName: ${{ parameters.funcName }}    
        appSettings: |
          [
            {
              "name": "${{ appSetting.name }}",
              "value": "${{ appSetting.value }}",
              "slotSetting": "${{ appSetting.slotSetting }}"
            }
          ]
        slotName: ${{ parameters.slotName }}
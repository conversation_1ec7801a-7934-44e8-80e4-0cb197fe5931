parameters:
  - name: srcPath
    type: string 
    default: "."
  - name: srcProject
    type: string 
    default: "*.csproj"
  - name: nugetPathFile
    type: string 
    default: ".nuget/NuGet.config"
  - name: buildConfiguration
    type: string 
    default: "Release"
  - name: scriptsPath
    type: string 
    default: "../scripts"
  - name: dotnetVersion
    type: string 
    default: "7.x"

steps:
  - task: UseDotNet@2
    displayName: 'use .net ${{ parameters.dotnetVersion }}'
    inputs:
      packageType: 'sdk'
      version: ${{ parameters.dotnetVersion }}
      includePreviewVersions: true   

  - task: DotNetCoreCLI@2
    displayName: 'dotnet restore'
    inputs:
      command: 'restore'
      projects: |
        ${{ parameters.srcPath }}/**/${{ parameters.srcProject }}
      feedsToUse: config
      nugetConfigPath: ${{ parameters.nugetPathFile }}

  - task: DotNetCoreCLI@2
    displayName: 'dotnet build'
    inputs:
      command: 'build'
      projects: |
        ${{ parameters.srcPath }}/**/${{ parameters.srcProject }}
      arguments: '--configuration ${{ parameters.buildConfiguration }}'

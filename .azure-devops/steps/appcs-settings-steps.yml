parameters:
  - name: armPath
    type: string 
    default: "."
  - name: subscriptionId
    type: string 
    default: "00000000-0000-0000-0000-000000000000"
  - name: subscriptionService
    type: string 
    default: 'COMPANY-PRODUCT-001'
  - name: rgName
    type: string 
    default: 'rg-PRODUCT-001'
  - name: rgLocation
    type: string 
    default: "West US 3"
  - name: appcsName
    type: string 
    default: "appcs-PRODUCT-ENVIRONMENT-001"
  - name: appcsKeys
    type: object
    default: [ "Shared:Sentinel", "Shared:Sentinel$Development" ]
  - name: appcsValues
    type: object
    default: [ "1", "1" ]

steps:
  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Validate ${{ parameters.appcsName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/appcs-appconfigurationsetting.json"        
        csmParametersFile: "${{ parameters.armPath }}/appcs-appconfigurationsetting.parameters.json"
        deploymentMode: "Validation"
        overrideParameters: -name "${{ parameters.appcsName }}" -appcsKeys ${{ convertToJson(parameters.appcsKeys) }} -appcsValues ${{ convertToJson(parameters.appcsValues) }}

  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Configure ${{ parameters.appcsName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/appcs-appconfigurationsetting.json"
        csmParametersFile: "${{ parameters.armPath }}/appcs-appconfigurationsetting.parameters.json"
        deploymentMode: "Incremental"
        overrideParameters: -name "${{ parameters.appcsName }}" -appcsKeys ${{ convertToJson(parameters.appcsKeys) }} -appcsValues ${{ convertToJson(parameters.appcsValues) }}

parameters:
  - name: subscriptionId
    type: string 
    default: "00000000-0000-0000-0000-000000000000"
  - name: subscriptionService
    type: string 
    default: 'COMPANY-PRODUCT-001'
  - name: rgName
    type: string 
    default: 'rg-PRODUCT-001'
  - name: rgLocation
    type: string 
    default: "West US 3"
  - name: logicPath
    type: string 
    default: "."
  - name: logicName
    type: string 
    default: "logic-PRODUCT-ENVIRONMENT-001"
  - name: logicParameters
    type: string 
    default: "logic-PRODUCT-ENVIRONMENT-001"

steps:
  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Validate ${{ parameters.logicName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.logicPath }}/LogicApp.json"
        csmParametersFile: "${{ parameters.logicPath }}/LogicApp.parameters.json"        
        deploymentMode: "Validation"
        overrideParameters: "${{ parameters.logicParameters }}"          

  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Deploy ${{ parameters.logicName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.logicPath }}/LogicApp.json"
        csmParametersFile: "${{ parameters.logicPath }}/LogicApp.parameters.json"
        deploymentMode: "Incremental"
        overrideParameters: "${{ parameters.logicParameters }}"
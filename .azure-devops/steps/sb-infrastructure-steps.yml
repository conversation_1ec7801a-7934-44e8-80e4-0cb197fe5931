parameters:
  - name: armPath
    type: string 
    default: "."
  - name: subscriptionId
    type: string 
    default: "00000000-0000-0000-0000-000000000000"
  - name: subscriptionService
    type: string 
    default: 'COMPANY-PRODUCT-001'
  - name: rgName
    type: string 
    default: 'rg-PRODUCT-001'
  - name: rgLocation
    type: string 
    default: "West US 3"
  - name: sbName
    type: string 
    default: "sb-PRODUCT-ENVIRONMENT-001"
  - name: sbSku
    type: string 
    default: "Basic"

steps:  
  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Validate ${{ parameters.sbName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/sb-servicebus.json"
        csmParametersFile: "${{ parameters.armPath }}/sb-servicebus.parameters.json"
        deploymentMode: "Validation"
        overrideParameters: -name "${{ parameters.sbName }}" -sku "${{ parameters.sbSku }}"

  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Deploy ${{ parameters.sbName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/sb-servicebus.json"
        csmParametersFile: "${{ parameters.armPath }}/sb-servicebus.parameters.json"
        deploymentMode: "Incremental"
        overrideParameters: -name "${{ parameters.sbName }}" -sku "${{ parameters.sbSku }}"
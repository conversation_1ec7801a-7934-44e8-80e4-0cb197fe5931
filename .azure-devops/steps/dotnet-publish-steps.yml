parameters:
  - name: artifactsPath
    type: string 
    default: "."
  - name: artifactName
    type: string 
    default: "drop"
  - name: nugetPathFile
    type: string 
    default: ".nuget/NuGet.config"
  - name: restorePath
    type: string 
    default: "."
  - name: restoreProject
    type: string 
    default: "*.csproj"
  - name: srcPath
    type: string 
    default: "."
  - name: srcProject
    type: string 
    default: "*.csproj"
  - name: scriptsPath
    type: string 
    default: "../scripts"
  - name: versionToReplace
    type: string 
    default: "1.0.0"
  - name: dotnetVersion
    type: string 
    default: "7.x"


steps:
  - task: UseDotNet@2
    displayName: 'use .net ${{ parameters.dotnetVersion }}'
    inputs:
      packageType: 'sdk'
      version: ${{ parameters.dotnetVersion }}
      includePreviewVersions: true  
      
  - task: PowerShell@2
    displayName: 'Set-Version.ps1'
    inputs:
      filePath: '${{ parameters.scriptsPath }}/Set-Version.ps1'
      arguments: '-Path ${{ parameters.srcPath }} -VersionToReplace ${{ parameters.versionToReplace }}'
      workingDirectory: '${{ parameters.scriptsPath }}'

  - task: DotNetCoreCLI@2
    displayName: 'dotnet restore'
    inputs:
      command: 'restore'
      projects: |
        ${{ parameters.restorePath }}/**/${{ parameters.restoreProject }}
      feedsToUse: config
      nugetConfigPath: ${{ parameters.nugetPathFile }}

  - task: DotNetCoreCLI@2
    displayName: 'dotnet publish'
    inputs:
      command: publish
      publishWebProjects: false
      projects: |
        ${{ parameters.srcPath }}/**/${{ parameters.srcProject }}
      arguments: '--output ${{ parameters.artifactsPath }}'

  - task: PublishBuildArtifacts@1
    displayName: 'Publish Artifact: drop'
    inputs:
      pathToPublish: '${{ parameters.artifactsPath }}'
      artifactName: '${{ parameters.artifactName }}'
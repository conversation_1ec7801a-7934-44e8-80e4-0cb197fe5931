parameters:
  - name: armPath
    type: string 
    default: "."
  - name: subscriptionId
    type: string 
    default: "00000000-0000-0000-0000-000000000000"
  - name: subscriptionService
    type: string 
    default: 'COMPANY-PRODUCT-001'
  - name: rgName
    type: string 
    default: 'rg-PRODUCT-001'
  - name: rgLocation
    type: string 
    default: "West US 3"
  - name: rgEnvironment
    type: string 
    default: 'Development'
  - name: planName
    type: string 
    default: "plan-api-PRODUCT-ENVIRONMENT-001"
  - name: planResourceGroupName
    type: string 
    default: "plan-api-PRODUCT-ENVIRONMENT-001"
  - name: planSku
    type: string
    default: "F1"
  - name: planCapacity
    type: string
    default: "0"
  - name: apiName
    type: string 
    default: "api-PRODUCT-ENVIRONMENT-001"

steps:  
  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Validate ${{ parameters.planName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.planResourceGroupName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/plan-appplan.json"        
        csmParametersFile: "${{ parameters.armPath }}/plan-appplan.parameters.json"        
        deploymentMode: "Validation"
        overrideParameters: -name "${{ parameters.planName }}" -sku "${{ parameters.planSku }}" -location "${{ parameters.rgLocation }}" -skuCapacity "${{ parameters.planCapacity }}"

  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Deploy ${{ parameters.planName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.planResourceGroupName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/plan-appplan.json"
        csmParametersFile: "${{ parameters.armPath }}/plan-appplan.parameters.json"
        deploymentMode: "Incremental"
        overrideParameters: -name "${{ parameters.planName }}" -sku "${{ parameters.planSku }}" -location "${{ parameters.rgLocation }}" -skuCapacity "${{ parameters.planCapacity }}"

  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Validate ${{ parameters.apiName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/api-apiapp.json"        
        csmParametersFile: "${{ parameters.armPath }}/api-apiapp.parameters.json"        
        deploymentMode: "Validation"
        overrideParameters: -name "${{ parameters.apiName }}" -location "${{ parameters.rgLocation }}" -planName "${{ parameters.planName }}" 

  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Deploy ${{ parameters.apiName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/api-apiapp.json"
        csmParametersFile: "${{ parameters.armPath }}/api-apiapp.parameters.json"
        deploymentMode: "Incremental"
        overrideParameters: -name "${{ parameters.apiName }}" -location "${{ parameters.rgLocation }}" -planName "${{ parameters.planName }}" 
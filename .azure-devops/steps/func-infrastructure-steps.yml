parameters:
  - name: armPath
    type: string 
    default: "."
  - name: subscriptionId
    type: string 
    default: "00000000-0000-0000-0000-000000000000"
  - name: subscriptionService
    type: string 
    default: 'COMPANY-PRODUCT-001'
  - name: rgName
    type: string 
    default: 'rg-PRODUCT-001'
  - name: rgLocation
    type: string 
    default: "West US 3"
  - name: rgEnvironment
    type: string 
    default: 'Development'
  - name: appiKey
    type: string 
  - name: appiConnection
    type: string 
  - name: funcName
    type: string 
    default: "func-PRODUCT-ENVIRONMENT-001"
  - name: funcVersion
    type: "number"
    default: 4
  - name: funcRuntime
    type: string 
    default: "dotnet-isolated"
  - name: planName
    type: string 
    default: "plan-func-PRODUCT-ENVIRONMENT-001"
  - name: planSku
    type: string
  - name: planCapacity
    type: string
    default: "1"
  - name: stName
    type: string 
    default: "stPRODUCTENVIRONMENT001"

steps:  
  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Validate ${{ parameters.planName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/plan-appplan.json"        
        csmParametersFile: "${{ parameters.armPath }}/plan-appplan.parameters.json"        
        deploymentMode: "Validation"
        overrideParameters: -name "${{ parameters.planName }}" -sku "${{ parameters.planSku }}" -location "${{ parameters.rgLocation }}" -skuCapacity "${{ parameters.planCapacity }}"

  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Deploy ${{ parameters.planName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/plan-appplan.json"
        csmParametersFile: "${{ parameters.armPath }}/plan-appplan.parameters.json"
        deploymentMode: "Incremental"
        overrideParameters: -name "${{ parameters.planName }}" -sku "${{ parameters.planSku }}" -location "${{ parameters.rgLocation }}" -skuCapacity "${{ parameters.planCapacity }}"

  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Validate ${{ parameters.funcName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/func-functionsapp.json"        
        csmParametersFile: "${{ parameters.armPath }}/func-functionsapp.parameters.json"        
        deploymentMode: "Validation"
        overrideParameters: -name "${{ parameters.funcName }}" -location "${{ parameters.rgLocation }}" -funcVersion "${{ parameters.funcVersion }}" -stName "${{ parameters.stName }}" -appiKey "${{ parameters.appiKey }}" -appiConnection "${{ parameters.appiConnection }}" -funcRuntime "${{ parameters.funcRuntime }}" -rgEnvironment "${{ parameters.rgEnvironment }}"

  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Deploy ${{ parameters.funcName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/func-functionsapp.json"
        csmParametersFile: "${{ parameters.armPath }}/func-functionsapp.parameters.json"
        deploymentMode: "Incremental"
        overrideParameters: -name "${{ parameters.funcName }}" -location "${{ parameters.rgLocation }}" -funcVersion "${{ parameters.funcVersion }}" -stName "${{ parameters.stName }}" -appiKey "${{ parameters.appiKey }}" -appiConnection "${{ parameters.appiConnection }}" -funcRuntime "${{ parameters.funcRuntime }}" -rgEnvironment "${{ parameters.rgEnvironment }}"

parameters:
  - name: armPath
    type: string 
    default: "."
  - name: subscriptionId
    type: string 
    default: "00000000-0000-0000-0000-000000000000"
  - name: subscriptionService
    type: string 
    default: 'COMPANY-PRODUCT-001'
  - name: rgName
    type: string 
    default: 'rg-PRODUCT-001'
  - name: rgLocation
    type: string 
    default: "West US 3"
  - name: rgEnvironment
    type: string 
    default: 'Development'
  - name: stappName
    type: string 
    default: "stapp-PRODUCT-ENVIRONMENT-001"
  - name: stappSku
    type: string 
    default: "Free"
  - name: repositoryUrl
    type: string 
    default: "https://github.com/MYORG/MYREPO"
  - name: branch
    type: string 
    default: "main"
  - name: repositoryToken
    type: string
    default: "GITHUB_PERSONAL_ACCESS_TOKEN"
  - name: appLocation
    type: string 
    default: "/src"
  - name: apiLocation
    type: string 
    default: "/api"
  - name: appArtifactLocation
    type: string 
    default: "build"

steps:  
  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Deploy ${{ parameters.stappName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/stapp-staticwebapp.json"
        csmParametersFile: "${{ parameters.armPath }}/stapp-staticwebapp.parameters.json"
        deploymentMode: "Incremental"
        overrideParameters: -name "${{ parameters.stappName }}" -location "${{ parameters.rgLocation }}"  -sku "${{ parameters.stappSku }}"  -repositoryUrl "${{ parameters.repositoryUrl }}"  -branch "${{ parameters.branch }}"  -repositoryToken "${{ parameters.repositoryToken }}"  -appLocation "${{ parameters.appLocation }}"  -apiLocation "${{ parameters.apiLocation }}"  -appArtifactLocation "${{ parameters.appArtifactLocation }}"
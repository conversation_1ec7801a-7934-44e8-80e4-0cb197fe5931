parameters:
- name: armPath
  type: string 
  default: "."
- name: subscriptionId
  type: string 
  default: "00000000-0000-0000-0000-000000000000"
- name: subscriptionService
  type: string 
  default: 'COMPANY-PRODUCT-001'
- name: rgName
  type: string 
  default: 'rg-PRODUCT-001'
- name: rgLocation
  type: string 
  default: "West US 3"
- name: appcsName
  type: string 
  default: "appcs-PRODUCT-ENVIRONMENT-001"
- name: appcsSku
  type: string 
  default: "free" #standard

steps:
  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Validate ${{ parameters.appcsName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/appcs-appconfigurationstore.json"        
        csmParametersFile: "${{ parameters.armPath }}/appcs-appconfigurationstore.parameters.json"        
        deploymentMode: "Validation"
        overrideParameters: -name "${{ parameters.appcsName }}" -sku "${{ parameters.appcsSku }}"

  - task: AzureResourceManagerTemplateDeployment@3
    displayName: "Deploy ${{ parameters.appcsName }}"
    inputs:
        deploymentScope: "Resource Group"
        azureResourceManagerConnection: "${{ parameters.subscriptionService }}"
        subscriptionId: "${{ parameters.subscriptionId }}"
        action: "Create Or Update Resource Group"
        resourceGroupName: "${{ parameters.rgName }}"
        location: "${{ parameters.rgLocation }}"
        templateLocation: "Linked artifact"
        csmFile: "${{ parameters.armPath }}/appcs-appconfigurationstore.json"
        csmParametersFile: "${{ parameters.armPath }}/appcs-appconfigurationstore.parameters.json"
        deploymentMode: "Incremental"
        overrideParameters: -name "${{ parameters.appcsName }}" -sku "${{ parameters.appcsSku }}"
